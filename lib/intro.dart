import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:permission_handler/permission_handler.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class IntroPage extends StatefulWidget {
  const IntroPage({Key? key}) : super(key: key);

  @override
  State<IntroPage> createState() => _IntroPageState();
}

class _IntroPageState extends State<IntroPage> {
  final controller = PageController();
  final centerCtl = Get.put(SettingController());

  RxInt page = 0.obs;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    super.initState();
    _requestCameraPermission();
    analytics.setCurrentScreen(screenName: "Intro");
    controller.addListener(() {
      page.value = controller.page!.round();
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    controller.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    List<Widget> textContent = <Widget>[
      //tab1
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
              context,
              "ยินดีต้อนรับ",
              18,
              const Color(0xFF282828),
              FontWeight.w400),
          const SizedBox(
            height: 16,
          ),
          AppWidget.boldText(
              context,
              "PRACHAKIJ MOBILE",
              14,
              const Color(0xFFFFB100),
              FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          AppWidget.normalText(
              context,
              "ตอบโจทย์ทุกงานบริการของเรา",
              12,
              const Color(0xFF707070),
              FontWeight.w300),
          const SizedBox(
            height: 10,
          ),
          AppWidget.normalText(
              context,
              "บนสมาร์ทโฟนของคุณ",
              12,
              const Color(0xFF707070),
              FontWeight.w300),
        ],
      ),
      //tab1
      //tab2
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
              context,
              "ใช้ง่าย สะดวกสบาย",
              18,
              const Color(0xFF282828),
              FontWeight.w400),
          const SizedBox(
            height: 16,
          ),
          AppWidget.normalText(
              context,
              "กับงานบริการมากมาย ที่คุณสามารถ",
              14,
              const Color(0xFF707070),
              FontWeight.w300),
          const SizedBox(
            height: 10,
          ),
          AppWidget.normalText(
              context,
              "ใช้งานได้ ผ่านเมนูต่างๆ",
              12,
              const Color(0xFF707070),
              FontWeight.w300),
          const SizedBox(
            height: 10,
          ),
          AppWidget.boldText(
              context,
              "",
              12,
              const Color(0xFFFFB100),
              FontWeight.normal),
        ],
      ),
      //tab2
      //tab3
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.normalText(
              context,
              "ใกล้ชิด เรื่องงานบริการ",
              18,
              const Color(0xFF282828),
              FontWeight.w400),
          const SizedBox(
            height: 16,
          ),
          AppWidget.normalText(
              context,
              "ส่งมอบทุกความประทับใจแก่ลูกค้าเสมอ",
              14,
              const Color(0xFF707070),
              FontWeight.w300),
          const SizedBox(
            height:10,
          ),
          AppWidget.normalText(
              context,
              "พร้อมรับใช้คุณลูกค้าทุกท่าน ทุกช่องทาง",
              12,
              const Color(0xFF707070),
              FontWeight.w300),
          const SizedBox(
            height: 10,
          ),
          AppWidget.boldText(
              context,
              "",
              12,
              const Color(0xFFFFB100),
              FontWeight.normal),
        ],
      ),
      //tab3
    ];

    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 0.5,1.0],
                  colors: [
                    Color(0xFFFFC700),
                    Color(0xFFFFB100),
                    Color(0xFFFF9900),
                  ],
                )
            ),
          ),
          Container(
            padding: const EdgeInsets.only(bottom: 300),
            child: Obx(() => centerCtl.os.value == "android"
            ? PageView(
              controller: controller,
              children: [
                buildPic(
                    'assets/image/intro/intro_android1.png',
                    Get.width
                ),
                buildPic(
                    'assets/image/intro/intro_android2.png',
                    Get.width
                ),
                buildPic(
                    'assets/image/intro/intro3.png',
                    207.0
                ),
              ],
            )
            : PageView(
              controller: controller,
              children: [
                buildPic(
                    'assets/image/intro/intro_ios1.png',
                    Get.width
                ),
                buildPic(
                    'assets/image/intro/intro_ios2.png',
                    Get.width
                ),
                buildPic(
                    'assets/image/intro/intro3.png',
                    207.0
                ),
              ],
            ),
            )
          ),
          Obx(() => buildIntroContainer(textContent[page.value]),)
        ],
      ),
    );
  }

  buildPic(String pic, size){
    return Obx(() => page.value == 2
    ? Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(pic, width: size,),
      ],
    )
    : Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(pic, width: size,),
      ],
    )
    );
  }

  buildIntroContainer(text){
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: Get.width,
        height: 325,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [
              Color(0xFFFFFFFF),
              Color(0xFFFFFFFF),
              Color(0xFFECECEC)
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF664701).withOpacity(1),
              offset: const Offset(0, -2), // changes position of shadow
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 30,
            ),
            Container(
              width: Get.width,
              margin: const EdgeInsets.only(
                left: 20,
                right: 20,
              ),
              child: text,
            ),
            const Spacer(),
            Obx(() => page.value == 2
            ? Center(
              child: InkWell(
                onTap: () async {
                  await AppService.setPref('bool', 'firstUse', false);
                  await AppService.setPref('bool', 'loginStatus', false);
                  Get.offAll(() => const HomeNavigator());
                },
                child: Container(
                  padding: const EdgeInsets.only(
                    top: 8,
                    bottom: 8,
                    right: 60,
                    left: 60,
                  ),
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(10),
                        topLeft: Radius.circular(10),
                        bottomLeft: Radius.circular(10),
                        bottomRight: Radius.circular(10),
                      ),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000).withOpacity(0.1),
                        offset: const Offset(1, 2),
                        blurRadius: 3.0,
                      ),
                    ],
                  ),
                  child: AppWidget.boldText(
                      context,
                      "เข้าใช้งาน",
                      15,
                      const Color(0xFF000000),
                      FontWeight.normal),
                ),
              ),
            )
            : Center(
              child: InkWell(
                onTap: (){
                  controller.animateToPage(3, duration: const Duration(milliseconds: 500), curve: Curves.easeInSine);
                },
                child: Container(
                  padding: const EdgeInsets.only(
                    top: 3,
                    bottom: 3,
                    right: 18,
                    left: 18,
                  ),
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                      ),
                      border: Border.all(
                        width: 1,
                        color: const Color(0xFF707070).withOpacity(0.4),
                      )
                  ),
                  child: AppWidget.normalText(
                      context,
                      "ข้าม",
                      15,
                      const Color(0xFF000000),
                      FontWeight.normal),
                ),
              ),
            ),
            ),
            const SizedBox(
              height: 40,
            ),
            Center(
              child: SmoothPageIndicator(
                  controller: controller,
                  count:  3,
                effect: ExpandingDotsEffect(
                  dotColor: const Color(0xFFFFB100).withOpacity(0.4),
                  activeDotColor: const Color(0xFFFFB100),
                  dotHeight: 6,
                  dotWidth: 6,
                ),
              ),
            ),
            const SizedBox(
              height: 40,
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _requestCameraPermission() async {
    var status = await Permission.camera.status;
    if (!status.isGranted) {
      status = await Permission.camera.request();
    }
    return status.isGranted;
  }

}

class AppPic {
  static const pic = [
    "assets/image/intro/intro1.png",
    "assets/image/intro/intro2.png",
    "assets/image/intro/intro3.png",
  ];
}
