// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBvdkGO4cJPznK4leGfDa3oPAshMhCJXPM',
    appId: '1:840143237001:web:0c908841594a0ebc7d7b58',
    messagingSenderId: '840143237001',
    projectId: 'mapp-pms',
    authDomain: 'mapp-pms.firebaseapp.com',
    databaseURL: 'https://mapp-pms.firebaseio.com',
    storageBucket: 'mapp-pms.appspot.com',
    measurementId: 'G-N4XVGX33TL',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBvdkGO4cJPznK4leGfDa3oPAshMhCJXPM',
    appId: '1:840143237001:android:019430ad09e24a647d7b58',
    messagingSenderId: '840143237001',
    projectId: 'mapp-pms',
    databaseURL: 'https://mapp-pms.firebaseio.com',
    storageBucket: 'mapp-pms.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBYmsclT9GJNIfQec5iCuIygC43lKIKon4',
    appId: '1:840143237001:ios:4d5c2bd7d4280e747d7b58',
    messagingSenderId: '840143237001',
    projectId: 'mapp-pms',
    databaseURL: 'https://mapp-pms.firebaseio.com',
    storageBucket: 'mapp-pms.appspot.com',
    androidClientId: '840143237001-15k45mbmqujtmmmt8ckkjnkshh1or87s.apps.googleusercontent.com',
    iosClientId: '840143237001-7oepdujcbq3lgjce52703gvbpjrgkav0.apps.googleusercontent.com',
    iosBundleId: 'com.prachakij.pms',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBYmsclT9GJNIfQec5iCuIygC43lKIKon4',
    appId: '1:840143237001:ios:4d5c2bd7d4280e747d7b58',
    messagingSenderId: '840143237001',
    projectId: 'mapp-pms',
    databaseURL: 'https://mapp-pms.firebaseio.com',
    storageBucket: 'mapp-pms.appspot.com',
    androidClientId: '840143237001-15k45mbmqujtmmmt8ckkjnkshh1or87s.apps.googleusercontent.com',
    iosClientId: '840143237001-7oepdujcbq3lgjce52703gvbpjrgkav0.apps.googleusercontent.com',
    iosBundleId: 'com.prachakij.pms',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBvdkGO4cJPznK4leGfDa3oPAshMhCJXPM',
    appId: '1:840143237001:web:45b374bfc12a73f27d7b58',
    messagingSenderId: '840143237001',
    projectId: 'mapp-pms',
    authDomain: 'mapp-pms.firebaseapp.com',
    databaseURL: 'https://mapp-pms.firebaseio.com',
    storageBucket: 'mapp-pms.appspot.com',
    measurementId: 'G-PLVN4LGDZC',
  );

}