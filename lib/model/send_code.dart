

class SendCode {
  SendCode({
    required this.from,
    required this.phone,
    required this.typeSMS,
  });

  String from;
  String phone;
  String typeSMS;

  factory SendCode.fromJson(Map<String, dynamic> json) => SendCode(
    from: json["from"],
    phone: json["phone"],
    typeSMS: json["typeSMS"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone,
    "from": from,
    "typeSMS": typeSMS,
  };
}

class ResponseSendCode {
  ResponseSendCode({
    this.success,
    this.statusCode,
    this.otp,
    this.refCode,
    this.result,
  });

  bool? success;
  int? statusCode;
  int? otp;
  String? refCode;
  bool? result;

  factory ResponseSendCode.fromJson(Map<String, dynamic> json) => ResponseSendCode(
    success: json["success"],
    statusCode: json["statusCode"],
    otp: json["otp"],
    refCode: json["refCode"],
    result: json["result"],
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "statusCode": statusCode,
    "otp": otp,
    "refCode": refCode,
    "result": result,
  };
}

class ResponseSendOtp {
  ResponseSendOtpResult? result;

  ResponseSendOtp({
    this.result,
  });

  factory ResponseSendOtp.fromJson(Map<String, dynamic> json) => ResponseSendOtp(
    result: ResponseSendOtpResult.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() => {
    "result": result!.toJson(),
  };
}

class ResponseSendOtpResult {
  String? code;
  String? detail;
  ResultResult? result;

  ResponseSendOtpResult({
    this.code,
    this.detail,
    this.result,
  });

  factory ResponseSendOtpResult.fromJson(Map<String, dynamic> json) => ResponseSendOtpResult(
    code: json["code"] ?? "",
    detail: json["detail"] ?? "",
    result: ResultResult.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() => {
    "code": code,
    "detail": detail,
    "result": result!.toJson(),
  };
}

class ResultResult {
  String? token;
  String? refCode;

  ResultResult({
    this.token,
    this.refCode,
  });

  factory ResultResult.fromJson(Map<String, dynamic> json) => ResultResult(
    token: json["token"] ?? "",
    refCode: json["ref_code"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "token": token,
    "ref_code": refCode,
  };
}
