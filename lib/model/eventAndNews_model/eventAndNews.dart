class ResponseEvent {
  ResponseEvent({
    this.idActivity,
    this.nameActivity,
    this.informationId,
    this.rateActivity,
    this.mapActivity,
    this.lpActivity,
    this.qrActivity,
    this.fdateRegisActivity,
    this.edateRegisActivity,
    this.fdateActivity,
    this.edateActivity,
    this.informationName,
    this.informationBody,
    this.informationPic,
  });

  String? idActivity;
  String? nameActivity;
  String? informationId;
  String? rateActivity;
  String? mapActivity;
  String? lpActivity;
  String? qrActivity;
  String? fdateRegisActivity;
  String? edateRegisActivity;
  String? fdateActivity;
  String? edateActivity;
  String? informationName;
  String? informationBody;
  String? informationPic;

  factory ResponseEvent.fromJson(Map<String, dynamic> json) => ResponseEvent(
    idActivity: json["id_activity"],
    nameActivity: json["name_activity"],
    informationId: json["information_id"],
    rateActivity: json["rate_activity"],
    mapActivity: json["map_activity"],
    lpActivity: json["LP_activity"],
    qrActivity: json["QR_activity"],
    fdateRegisActivity: json["fdate_regis_activity"],
    edateRegisActivity: json["edate_regis_activity"],
    fdateActivity: json["fdate_activity"],
    edateActivity: json["edate_activity"],
    informationName: json["information_name"],
    informationBody: json["information_body"],
    informationPic: json["information_pic"],
  );

  Map<String, dynamic> toJson() => {
    "id_activity": idActivity,
    "name_activity": nameActivity,
    "information_id": informationId,
    "rate_activity": rateActivity,
    "map_activity": mapActivity,
    "LP_activity": lpActivity,
    "QR_activity": qrActivity,
    "fdate_regis_activity": fdateRegisActivity,
    "edate_regis_activity": edateRegisActivity,
    "fdate_activity": fdateActivity,
    "edate_activity": edateActivity,
    "information_name": informationName,
    "information_body": informationBody,
    "information_pic": informationPic,
  };
}

class EventList {
  final List<ResponseEvent>? data;

  EventList({this.data});

  factory EventList.fromJson(List<dynamic> parsedJson){
    List<ResponseEvent>? data = <ResponseEvent>[];
    data = parsedJson.map((i) => ResponseEvent.fromJson(i)).toList();
    return EventList(data: data);
  }
}