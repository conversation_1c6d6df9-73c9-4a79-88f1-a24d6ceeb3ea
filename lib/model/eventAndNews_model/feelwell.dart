class ResponseFeelWell {
  ResponseFeelWell({
    this.running,
    this.updateTime,
    this.updateUser,
    this.createTime,
    this.createUser,
    this.rowSpreadsheet,
    this.name,
    this.status,
    this.typeFeelWellTv,
    this.primaryImg,
    this.linkVideo,
  });

  int? running;
  String? updateTime;
  String? updateUser;
  String? createTime;
  String? createUser;
  String? rowSpreadsheet;
  String? name;
  String? status;
  String? typeFeelWellTv;
  String? primaryImg;
  String? linkVideo;

  factory ResponseFeelWell.fromJson(Map<String, dynamic> json) => ResponseFeelWell(
    running: json["running"] == null ? null : json["running"],
    updateTime: json["update_time"] == null ? null : json["update_time"],
    updateUser: json["update_user"] == null ? null : json["update_user"],
    createTime: json["create_time"] == null ? null : json["create_time"],
    createUser: json["create_user"] == null ? null : json["create_user"],
    rowSpreadsheet: json["row_spreadsheet"] == null ? null : json["row_spreadsheet"],
    name: json["name"] == null ? null : json["name"],
    status: json["status"] == null ? null : json["status"],
    typeFeelWellTv: json["type_feelwelltv"] == null ? null : json["type_feelwelltv"],
    primaryImg: json["primary_img"] == null ? null : json["primary_img"],
    linkVideo: json["link_video"] == null ? null : json["link_video"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "update_time": updateTime,
    "update_user": updateUser,
    "create_time": createTime,
    "create_user": createUser,
    "row_spreadsheet": rowSpreadsheet,
    "name": name,
    "status": status,
    "type_feelwelltv": typeFeelWellTv,
    "primary_img": primaryImg,
    "link_video": linkVideo,
  };
}

class FeelWellList {
  final List<ResponseFeelWell>? data;

  FeelWellList({this.data});

  factory FeelWellList.fromJson(List<dynamic> parsedJson){
    List<ResponseFeelWell>? data = <ResponseFeelWell>[];
    data = parsedJson.map((i) => ResponseFeelWell.fromJson(i)).toList();
    return FeelWellList(data: data);
  }
}