class ResponseNews {
  int? informationId;
  String? informationName;
  String? informationBody;
  String? informationPic;
  String? informationDate;
  String? informationLink;
  String? type;

  ResponseNews({
    this.informationId,
    this.informationName,
    this.informationBody,
    this.informationPic,
    this.informationDate,
    this.informationLink,
    this.type,
  });

  factory ResponseNews.fromJson(Map<String, dynamic> json) => ResponseNews(
    informationId: json["information_id"] ?? "",
    informationName: json["information_name"] ?? "",
    informationBody: json["information_body"] ?? "",
    informationPic: json["information_pic"] ?? "",
    informationDate: json["information_date"] ?? "",
    informationLink: json["information_link"] ?? "",
    type: json["type"] ?? "information",
  );

  Map<String, dynamic> toJson() => {
    "information_id": informationId,
    "information_name": informationName,
    "information_body": informationBody,
    "information_pic": informationPic,
    "information_date": informationDate,
    "information_link": informationLink,
    "type": type,
  };
}

class NewsList {
  final List<ResponseNews>? data;

  NewsList({this.data});

  factory NewsList.fromJson(List<dynamic> parsedJson){
    List<ResponseNews>? data = <ResponseNews>[];
    data = parsedJson.map((i) => ResponseNews.fromJson(i)).toList();
    return NewsList(data: data);
  }
}