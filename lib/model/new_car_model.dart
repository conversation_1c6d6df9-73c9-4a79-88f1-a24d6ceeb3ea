import 'dart:convert';

NewCar newCarFromJson(String str) => NewCar.fromJson(json.decode(str));

String newCarToJson(NewCar data) => json.encode(data.toJson());

class NewCar {
  List<NewCarList>? newCarList;

  NewCar({
    this.newCarList,
  });

  factory NewCar.fromJson(Map<String, dynamic> json) => NewCar(
    newCarList: List<NewCarList>.from(json["newCarList"].map((x) => NewCarList.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "newCarList": List<dynamic>.from(newCarList!.map((x) => x.toJson())),
  };
}

class NewCarList {
  String? statusApp;
  int? bookingNo;
  String? car;
  String? carType;
  String? colorCode;
  String? color;
  String? bookingStatus;
  String? finance;
  String? statusMatchBooking;
  String? advisor;
  String? advisorNickname;
  String? advisorPic;
  String? advisorPhone;
  String? carImage;

  NewCarList({
    this.statusApp,
    this.bookingNo,
    this.car,
    this.carType,
    this.colorCode,
    this.color,
    this.bookingStatus,
    this.finance,
    this.statusMatchBooking,
    this.advisor,
    this.advisorNickname,
    this.advisorPic,
    this.advisorPhone,
    this.carImage,
  });

  factory NewCarList.fromJson(Map<String, dynamic> json) => NewCarList(
    statusApp: json["statusApp"] ?? "",
    bookingNo: json["bookingNo"] ?? "",
    car: json["car"] ?? "",
    carType: json["car_type"] ?? "",
    colorCode: json["color_Code"] ?? "",
    color: json["color"] ?? "",
    bookingStatus: json["bookingStatus"] ?? "",
    finance: json["finance"] ?? "",
    statusMatchBooking: json["status_match_booking"] ?? "",
    advisor: json["advisor"] ?? "",
    advisorNickname: json["advisorNickname"] ?? "",
    advisorPic: json["advisorPic"] ?? "",
    advisorPhone: json["advisorPhone"] ?? "",
    carImage: json["car_image"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "statusApp": statusApp,
    "bookingNo": bookingNo,
    "car": car,
    "car_type": carType,
    "color_Code": colorCode,
    "color": color,
    "bookingStatus": bookingStatus,
    "finance": finance,
    "status_match_booking": statusMatchBooking,
    "advisor": advisor,
    "advisorNickname": advisorNickname,
    "advisorPic": advisorPic,
    "advisorPhone": advisorPhone,
    "car_image": carImage,
  };
}