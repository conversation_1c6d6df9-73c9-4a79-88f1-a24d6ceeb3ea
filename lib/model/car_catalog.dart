class ResponseCarCatalog {
  ResponseCarCatalog({
    this.running,
    this.createTime,
    this.createUser,
    this.updateTime,
    this.updateUser,
    this.catalogTitle,
    this.catalogText,
    this.catalogImage,
    this.catalogUrl,
    this.catalogOrder,
  });

  int? running;
  String? createTime;
  String? createUser;
  String? updateTime;
  String? updateUser;
  String? catalogTitle;
  String? catalogText;
  String? catalogImage;
  String? catalogUrl;
  int? catalogOrder;

  factory ResponseCarCatalog.fromJson(Map<String, dynamic> json) => ResponseCarCatalog(
    running: json["running"],
    createTime: json["create_time"],
    createUser: json["create_user"],
    updateTime: json["update_time"],
    updateUser: json["update_user"],
    catalogTitle: json["catalog_title"],
    catalogText: json["catalog_text"],
    catalogImage: json["catalog_image"],
    catalogUrl: json["catalog_url"],
    catalogOrder: json["catalog_order"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "create_time": createTime,
    "create_user": createUser,
    "update_time": updateTime,
    "update_user": updateUser,
    "catalog_title": catalogTitle,
    "catalog_text": catalogText,
    "catalog_image": catalogImage,
    "catalog_url": catalogUrl,
    "catalog_order": catalogOrder,
  };
}

class CarCatalogList {
  final List<ResponseCarCatalog>? data;

  CarCatalogList({this.data});

  factory CarCatalogList.fromJson(List<dynamic> parsedJson){
    List<ResponseCarCatalog>? data = <ResponseCarCatalog>[];
    data = parsedJson.map((i) => ResponseCarCatalog.fromJson(i)).toList();
    return CarCatalogList(data: data);
  }
}