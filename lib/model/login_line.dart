class LoginLine {
  LoginLine({
    required this.lineID,
  });

  String lineID;

  factory LoginLine.fromJson(Map<String, dynamic> json) => LoginLine(
    lineID: json["lineID"] == null ? null : json["lineID"],
  );

  Map<String, dynamic> toJson() => {
    "lineID": lineID == null ? null : lineID,
  };
}

class ResponseLoginLine {
  ResponseLoginLine({
    this.status,
    this.userIdLine,
    this.message,
  });

  int? status;
  String? userIdLine;
  String? message;

  factory ResponseLoginLine.fromJson(Map<String, dynamic> json) => ResponseLoginLine(
    status: json["status"],
    userIdLine: json["userID_line"],
    message: json["message"],
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "userID_line": userIdLine,
    "message": message,
  };
}

class Line {
  Line({
    this.displayName,
    this.pictureUrl,
    this.statusMessage,
    this.userId,
  });

  String? displayName;
  String? pictureUrl;
  String? statusMessage;
  String? userId;

  factory Line.fromJson(Map<String, dynamic> json) => Line(
    displayName: json["displayName"],
    pictureUrl: json["pictureUrl"],
    statusMessage: json["statusMessage"],
    userId: json["userId"],
  );

  Map<String, dynamic> toJson() => {
    "displayName": displayName,
    "pictureUrl": pictureUrl,
    "statusMessage": statusMessage,
    "userId": userId,
  };
}