class PhoneCenter {
  PhoneCenter({
    required this.menu,
  });

  String menu;

  factory PhoneCenter.fromJson(Map<String, dynamic> json) => PhoneCenter(
    menu: json["menu"] == null ? null : json["menu"],
  );

  Map<String, dynamic> toJson() => {
    "menu": menu == null ? null : menu,
  };
}

class ResponsePhoneCenter {
  ResponsePhoneCenter({
    this.id,
    this.subject,
    this.phone,
    this.phoneNumber,
    this.name,
    this.flag,
  });

  int? id;
  String? subject;
  String? phone;
  String? phoneNumber;
  String? name;
  int? flag;

  factory ResponsePhoneCenter.fromJson(Map<String, dynamic> json) => ResponsePhoneCenter(
    id: json["result"][0]["id"] == null ? null : json["result"][0]["id"],
    subject: json["result"][0]["subject"] == null ? null : json["result"][0]["subject"],
    phone: json["result"][0]["phone"] == null ? null : json["result"][0]["phone"],
    phoneNumber: json["result"][0]["phone_number"] == null ? null : json["result"][0]["phone_number"],
    name: json["result"][0]["name"] == null ? null : json["result"][0]["name"],
    flag: json["result"][0]["flag"] == null ? null : json["result"][0]["flag"],
  );

  Map<String, dynamic> toJson() => {
    "id": id == null ? null : id,
    "subject": subject == null ? null : subject,
    "phone": phone == null ? null : phone,
    "phone_number": phoneNumber == null ? null : phoneNumber,
    "name": name == null ? null : name,
    "flag": flag == null ? null : flag,
  };
}
