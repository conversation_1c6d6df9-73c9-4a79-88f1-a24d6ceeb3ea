class Member {
  Member({
    required this.id,
  });

  String id;

  factory Member.fromJson(Map<String, dynamic> json) => Member(
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
  };
}

class ResponseCheckMember {
  ResponseCheckMember({
    this.id,
    this.roleId,
    this.mobile,
  });

  int? id;
  String? roleId;
  String? mobile;

  factory ResponseCheckMember.fromJson(Map<String, dynamic> json) => ResponseCheckMember(
    id: json["id"] ?? "",
    roleId: json["roleId"] ?? "",
    mobile: json["mobile"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "roleId": roleId,
    "mobile": mobile,
  };
}

class ResponseMember {
  ResponseMember({
    this.id,
    this.updateTime,
    this.createTime,
    this.nickname,
    this.fullname,
    this.username,
    this.password,
    this.phoneFirebase,
    this.firstname,
    this.lastname,
    this.mobile,
    this.tokenMessage,
    this.acceptAgreement,
    this.firebaseUid,
    this.advisorId,
    this.birthday,
    this.address,
    this.addressNumber,
    this.addressMoo,
    this.addressRoad,
    this.addressTumbol,
    this.addressAmphur,
    this.addressProvince,
    this.addressZipcode,
    this.email,
    this.lineIdTemp,
    this.facebookNameTemp,
    this.googleNameTemp,
    this.userUrl,
    this.ratingPms,
    this.twofaFlagTemp,
    this.secretGoogleTemp,
    this.countLogin,
    this.flag,
    this.userLevel,
    this.csIdTemp,
    this.csStatusTemp,
    this.idActivity,
    this.statusLdx,
    this.rowSpreadsheet,
    this.updateUser,
    this.createUser,
    this.idcard,
    this.displayName,
    this.userIdLine,
    this.userIdFb,
    this.userIdApple,
    this.linkAccountsConnect,
    this.profilePicture,
    this.usernameConnect,
    this.facebookConnect,
    this.appleConnect,
    this.lineConnect,
    this.roleId,
    this.idRefOnly,
    this.idRefOther,
    this.locationHome,
    this.idMR,
    this.ref_code_mr
  });

  int? id;
  String? updateTime;
  String? createTime;
  String? nickname;
  String? fullname;
  String? username;
  String? password;
  String? phoneFirebase;
  String? firstname;
  String? lastname;
  String? mobile;
  String? tokenMessage;
  String? acceptAgreement;
  String? firebaseUid;
  String? advisorId;
  String? birthday;
  String? address;
  String? addressNumber;
  String? addressMoo;
  String? addressRoad;
  String? addressTumbol;
  String? addressAmphur;
  String? addressProvince;
  String? addressZipcode;
  String? email;
  String? lineIdTemp;
  String? facebookNameTemp;
  String? googleNameTemp;
  String? userUrl;
  int? ratingPms;
  int? twofaFlagTemp;
  String? secretGoogleTemp;
  int? countLogin;
  int? flag;
  int? userLevel;
  String? csIdTemp;
  int? csStatusTemp;
  int? idActivity;
  String? statusLdx;
  int? rowSpreadsheet;
  String? updateUser;
  String? createUser;
  String? idcard;
  String? displayName;
  String? userIdLine;
  String? userIdFb;
  String? userIdApple;
  String? linkAccountsConnect;
  String? profilePicture;
  String? usernameConnect;
  String? facebookConnect;
  String? appleConnect;
  String? lineConnect;
  String? roleId;
  String? idRefOnly;
  String? idRefOther;
  String? locationHome;
  String? line_id_temp;
  String? idMR;
  String? ref_code_mr;

  factory ResponseMember.fromJson(Map<String, dynamic> json) => ResponseMember(
    id: json["id"] ?? "",
    updateTime: json["update_time"] ?? "",
    createTime: json["create_time"] ?? "",
    nickname: json["nickname"] ?? "",
    fullname: json["fullname"] ?? "",
    username: json["username"] ?? "",
    password: json["password"] ?? "",
    phoneFirebase: json["phone_firebase"] ?? "",
    firstname: json["firstname"] ?? "",
    lastname: json["lastname"] ?? "",
    mobile: json["mobile"] ?? "",
    tokenMessage: json["tokenMessage"] ?? "",
    acceptAgreement: json["accept_agreement"] ?? "",
    firebaseUid: json["firebase_uid"] ?? "",
    advisorId: json["advisor_id"] ?? "",
    birthday: json["birthday"] ?? "",
    address: json["address"] ?? "",
    addressNumber: json["address_number"] ?? "",
    addressMoo: json["address_moo"] ?? "",
    addressRoad: json["address_road"] ?? "",
    addressTumbol: json["address_tumbol"] ?? "",
    addressAmphur: json["address_amphur"] ?? "",
    addressProvince: json["address_province"] ?? "",
    addressZipcode: json["address_zipcode"] ?? "",
    email: json["email"] ?? "",
    lineIdTemp: json["line_id_temp"] ?? "",
    facebookNameTemp: json["facebook_name_temp"] ?? "",
    googleNameTemp: json["google_name_temp"] ?? "",
    userUrl: json["user_url"] ?? "",
    ratingPms: json["rating_PMS"] ?? "",
    twofaFlagTemp: json["twofa_flag_temp"] ?? "",
    secretGoogleTemp: json["secretGoogle_temp"] ?? "",
    countLogin: json["countLogin"] ?? "",
    flag: json["flag"] ?? "",
    userLevel: json["user_level"] ?? "",
    csIdTemp: json["cs_id_temp"] ?? "",
    csStatusTemp: json["cs_status_temp"] ?? "",
    idActivity: json["id_activity"] ?? "",
    statusLdx: json["status_ldx"] ?? "",
    rowSpreadsheet: json["row_spreadsheet"] ?? "",
    updateUser: json["update_user"] ?? "",
    createUser: json["create_user"] ?? "",
    idcard: json["idcard"] ?? "",
    displayName: json["displayName"] ?? "",
    userIdLine: json["userID_line"] ?? "",
    userIdFb: json["userID_fb"] ?? "",
    userIdApple: json["userID_apple"] ?? "",
    linkAccountsConnect: json["link_accounts_connect"] ?? "",
    profilePicture: json["profile_picture"] ?? "",
    usernameConnect: json["username_connect"] ?? "",
    facebookConnect: json["facebook_connect"] ?? "",
    appleConnect: json["apple_connect"] ?? "",
    lineConnect: json["line_connect"] ?? "",
    roleId: json["roleId"] ?? "",
    idRefOnly: json["id_ref_only"] ?? "",
    idRefOther: json["id_ref_other"] ?? "",
    locationHome: json["location_home"] ?? "",
    idMR: json["mr_code"] ?? "",
    ref_code_mr: json["ref_code_mr"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "update_time": updateTime,
    "create_time": createTime,
    "nickname": nickname,
    "fullname": fullname,
    "username": username,
    "password": password,
    "phone_firebase": phoneFirebase,
    "firstname": firstname,
    "lastname": lastname,
    "mobile": mobile,
    "tokenMessage": tokenMessage,
    "accept_agreement": acceptAgreement,
    "firebase_uid": firebaseUid,
    "advisor_id": advisorId,
    "birthday": birthday,
    "address": address,
    "address_number": addressNumber,
    "address_moo": addressMoo,
    "address_road": addressRoad,
    "address_tumbol": addressTumbol,
    "address_amphur": addressAmphur,
    "address_province": addressProvince,
    "address_zipcode": addressZipcode,
    "email": email,
    "line_id_temp": lineIdTemp,
    "facebook_name_temp": facebookNameTemp,
    "google_name_temp": googleNameTemp,
    "user_url": userUrl,
    "rating_PMS": ratingPms,
    "twofa_flag_temp": twofaFlagTemp,
    "secretGoogle_temp": secretGoogleTemp,
    "countLogin": countLogin,
    "flag": flag,
    "user_level": userLevel,
    "cs_id_temp": csIdTemp,
    "cs_status_temp": csStatusTemp,
    "id_activity": idActivity,
    "status_ldx": statusLdx,
    "row_spreadsheet": rowSpreadsheet,
    "update_user": updateUser,
    "create_user": createUser,
    "idcard": idcard,
    "displayName": displayName,
    "userID_line": userIdLine,
    "userID_fb": userIdFb,
    "userID_apple": userIdApple,
    "link_accounts_connect": linkAccountsConnect,
    "profile_picture": profilePicture,
    "username_connect": usernameConnect,
    "facebook_connect": facebookConnect,
    "apple_connect": appleConnect,
    "line_connect": lineConnect,
    "roleId": roleId,
    "id_ref_only": idRefOnly,
    "id_ref_other": idRefOther,
    "location_home": locationHome,
    "mr_code": idMR,
    "ref_code_mr": ref_code_mr
  };
}

class ResponseProfileAndMR {
  ResponseProfileAndMR({
    this.id,
    this.nickname,
    this.fullname,
    this.username,
    this.password,
    this.phoneFirebase,
    this.firstname,
    this.lastname,
    this.mobile,
    this.tokenMessage,
    this.acceptAgreement,
    this.birthday,
    this.address,
    this.addressNumber,
    this.addressMoo,
    this.addressRoad,
    this.addressTumbol,
    this.addressAmphur,
    this.addressProvince,
    this.addressZipcode,
    this.email,
    this.lineIdTemp,
    this.userUrl,
    this.countLogin,
    this.createUser,
    this.idcard,
    this.displayName,
    this.userIdLine,
    this.userIdFb,
    this.userIdApple,
    this.linkAccountsConnect,
    this.profilePicture,
    this.usernameConnect,
    this.facebookConnect,
    this.appleConnect,
    this.lineConnect,
    this.roleId,
    this.idRefOnly,
    this.idRefOther,
    this.locationHome,
    this.mrCode,
    this.bankNameMR,
    this.bookBankNoMR,
    this.bookBankNameMR,
    this.fullNameMR,
    this.idCardMR,
    this.phoneNumberMR,
    this.careerMR,
    this.careerNoteMR,
    this.businessNameMR,
    this.userIdTg,
    this.ref_code_mr
  });

  int? id;
  String? nickname;
  String? fullname;
  String? username;
  String? password;
  String? phoneFirebase;
  String? firstname;
  String? lastname;
  String? mobile;
  String? tokenMessage;
  String? acceptAgreement;
  String? birthday;
  String? address;
  String? addressNumber;
  String? addressMoo;
  String? addressRoad;
  String? addressTumbol;
  String? addressAmphur;
  String? addressProvince;
  String? addressZipcode;
  String? email;
  String? lineIdTemp;
  String? userUrl;
  int? countLogin;
  String? createUser;
  String? idcard;
  String? displayName;
  String? userIdLine;
  String? userIdFb;
  String? userIdApple;
  String? linkAccountsConnect;
  String? profilePicture;
  String? usernameConnect;
  String? facebookConnect;
  String? appleConnect;
  String? lineConnect;
  String? roleId;
  String? idRefOnly;
  String? idRefOther;
  String? locationHome;
  String? mrCode;
  String? bankNameMR;
  String? bookBankNoMR;
  String? bookBankNameMR;
  String? fullNameMR;
  String? idCardMR;
  String? phoneNumberMR;
  String? careerMR;
  String? careerNoteMR;
  String? businessNameMR;
  String? userIdTg;
  String? ref_code_mr;

  factory ResponseProfileAndMR.fromJson(Map<String, dynamic> json) => ResponseProfileAndMR(
    id: json["id"] ?? "",
    nickname: json["nickname"] ?? "",
    fullname: json["fullname"] ?? "",
    username: json["username"] ?? "",
    password: json["password"] ?? "",
    phoneFirebase: json["phone_firebase"] ?? "",
    firstname: json["firstname"] ?? "",
    lastname: json["lastname"] ?? "",
    mobile: json["mobile"] ?? "",
    tokenMessage: json["tokenMessage"] ?? "",
    acceptAgreement: json["accept_agreement"] ?? "",
    birthday: json["birthday"] ?? "",
    address: json["address"] ?? "",
    addressNumber: json["address_number"] ?? "",
    addressMoo: json["address_moo"] ?? "",
    addressRoad: json["address_road"] ?? "",
    addressTumbol: json["address_tumbol"] ?? "",
    addressAmphur: json["address_amphur"] ?? "",
    addressProvince: json["address_province"] ?? "",
    addressZipcode: json["address_zipcode"] ?? "",
    email: json["email"] ?? "",
    lineIdTemp: json["line_id_temp"] ?? "",
    userUrl: json["user_url"] ?? "",
    countLogin: json["countLogin"] ?? "",
    createUser: json["create_user"] ?? "",
    idcard: json["idcard"] ?? "",
    displayName: json["displayName"] ?? "",
    userIdLine: json["userID_line"] ?? "",
    userIdFb: json["userID_fb"] ?? "",
    userIdApple: json["userID_apple"] ?? "",
    linkAccountsConnect: json["link_accounts_connect"] ?? "",
    profilePicture: json["profile_picture"] ?? "",
    usernameConnect: json["username_connect"] ?? "",
    facebookConnect: json["facebook_connect"] ?? "",
    appleConnect: json["apple_connect"] ?? "",
    lineConnect: json["line_connect"] ?? "",
    roleId: json["roleId"] ?? "",
    idRefOnly: json["id_ref_only"] ?? "",
    idRefOther: json["id_ref_other"] ?? "",
    locationHome: json["location_home"] ?? "",
    mrCode: json["mr_code"] ?? "",
    bankNameMR: json["bank_name"] ?? "",
    bookBankNoMR: json["book_bank_no"] ?? "",
    bookBankNameMR: json["book_bank_name"] ?? "",
    fullNameMR: json["full_name"] ?? "",
    idCardMR: json["id_card"] ?? "",
    phoneNumberMR: json["phone_number"] ?? "",
    careerMR: json["career"] ?? "",
    careerNoteMR: json["career_note"] ?? "",
    businessNameMR: json["business_name"] ?? "",
    userIdTg: json["userID_tg"] ?? "",
    ref_code_mr: json["ref_code_mr"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "nickname": nickname,
    "fullname": fullname,
    "username": username,
    "password": password,
    "phone_firebase": phoneFirebase,
    "firstname": firstname,
    "lastname": lastname,
    "mobile": mobile,
    "tokenMessage": tokenMessage,
    "accept_agreement": acceptAgreement,
    "birthday": birthday,
    "address": address,
    "address_number": addressNumber,
    "address_moo": addressMoo,
    "address_road": addressRoad,
    "address_tumbol": addressTumbol,
    "address_amphur": addressAmphur,
    "address_province": addressProvince,
    "address_zipcode": addressZipcode,
    "email": email,
    "line_id_temp": lineIdTemp,
    "user_url": userUrl,
    "countLogin": countLogin,
    "create_user": createUser,
    "idcard": idcard,
    "displayName": displayName,
    "userID_line": userIdLine,
    "userID_fb": userIdFb,
    "userID_apple": userIdApple,
    "link_accounts_connect": linkAccountsConnect,
    "profile_picture": profilePicture,
    "username_connect": usernameConnect,
    "facebook_connect": facebookConnect,
    "apple_connect": appleConnect,
    "line_connect": lineConnect,
    "roleId": roleId,
    "id_ref_only": idRefOnly,
    "id_ref_other": idRefOther,
    "location_home": locationHome,
    "mr_code": mrCode,
    "bank_name": bankNameMR,
    "book_bank_no": bookBankNoMR,
    "book_bank_name": bookBankNameMR,
    "full_name": fullNameMR,
    "id_card": idCardMR,
    "phone_number": phoneNumberMR,
    "career": careerMR,
    "career_note": careerNoteMR,
    "business_name": businessNameMR,
    "userID_tg": userIdTg,
    "ref_code_mr": ref_code_mr,
  };
}

class SaveProfile {
  SaveProfile({
    required this.id,
    required this.firstname,
    required this.lastname,
    required this.idcard,
    required this.birthday,
    required this.address,
    required this.number,
    required this.moo,
    required this.road,
    required this.tumbol,
    required this.amphur,
    required this.province,
    required this.zipcode,
    required this.email,
    required this.location,
  });

  String id;
  String firstname;
  String lastname;
  String idcard;
  String birthday;
  String address;
  String number;
  String moo;
  String road;
  String tumbol;
  String amphur;
  String province;
  String zipcode;
  String email;
  String location;

  factory SaveProfile.fromJson(Map<String, dynamic> json) => SaveProfile(
    id: json["id"],
    firstname: json["firstname"],
    lastname: json["lastname"],
    idcard: json["idcard"],
    birthday: json["birthday"],
    address: json["address"],
    number: json["number"],
    moo: json["moo"],
    road: json["road"],
    tumbol: json["tumbol"],
    amphur: json["amphur"],
    province: json["province"],
    zipcode: json["zipcode"],
    email: json["email"],
    location: json["location"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstname": firstname,
    "lastname": lastname,
    "idcard": idcard,
    "birthday": birthday,
    "address": address,
    "number": number,
    "moo": moo,
    "road": road,
    "tumbol": tumbol,
    "amphur": amphur,
    "province": province,
    "zipcode": zipcode,
    "email": email,
    "location": location,
  };
}