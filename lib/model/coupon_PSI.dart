class DataCustomer {
  String? customer;
  String? carCode;
  String? carReg;
  String? engineCode;
  String? cvipDate;
  String? cttDate;
  String? centerName;
  String? urlPsi;
  int lastmile;

  DataCustomer({
    this.customer,
    this.carCode,
    this.carReg,
    this.engineCode,
    this.cvipDate,
    this.cttDate,
    this.centerName,
    this.urlPsi,
    this.lastmile = 0,
  });

  factory DataCustomer.fromJson(Map<String, dynamic> json) => DataCustomer(
    customer: json["customer"],
    carCode: json["carCode"],
    carReg: json["carReg"],
    engineCode: json["engineCode"],
    cvipDate: json["cvipDate"],
    cttDate: json["ctt_date"],
    centerName: json["centerName"],
    urlPsi: json["url_psi"],
    lastmile: json["lastmile"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "customer": customer,
    "carCode": carCode,
    "carReg": carReg,
    "engineCode": engineCode,
    "cvipDate": cvipDate,
    "ctt_date": cttDate,
    "centerName": centerName,
    "url_psi": urlPsi,
    "lastmile": lastmile,
  };
}

class PsiDetail {
  String? title;
  String? price;

  PsiDetail({this.title, this.price});

  factory PsiDetail.fromJson(Map<String, dynamic> json) => PsiDetail(
    title: json["title"],
    price: json["price"],
  );

  Map<String, dynamic> toJson() => {
    "title": title,
    "price": price,
  };
}

class DataCoupon {
  String? coupon;
  String? title;
  String? description;
  Limitation? limitation;
  int round;
  String? canInDate;
  String? expireDate;
  List<PsiDetail> psiDetail;
  int psiSum;
  int psiDiscount;
  int psiNet;
  double psiVat;
  double psiTotal;
  bool isExpired;
  bool alreadyInUse;

  DataCoupon({
    this.coupon,
    this.title,
    this.description,
    this.limitation,
    this.round = 0,
    this.canInDate,
    this.expireDate,
    this.psiDetail = const [],
    this.psiSum = 0,
    this.psiDiscount = 0,
    this.psiNet = 0,
    this.psiVat = 0.0,
    this.psiTotal = 0.0,
    this.isExpired = false,
    this.alreadyInUse = false,
  });

  factory DataCoupon.fromJson(Map<String, dynamic> json) => DataCoupon(
    coupon: json["coupon"],
    title: json["title"],
    description: json["description"],
    limitation: json["limitation"] != null ? Limitation.fromJson(json["limitation"]) : null,
    round: json["round"] ?? 0,
    canInDate: json["canInDate"],
    expireDate: json["expireDate"],
    psiDetail: (json["psi_detail"] as List).map((item) => PsiDetail.fromJson(item)).toList(),
    psiSum: json["psi_sum"] ?? 0,
    psiDiscount: json["psi_discount"] ?? 0,
    psiNet: json["psi_net"] ?? 0,
    psiVat: json["psi_vat"]?.toDouble() ?? 0.0,
    psiTotal: json["psi_total"]?.toDouble() ?? 0.0,
    isExpired: json["isExpired"] ?? false,
    alreadyInUse: json["alreadyInUse"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "coupon": coupon,
    "title": title,
    "description": description,
    "limitation": limitation?.toJson(),
    "round": round,
    "canInDate": canInDate,
    "expireDate": expireDate,
    "psi_detail": psiDetail.map((item) => item.toJson()).toList(),
    "psi_sum": psiSum,
    "psi_discount": psiDiscount,
    "psi_net": psiNet,
    "psi_vat": psiVat,
    "psi_total": psiTotal,
    "isExpired": isExpired,
    "alreadyInUse": alreadyInUse,
  };
}

class Limitation {
  int start;
  int end;

  Limitation({this.start = 0, this.end = 0});

  factory Limitation.fromJson(Map<String, dynamic> json) => Limitation(
    start: json["start"] ?? 0,
    end: json["end"] ?? 0,
  );

  Map<String, dynamic> toJson() => {
    "start": start,
    "end": end,
  };
}

class CarOwnerModel {
  List<CarList>? carList;

  CarOwnerModel({
    this.carList,
  });

  factory CarOwnerModel.fromJson(Map<String, dynamic> json) => CarOwnerModel(
    carList: List<CarList>.from(json["carList"].map((x) => CarList.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "carList": List<dynamic>.from(carList!.map((x) => x.toJson())),
  };
}

class CarList {
  // int? running;
  String? name;
  String? phone;
  dynamic reg;
  String? machineNumber;
  List<Mile>? miles;
  List<Rustproof>? rustproof;
  List<Insurance>? insurance;
  // List<Pmg>? pmg;
  String? colorCode;
  String? colorName;
  String? carCode;
  String? carModel;
  String? carModelSa;
  String? carType;
  String? carImage;
  String? carVIN;

  CarList({
    // this.running,
    this.name,
    this.phone,
    this.reg,
    this.machineNumber,
    this.miles,
    this.rustproof,
    this.insurance,
    // this.pmg,
    this.colorCode,
    this.colorName,
    this.carCode,
    this.carModel,
    this.carModelSa,
    this.carType,
    this.carImage,
    this.carVIN,
  });

  factory CarList.fromJson(Map<String, dynamic> json) => CarList(
    // running: json["running"] ?? "",
    name: json["name"] ?? "",
    phone: json["phone"] ?? "",
    reg: json["reg"] ?? "",
    machineNumber: json["machine_number"] ?? "",
    miles: List<Mile>.from(json["miles"].map((x) => Mile.fromJson(x))),
    rustproof: List<Rustproof>.from(json["rustproof"].map((x) => Rustproof.fromJson(x))),
    insurance: List<Insurance>.from(json["insurance"].map((x) => Insurance.fromJson(x))),
    // pmg: List<Pmg>.from(json["pmg"].map((x) => Pmg.fromJson(x))),
    colorCode: json["colorCode"] ?? "",
    colorName: json["colorName"] ?? "",
    carCode: json["carCode"] ?? "",
    carModel: json["car_model"] ?? "",
    carModelSa: json["car_model_sa"] ?? "",
    carType: json["car_type"] ?? "",
    carImage: json["car_image"] ?? "",
    carVIN: json["car_id"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    // "running": running,
    "name": name,
    "phone": phone,
    "reg": reg,
    "machine_number": machineNumber,
    "miles": List<dynamic>.from(miles!.map((x) => x.toJson())),
    "rustproof": List<dynamic>.from(rustproof!.map((x) => x.toJson())),
    "insurance": List<dynamic>.from(insurance!.map((x) => x.toJson())),
    // "pmg": List<dynamic>.from(pmg!.map((x) => x.toJson())),
    "colorCode": colorCode,
    "colorName": colorName,
    "carCode": carCode,
    "car_model": carModel,
    "car_model_sa": carModelSa,
    "car_type": carType,
    "car_image": carImage,
    "car_id": carVIN,
  };
}

class Insurance {
  String? insurance;
  String? insuranceDate;
  String? insuranceDateEnd;

  Insurance({
    this.insurance,
    this.insuranceDate,
    this.insuranceDateEnd,
  });

  factory Insurance.fromJson(Map<String, dynamic> json) => Insurance(
    insurance: json["insurance"],
    insuranceDate: json["insurance_date"],
    insuranceDateEnd: json["insurance_date_end"],
  );

  Map<String, dynamic> toJson() => {
    "insurance": insurance,
    "insurance_date": insuranceDate,
    "insurance_date_end": insuranceDateEnd,
  };
}

class Mile {
  String? mileageDetail;
  String? mileage;
  String? mileageDate;

  Mile({
    this.mileageDetail,
    this.mileage,
    this.mileageDate,
  });

  factory Mile.fromJson(Map<String, dynamic> json) => Mile(
    mileageDetail: json["mileage_detail"],
    mileage: json["mileage"],
    mileageDate: json["mileage_date"],
  );

  Map<String, dynamic> toJson() => {
    "mileage_detail": mileageDetail,
    "mileage": mileage,
    "mileage_date": mileageDate,
  };
}

class Rustproof {
  String? rustDetail;
  String? rustDate;

  Rustproof({
    this.rustDetail,
    this.rustDate,
  });

  factory Rustproof.fromJson(Map<String, dynamic> json) => Rustproof(
    rustDetail: json["rust_detail"],
    rustDate: json["rust_date"],
  );

  Map<String, dynamic> toJson() => {
    "rust_detail": rustDetail,
    "rust_date": rustDate,
  };
}

class Pmg {
  String? pmgJobId;
  String? pmgDate;

  Pmg({
    this.pmgJobId,
    this.pmgDate,
  });

  factory Pmg.fromJson(Map<String, dynamic> json) => Pmg(
    pmgJobId: json["pmg_job_id"] ?? "",
    pmgDate: json["pmg_date"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "pmg_job_id": pmgJobId,
    "pmg_date": pmgDate,
  };
}