class LoginUsername {
  LoginUsername({
    required this.database,
    required this.table,
    required this.filedUsername,
    required this.username,
    required this.password,
  });

  String database;
  String table;
  String filedUsername;
  String username;
  String password;

  factory LoginUsername.fromJson(Map<String, dynamic> json) => LoginUsername(
    database: json["database"] == null ? null : json["database"],
    table: json["table"] == null ? null : json["table"],
    filedUsername: json["filedUsername"] == null ? null : json["filedUsername"],
    username: json["username"] == null ? null : json["username"],
    password: json["password"] == null ? null : json["password"],
  );

  Map<String, dynamic> toJson() => {
    "database": database == null ? null : database,
    "table": table == null ? null : table,
    "filedUsername": filedUsername == null ? null : filedUsername,
    "username": username == null ? null : username,
    "password": password == null ? null : password,
  };
}