class ResponseLikePoint {
  String? address;
  double? totalBalance;
  double? availableBalance;
  double? lockedBalance;
  LockedDetail? lockedDetail;

  ResponseLikePoint({
    this.address,
    this.totalBalance,
    this.availableBalance,
    this.lockedBalance,
    this.lockedDetail,
  });

  factory ResponseLikePoint.fromJson(Map<String, dynamic> json) => ResponseLikePoint(
    address: json["address"],
    totalBalance: json["totalBalance"].toDouble(),
    availableBalance: json["availableBalance"].toDouble(),
    lockedBalance: json["lockedBalance"].toDouble(),
    lockedDetail: LockedDetail.fromJson(json["lockedDetail"]),
  );

  Map<String, dynamic> toJson() => {
    "address": address,
    "totalBalance": totalBalance,
    "availableBalance": availableBalance,
    "lockedBalance": lockedBalance,
    "lockedDetail": lockedDetail!.toJson(),
  };
}

class LockedDetail {
  double? lockAndEarn;
  double? timeLock;
  double? lockCompound;
  double? lpcu;

  LockedDetail({
    this.lockAndEarn,
    this.timeLock,
    this.lockCompound,
    this.lpcu,
  });

  factory LockedDetail.fromJson(Map<String, dynamic> json) => LockedDetail(
    lockAndEarn: json["lockAndEarn"].toDouble(),
    timeLock: json["timeLock"].toDouble(),
    lockCompound: json["lockCompound"].toDouble(),
    lpcu: json["LPCU"].toDouble(),
  );

  Map<String, dynamic> toJson() => {
    "lockAndEarn": lockAndEarn,
    "timeLock": timeLock,
    "lockCompound": lockCompound,
    "LPCU": lpcu,
  };
}