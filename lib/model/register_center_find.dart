class RegisterCenterFind {
  RegisterCenterFind({
    required this.reqPhone,
    required this.reqPhoneNationCode,
  });

  String reqPhone;
  String reqPhoneNationCode;

  factory RegisterCenterFind.fromJson(Map<String, dynamic> json) => RegisterCenterFind(
    reqPhone: json["reqPhone"] == null ? null : json["reqPhone"],
    reqPhoneNationCode: json["reqPhoneNationCode"] == null ? null : json["reqPhoneNationCode"],
  );

  Map<String, dynamic> toJson() => {
    "reqPhone": reqPhone == null ? null : reqPhone,
    "reqPhoneNationCode": reqPhoneNationCode == null ? null : reqPhoneNationCode
  };
}

class ResponseRegisterCenterFind {
  ResponseRegisterCenterFind({
    this.statusCode,
    this.refCode,
  });

  int? statusCode;
  String? refCode;

  factory ResponseRegisterCenterFind.fromJson(Map<String, dynamic> json) => ResponseRegisterCenterFind(
    statusCode: json["statusCode"] == null ? null : json["statusCode"],
    refCode: json["refCode"] == null ? null : json["refCode"],
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode == null ? null : statusCode,
    "refCode": refCode == null ? null : refCode
  };
}
