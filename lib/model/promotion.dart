class ResponsePromotion {
  ResponsePromotion({
    this.promotionId,
    this.userCreateId,
    this.userEditId,
    this.promotionName,
    this.promotionBody,
    this.promotionPic,
    this.promotionCover,
    this.promotionVideo,
    this.promotionDate,
    this.promotionEnd,
    this.promotionEditDate,
    this.promotionEditFlag,
    this.promotionFlag,
  });

  int? promotionId;
  int? userCreateId;
  int? userEditId;
  String? promotionName;
  String? promotionBody;
  String? promotionPic;
  String? promotionCover;
  String? promotionVideo;
  String? promotionDate;
  String? promotionEnd;
  String? promotionEditDate;
  int? promotionEditFlag;
  int? promotionFlag;

  factory ResponsePromotion.fromJson(Map<String, dynamic> json) => ResponsePromotion(
    promotionId: json["promotion_id"],
    userCreateId: json["user_create_id"],
    userEditId: json["user_edit_id"],
    promotionName: json["promotion_name"],
    promotionBody: json["promotion_body"],
    promotionPic: json["promotion_pic"],
    promotionCover: json["promotion_cover"],
    promotionVideo: json["promotion_video"],
    promotionDate: json["promotion_date"],
    promotionEnd: json["promotion_end"],
    promotionEditDate: json["promotion_edit_date"],
    promotionEditFlag: json["promotion_edit_flag"],
    promotionFlag: json["promotion_flag"],
  );

  Map<String, dynamic> toJson() => {
    "promotion_id": promotionId,
    "user_create_id": userCreateId,
    "user_edit_id": userEditId,
    "promotion_name": promotionName,
    "promotion_body": promotionBody,
    "promotion_pic": promotionPic,
    "promotion_cover": promotionCover,
    "promotion_video": promotionVideo,
    "promotion_date": promotionDate,
    "promotion_end": promotionEnd,
    "promotion_edit_date": promotionEditDate,
    "promotion_edit_flag": promotionEditFlag,
    "promotion_flag": promotionFlag,
  };
}

class PromotionList {
final List<ResponsePromotion>? data;

PromotionList({this.data});

factory PromotionList.fromJson(List<dynamic> parsedJson){
List<ResponsePromotion>? data = <ResponsePromotion>[];
data = parsedJson.map((i) => ResponsePromotion.fromJson(i)).toList();
return PromotionList(data: data);
}
}