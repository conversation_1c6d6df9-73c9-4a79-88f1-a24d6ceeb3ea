import 'dart:convert';

LifetimeAppointment lifetimeAppointmentFromJson(String str) => LifetimeAppointment.fromJson(json.decode(str));

String lifetimeAppointmentToJson(LifetimeAppointment data) => json.encode(data.toJson());

class LifetimeAppointment {
  List<CarList>? carList;

  LifetimeAppointment({
    this.carList,
  });

  factory LifetimeAppointment.fromJson(Map<String, dynamic> json) => LifetimeAppointment(
    carList: List<CarList>.from(json["carList"].map((x) => CarList.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "carList": List<dynamic>.from(carList!.map((x) => x.toJson())),
  };
}

class CarList {
  int? running;
  String? name;
  String? phone;
  String? reg;
  String? machineNumber;
  List<Mile>? miles;
  List<Rustproof>? rustproof;
  List<Insurance>? insurance;
  List<Pmg>? pmg;
  String? colorCode;
  String? colorName;
  String? carCode;
  String? carModel;
  String? carModelSa;
  String? carType;
  String? carImage;
  String? carVIN;

  CarList({
    this.running,
    this.name,
    this.phone,
    this.reg,
    this.machineNumber,
    this.miles,
    this.rustproof,
    this.insurance,
    this.pmg,
    this.colorCode,
    this.colorName,
    this.carCode,
    this.carModel,
    this.carModelSa,
    this.carType,
    this.carImage,
    this.carVIN,
  });

  factory CarList.fromJson(Map<String, dynamic> json) => CarList(
    running: json["running"] ?? "",
    name: json["name"] ?? "",
    phone: json["phone"] ?? "",
    reg: json["reg"] ?? "",
    machineNumber: json["machine_number"] ?? "",
    miles: List<Mile>.from(json["miles"].map((x) => Mile.fromJson(x))),
    rustproof: List<Rustproof>.from(json["rustproof"].map((x) => Rustproof.fromJson(x))),
    insurance: List<Insurance>.from(json["insurance"].map((x) => Insurance.fromJson(x))),
    pmg: List<Pmg>.from(json["pmg"].map((x) => Pmg.fromJson(x))),
    colorCode: json["colorCode"] ?? "",
    colorName: json["colorName"] ?? "",
    carCode: json["carCode"] ?? "",
    carModel: json["car_model"] ?? "",
    carModelSa: json["car_model_sa"] ?? "",
    carType: json["car_type"] ?? "",
    carImage: json["car_image"] ?? "",
    carVIN: json["car_id"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "name": name,
    "phone": phone,
    "reg": reg,
    "machine_number": machineNumber,
    "miles": List<dynamic>.from(miles!.map((x) => x.toJson())),
    "rustproof": List<dynamic>.from(rustproof!.map((x) => x.toJson())),
    "insurance": List<dynamic>.from(insurance!.map((x) => x.toJson())),
    "pmg": List<dynamic>.from(pmg!.map((x) => x.toJson())),
    "colorCode": colorCode,
    "colorName": colorName,
    "carCode": carCode,
    "car_model": carModel,
    "car_model_sa": carModelSa,
    "car_type": carType,
    "car_image": carImage,
    "car_id": carVIN,
  };
}

class Insurance {
  String? insurance;
  String? insuranceDate;
  String? insuranceDateEnd;

  Insurance({
    this.insurance,
    this.insuranceDate,
    this.insuranceDateEnd,
  });

  factory Insurance.fromJson(Map<String, dynamic> json) => Insurance(
    insurance: json["insurance"],
    insuranceDate: json["insurance_date"],
    insuranceDateEnd: json["insurance_date_end"],
  );

  Map<String, dynamic> toJson() => {
    "insurance": insurance,
    "insurance_date": insuranceDate,
    "insurance_date_end": insuranceDateEnd,
  };
}

class Mile {
  String? mileageDetail;
  String? mileage;
  String? mileageDate;

  Mile({
    this.mileageDetail,
    this.mileage,
    this.mileageDate,
  });

  factory Mile.fromJson(Map<String, dynamic> json) => Mile(
    mileageDetail: json["mileage_detail"],
    mileage: json["mileage"],
    mileageDate: json["mileage_date"],
  );

  Map<String, dynamic> toJson() => {
    "mileage_detail": mileageDetail,
    "mileage": mileage,
    "mileage_date": mileageDate,
  };
}

class Rustproof {
  String? rustDetail;
  String? rustDate;

  Rustproof({
    this.rustDetail,
    this.rustDate,
  });

  factory Rustproof.fromJson(Map<String, dynamic> json) => Rustproof(
    rustDetail: json["rust_detail"],
    rustDate: json["rust_date"],
  );

  Map<String, dynamic> toJson() => {
    "rust_detail": rustDetail,
    "rust_date": rustDate,
  };
}

class Pmg {
  String? pmgJobId;
  String? pmgDate;

  Pmg({
    this.pmgJobId,
    this.pmgDate,
  });

  factory Pmg.fromJson(Map<String, dynamic> json) => Pmg(
    pmgJobId: json["pmg_job_id"] ?? "",
    pmgDate: json["pmg_date"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "pmg_job_id": pmgJobId,
    "pmg_date": pmgDate,
  };
}