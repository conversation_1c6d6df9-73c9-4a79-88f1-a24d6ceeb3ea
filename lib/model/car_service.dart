class ResponseCarService {
  ResponseCarService({
    this.running,
    this.updateTime,
    this.updateUser,
    this.createTime,
    this.createUser,
    this.rowSpreadsheet,
    this.regis,
    this.team,
    this.typeTeam,
    this.mobile,
    this.tokenLine,
    this.serviceArea,
  });

  int? running;
  String? updateTime;
  String? updateUser;
  String? createTime;
  String? createUser;
  dynamic rowSpreadsheet;
  String? regis;
  String? team;
  String? typeTeam;
  String? mobile;
  String? tokenLine;
  String? serviceArea;

  factory ResponseCarService.fromJson(Map<String, dynamic> json) => ResponseCarService(
    running: json["running"] == null ? null : json["running"],
    updateTime: json["update_time"] == null ? null : json["update_time"],
    updateUser: json["update_user"] == null ? null : json["update_user"],
    createTime: json["create_time"] == null ? null : json["create_time"],
    createUser: json["create_user"] == null ? null : json["create_user"],
    rowSpreadsheet: json["row_spreadsheet"] == null ? null : json["row_spreadsheet"],
    regis: json["regis"] == null ? null : json["regis"],
    team: json["team"] == null ? null : json["team"],
    typeTeam: json["type_team"] == null ? null : json["type_team"],
    mobile: json["mobile"] == null ? null : json["mobile"],
    tokenLine: json["token_line"] == null ? null : json["token_line"],
    serviceArea: json["service_area"] == null ? null : json["service_area"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "update_time": updateTime,
    "update_user": updateUser,
    "create_time": createTime,
    "create_user": createUser,
    "row_spreadsheet": rowSpreadsheet,
    "regis": regis,
    "team": team,
    "type_team": typeTeam,
    "mobile": mobile,
    "token_line": tokenLine,
    "service_area": serviceArea,
  };
}

class CarServiceList {
  final List<ResponseCarService>? data;

  CarServiceList({this.data});

  factory CarServiceList.fromJson(List<dynamic> parsedJson){
    List<ResponseCarService>? data = <ResponseCarService>[];
    data = parsedJson.map((i) => ResponseCarService.fromJson(i)).toList();
    return CarServiceList(data: data);
  }
}