class GetMR {
  GetMR({
    required this.userId,
  });

  String userId;

  factory GetMR.fromJson(Map<String, dynamic> json) => GetMR(
    userId: json["userId"],
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
  };
}

class ResponseMR {
  ResponseMR({
    this.mrCode,
  });

  String? mrCode;

  factory ResponseMR.fromJson(Map<String, dynamic> json) => ResponseMR(
    mrCode: json["mr_code"],
  );

  Map<String, dynamic> toJson() => {
    "mr_code": mrCode,
  };
}

class GetRankMR {
  GetRankMR({
    required this.mrCode,
  });

  String mrCode;

  factory GetRankMR.fromJson(Map<String, dynamic> json) => GetRankMR(
    mrCode: json["MrCode"],
  );

  Map<String, dynamic> toJson() => {
    "MrCode": mrCode,
  };
}

class ResponseRankMR {
  ResponseRankMR({
    this.rankCurrent,
    this.pointLikeCurrent,
    this.pointLikeLastYear,
  });

  String? rankCurrent;
  int? pointLikeCurrent;
  int? pointLikeLastYear;

  factory ResponseRankMR.fromJson(Map<String, dynamic> json) => ResponseRankMR(
    rankCurrent: json["rankCurrent"],
    pointLikeCurrent: json["pointLikeCurrent"],
    pointLikeLastYear: json["pointLikeLastYear"],
  );

  Map<String, dynamic> toJson() => {
    "rankCurrent": rankCurrent,
    "pointLikeCurrent": pointLikeCurrent,
    "pointLikeLastYear": pointLikeLastYear,
  };
}

class GetProfileMR {
  GetProfileMR({
    required this.mrCode,
  });

  String mrCode;

  factory GetProfileMR.fromJson(Map<String, dynamic> json) => GetProfileMR(
    mrCode: json["MrCode"],
  );

  Map<String, dynamic> toJson() => {
    "MrCode": mrCode,
  };
}

class ResponseProfileMR {
  ResponseProfileMR({
    this.running,
    this.mrCode,
    this.userId,
    this.bankName,
    this.bookBankNo,
    this.bookBankName,
    this.fullName,
    this.idCard,
    this.phoneNumber,
    this.career,
    this.careerNote,
    this.businessName,
  });

  int? running;
  String? mrCode;
  int? userId;
  String? bankName;
  String? bookBankNo;
  String? bookBankName;
  String? fullName;
  String? idCard;
  String? phoneNumber;
  String? career;
  String? careerNote;
  String? businessName;

  factory ResponseProfileMR.fromJson(Map<String, dynamic> json) => ResponseProfileMR(
    running: json["running"],
    mrCode: json["mr_code"],
    userId: json["user_id"],
    bankName: json["bank_name"],
    bookBankNo: json["book_bank_no"],
    bookBankName: json["book_bank_name"],
    fullName: json["full_name"],
    idCard: json["id_card"],
    phoneNumber: json["phone_number"],
    career: json["career"],
    careerNote: json["career_note"],
    businessName: json["business_name"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "mr_code": mrCode,
    "user_id": userId,
    "bank_name": bankName,
    "book_bank_no": bookBankNo,
    "book_bank_name": bookBankName,
    "full_name": fullName,
    "id_card": idCard,
    "phone_number": phoneNumber,
    "career": career,
    "career_note": careerNote,
    "business_name": businessName,
  };
}

class GetReferralMR {
  GetReferralMR({
    required this.mrCode,
    required this.takeOffset,
  });

  String mrCode;
  int takeOffset;

  factory GetReferralMR.fromJson(Map<String, dynamic> json) => GetReferralMR(
    mrCode: json["MrCode"],
    takeOffset: json["takeOffset"],
  );

  Map<String, dynamic> toJson() => {
    "MrCode": mrCode,
    "takeOffset": takeOffset,
  };
}

class ResponseReferralMR {
  ResponseReferralMR({
    this.running,
    this.amountLikepoint,
    this.amountFiat,
    this.referral,
    this.mrReferral,
    this.fullName,
    this.phoneNumber,
    this.modelCar,
    this.noteReferral,
    this.branchName,
    this.salesperson,
    this.rewardType,
    this.statusReferral,
    this.productReferral,
    this.carRegis,
    this.poiRunning,
    this.createTime,
  });

  int? running;
  int? amountLikepoint;
  int? amountFiat;
  String? referral;
  String? mrReferral;
  String? fullName;
  String? phoneNumber;
  String? modelCar;
  String? noteReferral;
  String? branchName;
  String? salesperson;
  String? rewardType;
  String? statusReferral;
  String? productReferral;
  String? carRegis;
  int? poiRunning;
  String? createTime;

  factory ResponseReferralMR.fromJson(Map<String, dynamic> json) => ResponseReferralMR(
    running: json["running"],
    amountLikepoint: json["amount_likepoint"],
    amountFiat: json["amount_fiat"],
    referral: json["referral"],
    mrReferral: json["mr_referral"],
    fullName: json["full_name"],
    phoneNumber: json["phone_number"],
    modelCar: json["model_car"],
    noteReferral: json["note_referral"],
    branchName: json["branch_name"],
    salesperson: json["salesperson"],
    rewardType: json["reward_type"],
    statusReferral: json["status_referral"],
    productReferral: json["product_referral"],
    carRegis: json["car_regis"],
    poiRunning: json["poi_running"],
    createTime: json["create_time"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "amount_likepoint": amountLikepoint,
    "amount_fiat": amountFiat,
    "referral": referral,
    "mr_referral": mrReferral,
    "full_name": fullName,
    "phone_number": phoneNumber,
    "model_car": modelCar,
    "note_referral": noteReferral,
    "branch_name": branchName,
    "salesperson": salesperson,
    "reward_type": rewardType,
    "status_referral": statusReferral,
    "product_referral": productReferral,
    "car_regis": carRegis,
    "poi_running": poiRunning,
    "create_time": createTime,
  };
}

class ReferralList {
  final List<ResponseReferralMR>? data;

  ReferralList({this.data});

  factory ReferralList.fromJson(List<dynamic> parsedJson){
    List<ResponseReferralMR>? data = <ResponseReferralMR>[];
    data = parsedJson.map((i) => ResponseReferralMR.fromJson(i)).toList();
    return ReferralList(data: data);
  }
}

class ResponseRankMRFB {
  ResponseRankMRFB({
    this.rank,
    this.accumulate,
    this.detail,
    this.id,
  });

  String? rank;
  int? accumulate;
  String? detail;
  String? id;

  factory ResponseRankMRFB.fromJson(Map<String, dynamic> json) => ResponseRankMRFB(
    rank: json["rank"],
    accumulate: json["accumulate"],
    detail: json["detail"],
    id: json["id"],
  );

  Map<String, dynamic> toJson() => {
    "rank": rank,
    "accumulate": accumulate,
    "detail": detail,
    "id": id,
  };
}

