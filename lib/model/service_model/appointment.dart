class Appointment {
  Appointment({
    required this.service_type,
    required this.license,
    required this.date_select,
    required this.time_select,
    required this.branch,
    required this.location_name,
    required this.latlng,
    required this.detail,
    required this.phone,
  });

  String service_type;
  String license;
  String date_select;
  String time_select;
  String branch;
  String location_name;
  String latlng;
  String detail;
  String phone;

  factory Appointment.fromJson(Map<String, dynamic> json) => Appointment(
    service_type: json["service_type"],
    license: json["license"],
    date_select: json["date_select"],
    time_select: json["time_select"],
    branch: json["branch"],
    location_name: json["location_name"],
    latlng: json["latlng"],
    detail: json["detail"],
    phone: json["phone"],
  );

  Map<String, dynamic> toJson() => {
    "service_type": service_type,
    "license": license,
    "date_select": date_select,
    "time_select": time_select,
    "branch": branch,
    "location_name": location_name,
    "latlng": latlng,
    "detail": detail,
    "phone": phone,
  };
}

class ResponseAppointment{
  ResponseAppointment({
    this.status,
  });

  int? status;

  factory ResponseAppointment.fromJson(Map<String, dynamic> json) => ResponseAppointment(
    status: json["status"],
  );

  Map<String, dynamic> toJson() => {
    "status": status,
  };
}

class ResponseCarReg {
  int? running;
  String? createTime;
  String? createUser;
  String? updateTime;
  String? updateUser;
  String? carUserPhone;
  String? carCharacter;
  String? carNumber;
  String? carProvince;
  String? carLicense;
  String? carEngineNo;

  ResponseCarReg({
    this.running,
    this.createTime,
    this.createUser,
    this.updateTime,
    this.updateUser,
    this.carUserPhone,
    this.carCharacter,
    this.carNumber,
    this.carProvince,
    this.carLicense,
    this.carEngineNo,
  });

  factory ResponseCarReg.fromJson(Map<String, dynamic> json) => ResponseCarReg(
    running: json["running"],
    createTime: json["create_time"],
    createUser: json["create_user"],
    updateTime: json["update_time"],
    updateUser: json["update_user"],
    carUserPhone: json["car_user_phone"],
    carCharacter: json["car_character"],
    carNumber: json["car_number"],
    carProvince: json["car_province"],
    carLicense: json["car_license"],
    carEngineNo: json["car_engine_no"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "create_time": createTime,
    "create_user": createUser,
    "update_time": updateTime,
    "update_user": updateUser,
    "car_user_phone": carUserPhone,
    "car_character": carCharacter,
    "car_number": carNumber,
    "car_province": carProvince,
    "car_license": carLicense,
    "car_engine_no": carEngineNo,
  };
}

class CarRegList {
  final List<ResponseCarReg>? data;

  CarRegList({this.data});

  factory CarRegList.fromJson(List<dynamic> parsedJson){
    List<ResponseCarReg>? data = <ResponseCarReg>[];
    data = parsedJson.map((i) => ResponseCarReg.fromJson(i)).toList();
    return CarRegList(data: data);
  }
}

