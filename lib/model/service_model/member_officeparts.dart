class MemberOfficeParts {
  MemberOfficeParts({
    required this.menu,
  });

  String menu;

  factory MemberOfficeParts.fromJson(Map<String, dynamic> json) => MemberOfficeParts(
    menu: json["menu"] == null ? null : json["menu"],
  );

  Map<String, dynamic> toJson() => {
    "menu": menu == null ? null : menu,
  };
}

class ResponseMemberOfficeParts {
  ResponseMemberOfficeParts({
    this.id,
    this.name,
    this.picture,
    this.phone,
    this.lineId,
    this.team,
  });

  int? id;
  String? name;
  String? picture;
  String? phone;
  String? lineId;
  String? team;

  factory ResponseMemberOfficeParts.fromJson(Map<String, dynamic> json) => ResponseMemberOfficeParts(
    id: json["part_id"] == null ? null : json["part_id"],
    name: json["part_name"] == null ? null : json["part_name"],
    picture: json["part_picture"] == null ? null : json["part_picture"],
    phone: json["part_phone"] == null ? null : json["part_phone"],
    lineId: json["part_line_id"] == null ? null : json["part_line_id"],
    team: json["part_team"] == null ? null : json["part_team"],
  );

  Map<String, dynamic> toJson() => {
    "part_id": id,
    "part_name": name,
    "part_picture": picture,
    "part_phone": phone,
    "part_line_id": lineId,
    "part_team": team,
  };
}

class MemberOfficePartsList {
  final List<ResponseMemberOfficeParts>? data;

  MemberOfficePartsList({this.data});

  factory MemberOfficePartsList.fromJson(List<dynamic> parsedJson){
    List<ResponseMemberOfficeParts>? data = <ResponseMemberOfficeParts>[];
    data = parsedJson.map((i) => ResponseMemberOfficeParts.fromJson(i)).toList();
    return MemberOfficePartsList(data: data);
  }
}
