class ClaimHistory {
  ClaimHistory({
    required this.phone,
  });

  String phone;

  factory ClaimHistory.fromJson(Map<String, dynamic> json) => ClaimHistory(
    phone: json["phone"] == null ? null : json["phone"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone == null ? null : phone,
  };
}

class ResponseClaimHistory {
  ResponseClaimHistory({
    this.jobId,
    this.jobNote,
    this.appraisersName,
    this.idSendCar,
    this.carReceiver,
    this.appStatus,
    this.carReg,
    this.createTime,
    this.isrCode,
    this.texId,
  });

  String? jobId;
  String? jobNote;
  String? appraisersName;
  String? idSendCar;
  dynamic carReceiver;
  String? appStatus;
  String? carReg;
  String? createTime;
  String? isrCode;
  String? texId;

  factory ResponseClaimHistory.fromJson(Map<String, dynamic> json) => ResponseClaimHistory(
    jobId: json["job_id"] == null ? null : json["job_id"],
    jobNote: json["job_note"] == null ? null : json["job_note"],
    appraisersName: json["appraisers_name"] == null ? null : json["appraisers_name"],
    idSendCar: json["id_send_car"] == null ? null : json["id_send_car"],
    carReceiver: json["Car_receiver"] == null ? null : json["Car_receiver"],
    appStatus: json["app_status"] == null ? null : json["app_status"],
    carReg: json["car_reg"] == null ? null : json["car_reg"],
    createTime: json["create_time"] == null ? null : json["create_time"],
    isrCode: json["isr_code"] == null ? null : json["isr_code"],
    texId: json["tex_id"] == null ? null : json["tex_id"],
  );

  Map<String, dynamic> toJson() => {
    "job_id": jobId,
    "job_note": jobNote,
    "appraisers_name": appraisersName,
    "id_send_car": idSendCar,
    "Car_receiver": carReceiver,
    "app_status": appStatus,
    "car_reg": carReg,
    "create_time": createTime,
    "isr_code": isrCode,
    "tex_id": texId,
  };
}

class ClaimHistoryList {
  final List<ResponseClaimHistory>? data;

  ClaimHistoryList({this.data});

  factory ClaimHistoryList.fromJson(List<dynamic> parsedJson){
    List<ResponseClaimHistory>? data = <ResponseClaimHistory>[];
    data = parsedJson.map((i) => ResponseClaimHistory.fromJson(i)).toList();
    return ClaimHistoryList(data: data);
  }
}