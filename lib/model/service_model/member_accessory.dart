class MemberAccessory {
  MemberAccessory({
    required this.menu,
  });

  String menu;

  factory MemberAccessory.fromJson(Map<String, dynamic> json) => MemberAccessory(
    menu: json["menu"] == null ? null : json["menu"],
  );

  Map<String, dynamic> toJson() => {
    "menu": menu == null ? null : menu,
  };
}

class ResponseMemberAccessory {
  ResponseMemberAccessory({
    this.id,
    this.name,
    this.picture,
    this.phone,
    this.lineId,
    this.team,
  });

  int? id;
  String? name;
  String? picture;
  String? phone;
  String? lineId;
  String? team;

  factory ResponseMemberAccessory.fromJson(Map<String, dynamic> json) => ResponseMemberAccessory(
    id: json["id"] == null ? null : json["id"],
    name: json["name"] == null ? null : json["name"],
    picture: json["pic"] == null ? null : json["pic"],
    phone: json["phone"] == null ? null : json["phone"],
    lineId: json["line_id"] == null ? null : json["line_id"],
    team: json["team"] == null ? null : json["team"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "pic": picture,
    "phone": phone,
    "line_id": lineId,
    "team": team,
  };
}

class MemberAccessoryList {
  final List<ResponseMemberAccessory>? data;

  MemberAccessoryList({this.data});

  factory MemberAccessoryList.fromJson(List<dynamic> parsedJson){
    List<ResponseMemberAccessory>? data = <ResponseMemberAccessory>[];
    data = parsedJson.map((i) => ResponseMemberAccessory.fromJson(i)).toList();
    return MemberAccessoryList(data: data);
  }
}
