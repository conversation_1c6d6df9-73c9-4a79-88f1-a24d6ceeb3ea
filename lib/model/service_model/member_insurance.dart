class MemberInsurance {
  MemberInsurance({
    required this.menu,
  });

  String menu;

  factory MemberInsurance.fromJson(Map<String, dynamic> json) => MemberInsurance(
    menu: json["menu"] == null ? null : json["menu"],
  );

  Map<String, dynamic> toJson() => {
    "menu": menu == null ? null : menu,
  };
}

class ResponseMemberInsurance {
  ResponseMemberInsurance({
    this.id,
    this.name,
    this.picture,
    this.phone,
    this.lineId,
    this.branch,
    this.team,
  });

  int? id;
  String? name;
  String? picture;
  String? phone;
  String? lineId;
  String? branch;
  String? team;

  factory ResponseMemberInsurance.fromJson(Map<String, dynamic> json) => ResponseMemberInsurance(
    id: json["id"] == null ? null : json["id"],
    name: json["name"] == null ? null : json["name"],
    picture: json["picture"] == null ? null : json["picture"],
    phone: json["phone"] == null ? null : json["phone"],
    lineId: json["line_id"] == null ? null : json["line_id"],
    branch: json["branch"] == null ? null : json["branch"],
    team: json["team"] == null ? null : json["team"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "picture": picture,
    "phone": phone,
    "line_id": lineId,
    "branch": branch,
    "team": team,
  };
}

class MemberInsuranceList {
  final List<ResponseMemberInsurance>? data;

  MemberInsuranceList({this.data});

  factory MemberInsuranceList.fromJson(List<dynamic> parsedJson){
    List<ResponseMemberInsurance>? data = <ResponseMemberInsurance>[];
    data = parsedJson.map((i) => ResponseMemberInsurance.fromJson(i)).toList();
    return MemberInsuranceList(data: data);
  }
}
