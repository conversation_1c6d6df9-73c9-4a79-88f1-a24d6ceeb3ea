class SC {
  SC({
    required this.branch,
  });

  String branch;

  factory SC.fromJson(Map<String, dynamic> json) => SC(
    branch: json["branch"] == null ? null : json["branch"],
  );

  Map<String, dynamic> toJson() => {
    "branch": branch == null ? null : branch,
  };
}

class ResponseSC {
  ResponseSC({
    this.status,
    required this.result,
  });

  int? status;
  Result? result;

  factory ResponseSC.fromJson(Map<String, dynamic> json) => ResponseSC(
      status: json["status"] == null ? null : json["status"],
      result: json["result"] == null ? null : Result.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "result": result!.toJson(),
  };
}

class Result {
  Result({
    this.saleId,
    this.saleMemberId,
    this.saleName,
    this.saleNickname,
    this.salePictureTemp,
    this.salePicture,
    this.salePictureBg,
    this.salePhone,
    this.saleLineId,
    this.saleFacebook,
    this.saleToken,
    this.saleProduct,
    this.saleBranch,
    this.saleBranchOrder,
    this.salePosition,
  });

  int? saleId;
  String? saleMemberId;
  String? saleName;
  String? saleNickname;
  String? salePictureTemp;
  String? salePicture;
  String? salePictureBg;
  String? salePhone;
  String? saleLineId;
  String? saleFacebook;
  String? saleToken;
  String? saleProduct;
  String? saleBranch;
  int? saleBranchOrder;
  int? salePosition;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
    saleId: json["sale_id"] == null ? null : json["sale_id"],
    saleMemberId: json["sale_member_id"] == null ? null : json["sale_member_id"],
    saleName: json["sale_name"] == null ? null : json["sale_name"],
    saleNickname: json["sale_nickname"] == null ? null : json["sale_nickname"],
    salePictureTemp: json["sale_picture_temp"] == null ? null : json["sale_picture_temp"],
    salePicture: json["sale_picture"] == null ? null : json["sale_picture"],
    salePictureBg: json["sale_picture_bg"] == null ? null : json["sale_picture_bg"],
    salePhone: json["sale_phone"] == null ? null : json["sale_phone"],
    saleLineId: json["sale_line_id"] == null ? null : json["sale_line_id"],
    saleFacebook: json["sale_facebook"] == null ? null : json["sale_facebook"],
    saleToken: json["sale_token"] == null ? null : json["sale_token"],
    saleProduct: json["sale_product"] == null ? null : json["sale_product"],
    saleBranch: json["sale_branch"] == null ? null : json["sale_branch"],
    saleBranchOrder: json["sale_branch_order"] == null ? null : json["sale_branch_order"],
    salePosition: json["sale_position"] == null ? null : json["sale_position"],
  );

  Map<String, dynamic> toJson() => {
    "sale_id": saleId,
    "sale_member_id": saleMemberId,
    "sale_name": saleName,
    "sale_nickname": saleNickname,
    "sale_picture_temp": salePictureTemp,
    "sale_picture": salePicture,
    "sale_picture_bg": salePictureBg,
    "sale_phone": salePhone,
    "sale_line_id": saleLineId,
    "sale_facebook": saleFacebook,
    "sale_token": saleToken,
    "sale_product": saleProduct,
    "sale_branch": saleBranch,
    "sale_branch_order": saleBranchOrder,
    "sale_position": salePosition,
  };
}

class SCList {
  final List<Result>? data;

  SCList({this.data});

  factory SCList.fromJson(List<dynamic> parsedJson){
    List<Result>? data = <Result>[];
    data = parsedJson.map((i) => Result.fromJson(i)).toList();
    return SCList(data: data);
  }
}
