class ClaimOnline {
  ClaimOnline({
    required this.fileOne,
    required this.fileTwo,
    required this.fileThree,
    required this.fileFour,
    required this.fileFive,
    required this.customerId,
    required this.sc,
    required this.car,
    required this.insurance,
    required this.phone,
    required this.idLine,
    required this.phoneSystem,
    required this.displayName,
  });

  String fileOne;
  String fileTwo;
  String fileThree;
  String fileFour;
  String fileFive;
  String customerId;
  String sc;
  String car;
  String insurance;
  String phone;
  String idLine;
  String phoneSystem;
  String displayName;

  factory ClaimOnline.fromJson(Map<String, dynamic> json) => ClaimOnline(
    fileOne: json["fileOne"] == null ? null : json["fileOne"],
    fileTwo: json["fileTwo"] == null ? null : json["fileTwo"],
    fileThree: json["fileThree"] == null ? null : json["fileThree"],
    fileFour: json["fileFour"] == null ? null : json["fileFour"],
    fileFive: json["fileFive"] == null ? null : json["fileFive"],
    customerId: json["customerId"] == null ? null : json["customerId"],
    sc: json["sc"] == null ? null : json["sc"],
    car: json["car"] == null ? null : json["car"],
    insurance: json["insurance"] == null ? null : json["insurance"],
    phone: json["phone"] == null ? null : json["phone"],
    idLine: json["idLine"] == null ? null : json["idLine"],
    phoneSystem: json["phoneSystem"] == null ? null : json["phoneSystem"],
    displayName: json["displayName"] == null ? null : json["displayName"],
  );

  Map<String, dynamic> toJson() => {
    "fileOne": fileOne == null ? null : fileOne,
    "fileTwo": fileTwo == null ? null : fileTwo,
    "fileThree": fileThree == null ? null : fileThree,
    "fileFour": fileFour == null ? null : fileFour,
    "fileFive": fileFive == null ? null : fileFive,
    "customerId": customerId == null ? null : customerId,
    "sc": sc == null ? null : sc,
    "car": car == null ? null : car,
    "insurance": insurance == null ? null : insurance,
    "phone": phone == null ? null : phone,
    "idLine": idLine == null ? null : idLine,
    "phoneSystem": phoneSystem == null ? null : phoneSystem,
    "displayName": displayName == null ? null : displayName,
  };
}

class ResponseClaimOnline{
  ResponseClaimOnline({
    this.status,
  });

  int? status;

  factory ResponseClaimOnline.fromJson(Map<String, dynamic> json) => ResponseClaimOnline(
    status: json["status"] == null ? null : json["status"],
  );

  Map<String, dynamic> toJson() => {
    "status": status == null ? null : status,
  };
}