class MemberPMG {
  MemberPMG({
    required this.menu,
  });

  String menu;

  factory MemberPMG.fromJson(Map<String, dynamic> json) => MemberPMG(
    menu: json["menu"] == null ? null : json["menu"],
  );

  Map<String, dynamic> toJson() => {
    "menu": menu == null ? null : menu,
  };
}

class ResponseMemberPMG {
  ResponseMemberPMG({
    this.id,
    this.name,
    this.picture,
    this.phone,
    this.lineId,
    this.team,
    this.branch
  });

  int? id;
  String? name;
  String? picture;
  String? phone;
  String? lineId;
  String? team;
  String? branch;

  factory ResponseMemberPMG.fromJson(Map<String, dynamic> json) => ResponseMemberPMG(
    id: json["C_id"] == null ? null : json["C_id"],
    name: json["C_name"] == null ? null : json["C_name"],
    picture: json["C_picture"] == null ? null : json["C_picture"],
    phone: json["C_phone"] == null ? null : json["C_phone"],
    lineId: json["C_line_id"] == null ? null : json["C_line_id"],
    team: json["C_team"] == null ? null : json["C_team"],
    branch: json["C_branch"] == null ? null : json["C_branch"],
  );

  Map<String, dynamic> toJson() => {
    "C_id": id,
    "C_name": name,
    "C_picture": picture,
    "C_phone": phone,
    "C_line_id": lineId,
    "C_team": team,
    "C_branch": team,
  };
}

class MemberPMGList {
  final List<ResponseMemberPMG>? data;

  MemberPMGList({this.data});

  factory MemberPMGList.fromJson(List<dynamic> parsedJson){
    List<ResponseMemberPMG>? data = <ResponseMemberPMG>[];
    data = parsedJson.map((i) => ResponseMemberPMG.fromJson(i)).toList();
    return MemberPMGList(data: data);
  }
}
