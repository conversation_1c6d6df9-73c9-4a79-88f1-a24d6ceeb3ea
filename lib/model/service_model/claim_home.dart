class ClaimHome {
  ClaimHome({
    required this.customerName,
    required this.location,
    required this.latlng,
    required this.file,
    required this.detail,
    required this.phone,
    required this.idLine,
    required this.phoneSystem,
    required this.customerId,
    required this.car,
    required this.insurance,
    required this.sc,
  });

  String customerName;
  String location;
  String latlng;
  String file;
  String detail;
  String customerId;
  String sc;
  String car;
  String insurance;
  String phone;
  String idLine;
  String phoneSystem;

  factory ClaimHome.fromJson(Map<String, dynamic> json) => ClaimHome(
    customerName: json["name_customer"] == null ? null : json["name_customer"],
    location: json["location_name"] == null ? null : json["location_name"],
    latlng: json["latlng"] == null ? null : json["latlng"],
    file: json["file"] == null ? null : json["file"],
    detail: json["detail"] == null ? null : json["detail"],
    customerId: json["customerId"] == null ? null : json["customerId"],
    sc: json["sc"] == null ? null : json["sc"],
    car: json["car"] == null ? null : json["car"],
    insurance: json["insurance"] == null ? null : json["insurance"],
    phone: json["phone"] == null ? null : json["phone"],
    idLine: json["idLine"] == null ? null : json["idLine"],
    phoneSystem: json["phoneSystem"] == null ? null : json["phoneSystem"],
  );

  Map<String, dynamic> toJson() => {
    "name_customer": customerName == null ? null : customerName,
    "location_name": location == null ? null : location,
    "latlng": latlng == null ? null : latlng,
    "file": file == null ? null : file,
    "detail": detail == null ? null : detail,
    "customerId": customerId == null ? null : customerId,
    "sc": sc == null ? null : sc,
    "car": car == null ? null : car,
    "insurance": insurance == null ? null : insurance,
    "phone": phone == null ? null : phone,
    "idLine": idLine == null ? null : idLine,
    "phoneSystem": phoneSystem == null ? null : phoneSystem,
  };
}

class ResponseClaimHome{
  ResponseClaimHome({
    this.status,
  });

  int? status;

  factory ResponseClaimHome.fromJson(Map<String, dynamic> json) => ResponseClaimHome(
    status: json["status"] == null ? null : json["status"],
  );

  Map<String, dynamic> toJson() => {
    "status": status == null ? null : status,
  };
}