class SA {
  SA({
    required this.branch,
  });

  String branch;

  factory SA.fromJson(Map<String, dynamic> json) => SA(
    branch: json["branch"] == null ? null : json["branch"],
  );

  Map<String, dynamic> toJson() => {
    "branch": branch == null ? null : branch,
  };
}

class ResponseSA {
  ResponseSA({
    this.status,
    required this.result,
  });

  int? status;
  Result? result;

  factory ResponseSA.fromJson(Map<String, dynamic> json) => ResponseSA(
    status: json["status"] == null ? null : json["status"],
    result: json["result"] == null ? null : Result.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "result": result!.toJson(),
  };
}

class Result {
  Result({
    this.id,
    this.name,
    this.pic,
    this.phone,
    this.lineId,
    this.branch,
    this.team,
  });

  int? id;
  String? name;
  String? pic;
  String? phone;
  String? lineId;
  String? branch;
  String? team;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
    id: json["id"] == null ? null : json["id"],
    name: json["name"] == null ? null : json["name"],
    pic: json["pic"] == null ? null : json["pic"],
    phone: json["phone"] == null ? null : json["phone"],
    lineId: json["line_id"] == null ? null : json["line_id"],
    branch: json["branch"] == null ? null : json["branch"],
    team: json["team"] == null ? null : json["team"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "pic": pic,
    "phone": phone,
    "line_id": lineId,
    "branch": branch,
    "team": team,
  };
}

class SAList {
  final List<Result>? data;

  SAList({this.data});

  factory SAList.fromJson(List<dynamic> parsedJson){
    List<Result>? data = <Result>[];
    data = parsedJson.map((i) => Result.fromJson(i)).toList();
    return SAList(data: data);
  }
}
