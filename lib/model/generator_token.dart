class GenToken {
  GenToken({
    required this.userId,
    required this.appId,
    required this.roleId,
  });

  String userId;
  String appId;
  String roleId;

  factory GenToken.fromJson(Map<String, dynamic> json) => GenToken(
    userId: json["userId"] == null ? null : json["userId"],
    appId: json["appId"] == null ? null : json["appId"],
    roleId: json["roleId"] == null ? null : json["roleId"],
  );

  Map<String, dynamic> toJson() => {
    "userId": userId == null ? null : userId,
    "appId": appId == null ? null : appId,
    "roleId": roleId == null ? null : roleId,
  };
}

class ResponseGenToken {
  ResponseGenToken({
    this.status,
    this.successfullFlag,
    this.userId,
    this.msg,
    this.accessToken,
  });

  int? status;
  String? successfullFlag;
  String? userId;
  String? msg;
  String? accessToken;


  factory ResponseGenToken.fromJson(Map<String, dynamic> json) => ResponseGenToken(
    status: json["status"] == null ? null : json["status"],
    successfullFlag: json["result"]["successfullFlag"] == null ? null : json["result"]["successfullFlag"],
    userId: json["result"]["userId"] == null ? null : json["result"]["userId"],
    msg: json["result"]["msg"] == null ? null : json["result"]["msg"],
    accessToken: json["result"]["accessToken"] == null ? null : json["result"]["accessToken"],
  );

  Map<String, dynamic> toJson() => {
    "status": status == null ? null : status,
    "successfullFlag": successfullFlag == null ? null : successfullFlag,
    "userId": userId == null ? null : userId,
    "msg": msg == null ? null : msg,
    "accessToken": accessToken == null ? null : accessToken,
  };
}
