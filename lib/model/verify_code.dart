class VerifyCode {
  VerifyCode({
    required this.phone,
    required this.otpCode,
    required this.refCode,
    required this.fromBU,
  });

  String phone;
  String otpCode;
  String refCode;
  String fromBU;

  factory VerifyCode.fromJson(Map<String, dynamic> json) => VerifyCode(
    phone: json["phone"],
    otpCode: json["otpCode"],
    refCode: json["refCode"],
    fromBU: json["fromBU"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone,
    "otpCode": otpCode,
    "refCode": refCode,
    "fromBU": fromBU,
  };
}

class ResponseVerifyCode {
  ResponseVerifyCode({
    this.statusCode,
    this.msg,
    this.likewallet,
  });

  int? statusCode;
  String? msg;
  Likewallet? likewallet;

  factory ResponseVerifyCode.fromJson(Map<String, dynamic> json) => ResponseVerifyCode(
    statusCode: json["statusCode"],
    msg: json["msg"],
    likewallet: json["likewallet"] == null ? null : Likewallet.fromJson(json["likewallet"])
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode,
    "msg": msg,
    "likewallet": likewallet?.toJson(),
  };
}

class Likewallet {
  Likewallet({
    this.statusCode,
    this.result,
  });

  int? statusCode;
  String? result;

  factory Likewallet.fromJson(Map<String, dynamic> json) => Likewallet(
    statusCode: json["statusCode"],
    result: json["result"],
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode,
    "result": result,
  };
}
