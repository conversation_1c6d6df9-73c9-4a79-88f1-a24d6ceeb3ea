class ResponseProvince {
  ResponseProvince({
    this.id,
    this.codeTemp,
    this.nameTh,
    this.nameEn,
    this.geographyId,
  });

  int? id;
  String? codeTemp;
  String? nameTh;
  String? nameEn;
  int? geographyId;

  factory ResponseProvince.fromJson(Map<String, dynamic> json) => ResponseProvince(
    id: json["id"] == null ? null : json["id"],
    codeTemp: json["code_temp"] == null ? null : json["code_temp"],
    nameTh: json["name_th"] == null ? null : json["name_th"],
    nameEn: json["name_en"] == null ? null : json["name_en"],
    geographyId: json["geography_id"] == null ? null : json["geography_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "code_temp": codeTemp,
    "name_th": nameTh,
    "name_en": nameEn,
    "geography_id": geographyId,
  };
}

class ProvinceList {
  final List<ResponseProvince>? data;

  ProvinceList({this.data});

  factory ProvinceList.fromJson(List<dynamic> parsedJson){
    List<ResponseProvince>? data = <ResponseProvince>[];
    data = parsedJson.map((i) => ResponseProvince.fromJson(i)).toList();
    return ProvinceList(data: data);
  }
}