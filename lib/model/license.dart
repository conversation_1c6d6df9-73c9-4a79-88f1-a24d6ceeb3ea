class SaveLicense {
  SaveLicense({
    required this.phone,
    required this.create_user,
    required this.character,
    required this.number,
    required this.province,
    required this.car_license,
    required this.car_engine_no,
  });

  String phone;
  String create_user;
  String character;
  String number;
  String province;
  String car_license;
  String car_engine_no;

  factory SaveLicense.fromJson(Map<String, dynamic> json) => SaveLicense(
    phone: json["phone"] == null ? null : json["phone"],
    create_user: json["create_user"] == null ? null : json["create_user"],
    character: json["character"] == null ? null : json["character"],
    number: json["number"] == null ? null : json["number"],
    province: json["province"] == null ? null : json["province"],
    car_license: json["car_license"] == null ? null : json["car_license"],
    car_engine_no: json["car_engine_no"] == null ? null : json["car_engine_no"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone == null ? null : phone,
    "create_user": create_user == null ? null : create_user,
    "character": character == null ? null : character,
    "number": number == null ? null : number,
    "province": province == null ? null : province,
    "car_license": car_license == null ? null : car_license,
    "car_engine_no": car_engine_no == null ? null : car_engine_no,
  };
}

class ResponseAddLicense {
  ResponseAddLicense({
    this.status,
  });

  int? status;
  factory ResponseAddLicense.fromJson(Map<String, dynamic> json) => ResponseAddLicense(
    status: json["status"] == null ? null : json["status"],
  );

  Map<String, dynamic> toJson() => {
    "status": status,
  };
}

class GetLicense {
  GetLicense({
    required this.phone,
  });

  String phone;

  factory GetLicense.fromJson(Map<String, dynamic> json) => GetLicense(
    phone: json["phone"] == null ? null : json["phone"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone == null ? null : phone,
  };
}

class ResponseGetLicense {
  ResponseGetLicense({
    this.running,
    this.createTime,
    this.createUser,
    this.updateTime,
    this.updateUser,
    this.carUserPhone,
    this.carCharacter,
    this.carNumber,
    this.carProvince,
    this.carLicense,
    this.carEngineNo,
  });

  int? running;
  String? createTime;
  String? createUser;
  String? updateTime;
  String? updateUser;
  String? carUserPhone;
  String? carCharacter;
  String? carNumber;
  String? carProvince;
  String? carLicense;
  String? carEngineNo;

  factory ResponseGetLicense.fromJson(Map<String, dynamic> json) => ResponseGetLicense(
    running: json["running"] == null ? null : json["running"],
    createTime: json["create_time"] == null ? null : json["create_time"],
    createUser: json["create_user"] == null ? null : json["create_user"],
    updateTime: json["update_time"] == null ? null : json["update_time"],
    updateUser: json["update_user"] == null ? null : json["update_user"],
    carUserPhone: json["car_user_phone"] == null ? null : json["car_user_phone"],
    carCharacter: json["car_character"] == null ? null : json["car_character"],
    carNumber: json["car_number"] == null ? null : json["car_number"],
    carProvince: json["car_province"] == null ? null : json["car_province"],
    carLicense: json["car_license"] == null ? null : json["car_license"],
    carEngineNo: json["car_engine_no"] == null ? null : json["car_engine_no"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "create_time": createTime,
    "create_user": createUser,
    "update_time": updateTime,
    "update_user": updateUser,
    "car_user_phone": carUserPhone,
    "car_character": carCharacter,
    "car_number": carNumber,
    "car_province": carProvince,
    "car_license": carLicense,
    "car_engine_no": carEngineNo,
  };
}

class carLicenseList {
  final List<ResponseGetLicense>? data;

  carLicenseList({this.data});

  factory carLicenseList.fromJson(List<dynamic> parsedJson){
    List<ResponseGetLicense>? data = <ResponseGetLicense>[];
    data = parsedJson.map((i) => ResponseGetLicense.fromJson(i)).toList();
    return carLicenseList(data: data);
  }
}
