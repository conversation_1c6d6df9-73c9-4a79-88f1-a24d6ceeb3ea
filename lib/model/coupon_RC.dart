class ConponRcModel {
  int? running;
  String? phoneOwner;
  String? phoneGet;
  String? status;
  String? point;
  String? expire_date;
  String? car_owner_name;
  String? register_license;

  ConponRcModel({
    this.running,
    this.phoneOwner,
    this.phoneGet,
    this.status,
    this.point,
    this.expire_date,
    this.car_owner_name,
    this.register_license,
  });

  factory ConponRcModel.fromJson(Map<String, dynamic> json) {
    return ConponRcModel(
      running: json['running'] ?? 0,
      phoneOwner: json['phoneOwner'] ??"",
      phoneGet: json['phoneGet'] ??"",
      status: json['status'] ??"",
      point: json['point'] ??"",
      expire_date: json['expire_date'] ??"",
      car_owner_name: json['car_owner_name'] ??"",
      register_license: json['register_license'] ??"",
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'phoneOwner': phoneOwner,
      'phoneGet': phoneGet,
      'status': status,
      'point': point,
      'expire_date': expire_date,
      'car_owner_name': car_owner_name,
      'register_license': register_license,
    };
  }
}

class ConponRcList {
  final List<ConponRcModel>? data;

  ConponRcList({this.data});

  factory ConponRcList.fromJson(List<dynamic> parsedJson){
    List<ConponRcModel>? data = <ConponRcModel>[];
    data = parsedJson.map((i) => ConponRcModel.fromJson(i)).toList();
    return ConponRcList(data: data);
  }
}