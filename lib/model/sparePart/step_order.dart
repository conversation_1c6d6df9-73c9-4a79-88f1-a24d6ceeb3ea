import 'dart:convert';

List<StepOrder> stepOrderFromJson(String str) => List<StepOrder>.from(json.decode(str).map((x) => StepOrder.fromJson(x)));

String stepOrderToJson(List<StepOrder> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class StepOrder {
  String? status;
  String? icon;
  String? text;
  String? sub;

  StepOrder({
    this.status,
    this.icon,
    this.text,
    this.sub,
  });

  factory StepOrder.fromJson(Map<String, dynamic> json) => StepOrder(
    status: json["status"],
    icon: json["icon"],
    text: json["text"],
    sub: json["sub"],
  );

  Map<String, dynamic> toJson() => {
    "status": status,
    "icon": icon,
    "text": text,
    "sub": sub,
  };
}
