import 'dart:convert';

List<BagProduction> bagProductionFromJson(String str) => List<BagProduction>.from(json.decode(str).map((x) => BagProduction.fromJson(x)));

String bagProductionToJson(List<BagProduction> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BagProduction {
  String? type;
  String? subtype;
  int? index;
  int? stock;
  String? name;
  int? amount;
  bool? checked;

  BagProduction({
    this.type,
    this.subtype,
    this.index,
    this.stock,
    this.name,
    this.amount,
    this.checked,
  });

  factory BagProduction.fromJson(Map<String, dynamic> json) => BagProduction(
    type: json["type"],
    subtype: json["subtype"],
    index: json["index"],
    stock: json["stock"],
    name: json["name"],
    amount: json["amount"],
    checked: json["checked"],
  );

  Map<String, dynamic> toJson() => {
    "type": type,
    "subtype": subtype,
    "index": index,
    "stock": stock,
    "name": name,
    "amount": amount,
    "checked": checked,
  };
}


ShowProduction showProductionFromJson(String str) => ShowProduction.fromJson(json.decode(str));

String showProductionToJson(ShowProduction data) => json.encode(data.toJson());

class ShowProduction {
  String? type;
  String? subtype;
  int? index;
  int? stock;
  String? name;

  ShowProduction({
    this.type,
    this.subtype,
    this.index,
    this.stock,
    this.name,
  });

  factory ShowProduction.fromJson(Map<String, dynamic> json) => ShowProduction(
    type: json["type"],
    subtype: json["subtype"],
    index: json["index"],
    stock: json["stock"],
    name: json["name"],
  );

  Map<String, dynamic> toJson() => {
    "type": type,
    "subtype": subtype,
    "index": index,
    "stock": stock,
    "name": name,
  };
}
