class ProvinceModel {
  int? address_id;
  int? member_id;
  String? address_line;
  String? sub_district;
  String? district;
  String? province;
  String? postal_code;
  String? country;
  String? fullname;
  int? type_default;


  ProvinceModel({
    this.address_id,
    this.member_id,
    this.address_line,
    this.sub_district,
    this.district,
    this.province,
    this.postal_code,
    this.country,
    this.fullname,
    this.type_default
  });

  factory ProvinceModel.fromJson(Map<String, dynamic> json) {
    return ProvinceModel(
      address_id: json['address_id'],
      member_id: json['member_id'],
      address_line: json['address_line'],
      sub_district: json['sub_district'],
      district: json['district'],
      province: json['province'],
      postal_code: json['postal_code'],
      country: json['country'],
      fullname: json['fullname'],
      type_default: json['type_default']
    );
  }

  Map<String, dynamic> toJson() => {
    'address_id': address_id,
    'member_id': member_id,
    'address_line': address_line,
    'sub_district': sub_district,
    'district': district,
    'province': province,
    'postal_code': postal_code,
    'country': country,
    'fullname': fullname,
    'type_default': type_default
  };
}

class ProvinceList {
  final List<ProvinceModel>? data;

  ProvinceList({this.data});

  factory ProvinceList.fromJson(List<dynamic> parsedJson){
    List<ProvinceModel>? data = <ProvinceModel>[];
    data = parsedJson.map((i) => ProvinceModel.fromJson(i)).toList();
    return ProvinceList(data: data);
  }
}