import 'dart:convert';

ResCarRepairStatus resCarRepairStatusFromJson(String str) => ResCarRepairStatus.fromJson(json.decode(str));

String resCarRepairStatusToJson(ResCarRepairStatus data) => json.encode(data.toJson());

class ResCarRepairStatus {
  int? userId;
  int? running;
  String? name;
  String? phone;
  String? reg;
  String? machineNumber;
  String? status;
  String? colorCode;
  String? colorName;
  String? carCode;
  String? carModel;
  String? carModelSa;
  String? carType;
  String? carImage;
  String? branch;
  String? partnerTxnUid;
  String? datePayment;
  dynamic amount;
  String? lift;
  String? camera;
  String? timeRepair;
  String? detailVHC;
  String? typePay;

  ResCarRepairStatus({
    this.userId,
    this.running,
    this.name,
    this.phone,
    this.reg,
    this.machineNumber,
    this.status,
    this.colorCode,
    this.colorName,
    this.carCode,
    this.carModel,
    this.carModelSa,
    this.carType,
    this.carImage,
    this.branch,
    this.partnerTxnUid,
    this.datePayment,
    this.amount,
    this.lift,
    this.camera,
    this.timeRepair,
    this.detailVHC,
    this.typePay,
  });

  factory ResCarRepairStatus.fromJson(Map<String, dynamic> json) => ResCarRepairStatus(
    userId: json["userID"] ?? "",
    running: json["running"] ?? "",
    name: json["name"] ?? "",
    phone: json["phone"] ?? "",
    reg: json["reg"] ?? "",
    machineNumber: json["machine_number"] ?? "",
    status: json["status"] ?? "",
    colorCode: json["colorCode"] ?? "",
    colorName: json["colorName"] ?? "",
    carCode: json["carCode"] ?? "",
    carModel: json["car_model"] ?? "",
    carModelSa: json["car_model_sa"] ?? "",
    carType: json["car_type"] ?? "",
    carImage: json["car_image"] ?? "",
    branch: json["branch"] ?? "",
    partnerTxnUid: json["partnerTxnUid"] ?? "",
    datePayment: json["date_payment"] ?? "",
    amount: json["amount"] ?? 0,
    lift: json["lift"] ?? "",
    camera: json["camera"] ?? "",
    timeRepair: json["timeRepair"] ?? "",
    detailVHC: json["detail_vhc"] ?? "",
    typePay: json["type_pay"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "userID": userId,
    "running": running,
    "name": name,
    "phone": phone,
    "reg": reg,
    "machine_number": machineNumber,
    "status": status,
    "colorCode": colorCode,
    "colorName": colorName,
    "carCode": carCode,
    "car_model": carModel,
    "car_model_sa": carModelSa,
    "car_type": carType,
    "car_image": carImage,
    "branch": branch,
    "partnerTxnUid": partnerTxnUid,
    "date_payment": datePayment,
    "amount": amount,
    "lift": lift,
    "camera": camera,
    "time_repair": timeRepair,
    "detail_vhc": detailVHC,
    "type_pay": typePay,
  };
}