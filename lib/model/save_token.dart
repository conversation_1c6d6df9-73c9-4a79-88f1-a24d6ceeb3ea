class SaveToken {
  SaveToken({
    required this.phone,
    required this.token,
  });

  String phone;
  String token;

  factory SaveToken.fromJson(Map<String, dynamic> json) => SaveToken(
    phone: json["phone"] == null ? null : json["phone"],
    token: json["token"] == null ? null : json["token"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone == null ? null : phone,
    "token": token == null ? null : token
  };
}

class ResponseSaveToken {
  ResponseSaveToken({
    this.statusCode,
    this.refCode,
  });

  int? statusCode;
  String? refCode;

  factory ResponseSaveToken.fromJson(Map<String, dynamic> json) => ResponseSaveToken(
    statusCode: json["statusCode"] == null ? null : json["statusCode"],
    refCode: json["refCode"] == null ? null : json["refCode"],
  );

  Map<String, dynamic> toJson() => {
    "statusCode": statusCode == null ? null : statusCode,
    "refCode": refCode == null ? null : refCode
  };
}
