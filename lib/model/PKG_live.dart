class ResponsePkgLive {
  ResponsePkgLive({
    this.running,
    this.updateTime,
    this.updateUser,
    this.createTime,
    this.createUser,
    this.rowSpreadsheet,
    this.name,
    this.status,
    this.primaryImg,
    this.linkVideo,
  });

  int? running;
  String? updateTime;
  String? updateUser;
  String? createTime;
  String? createUser;
  String? rowSpreadsheet;
  String? name;
  String? status;
  String? primaryImg;
  String? linkVideo;

  factory ResponsePkgLive.fromJson(Map<String, dynamic> json) => ResponsePkgLive(
    running: json["running"] == null ? null : json["running"],
    updateTime: json["update_time"] == null ? null : json["update_time"],
    updateUser: json["update_user"] == null ? null : json["update_user"],
    createTime: json["create_time"] == null ? null : json["create_time"],
    createUser: json["create_user"] == null ? null : json["create_user"],
    rowSpreadsheet: json["row_spreadsheet"] == null ? null : json["row_spreadsheet"],
    name: json["name"] == null ? null : json["name"],
    status: json["status"] == null ? null : json["status"],
    primaryImg: json["primary_img"] == null ? null : json["primary_img"],
    linkVideo: json["link_video"] == null ? null : json["link_video"],
  );

  Map<String, dynamic> toJson() => {
    "running": running,
    "update_time": updateTime,
    "update_user": updateUser,
    "create_time": createTime,
    "create_user": createUser,
    "row_spreadsheet": rowSpreadsheet,
    "name": name,
    "status": status,
    "primary_img": primaryImg,
    "link_video": linkVideo,
  };
}

class PKGLiveList {
  final List<ResponsePkgLive>? data;

  PKGLiveList({this.data});

  factory PKGLiveList.fromJson(List<dynamic> parsedJson){
    List<ResponsePkgLive>? data = <ResponsePkgLive>[];
    data = parsedJson.map((i) => ResponsePkgLive.fromJson(i)).toList();
    return PKGLiveList(data: data);
  }
}