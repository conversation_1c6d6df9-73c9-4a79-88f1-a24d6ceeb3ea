class Agreement {
  Agreement({
    required this.phone,
  });

  String phone;

  factory Agreement.fromJson(Map<String, dynamic> json) => Agreement(
    phone: json["phone"] == null ? null : json["phone"],
  );

  Map<String, dynamic> toJson() => {
    "phone": phone == null ? null : phone,
  };
}

class ResponseAgreement {
  ResponseAgreement({
    this.mobile,
    this.acceptAgreement,
    this.idcard,
  });

  String? mobile;
  String? acceptAgreement;
  String? idcard;

  factory ResponseAgreement.fromJson(Map<String, dynamic> json) => ResponseAgreement(
    mobile: json["mobile"],
    acceptAgreement: json["accept_agreement"],
    idcard: json["idcard"],
  );

  Map<String, dynamic> toJson() => {
    "mobile": mobile,
    "accept_agreement": acceptAgreement,
    "idcard": idcard,
  };
}

