import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' ;
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/intro.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:rive/rive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart' as flutter;
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';

import 'controller/ai_tutorial_controller.dart';


class IndexPage extends StatefulWidget {
  const IndexPage({Key? key}) : super(key: key);

  @override
  State<IndexPage> createState() => _IndexPageState();
}



class _IndexPageState extends State<IndexPage> {
  final SecureStorage secureStorage = SecureStorage();

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  String? uid;
  bool? loginStatus;
  bool? firstUse;

  getLogin() async {
    try {
      await SharedPreferences.getInstance();
      loginStatus = await AppService.getPref('bool', 'loginStatus');
      firstUse = await AppService.getPref('bool', 'firstUse');
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  final profileCtl = Get.put(ProfileController(), permanent: true);
  final loginCtl = Get.put(LoginController(), permanent: true);
  final pageCtl = Get.put(PageSelectController(), permanent: true);
  final centerCtl = Get.put(SettingController(), permanent: true);
  final likePointCtl = Get.put(LikePointController(), permanent: true);
  final eventCtl = Get.put(EventAndNewsController(), permanent: true);
  final promotionCtl = Get.put(PromotionController(), permanent: true);
  final psiCtl = Get.put(PsiController(), permanent: true);
  final tutorialCtl = Get.put(TutorialController(), permanent: true);
  final aiTutorialCtl = Get.put(AiTutorialController(), permanent: true);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Future.delayed(
        const Duration(milliseconds: 0),
            () async {
          await getLogin();
          await eventCtl.getEvent();
          await promotionCtl.getURL();
          await promotionCtl.getPromotion();
          await psiCtl.getCarOwner();
          // psiCtl.getCarOwner();
          if(loginStatus == false && firstUse == true ) {
            Get.offAll(() => const IntroPage());
          }
          Get.offAll(() => const HomeNavigator());
        }
    );
    analytics.setCurrentScreen(screenName: "Index");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: const Color(0xFFFFB200),
          ),
          Center(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.only(
                    top: 157,
                  ),
                  child: flutter.Image.asset('assets/image/logo.png',
                    height: 163,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(
                    top: 140,
                  ),
                  height: 118,
                  child: const RiveAnimation.asset('assets/animatedlogo/Chang-running.riv'),
                ),
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    Row(
                      children: const <Widget>[
                        Text(
                          'ISUZU',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                            letterSpacing: 1,
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(
                          'PRACHAKIJ',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: Colors.white,
                            fontSize: 12,
                            letterSpacing: 1.5,
                          ),
                        )
                      ],
                    )
                  ],
                ),
                const SizedBox(
                    height: 50
                )
              ],
            ),
          ),

        ],
      ),
    );
  }
}