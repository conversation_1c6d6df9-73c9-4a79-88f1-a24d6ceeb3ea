import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;


class AppVersion {
  static String getOS() {
    String os = Platform.operatingSystem;

    return os;
  }

  // static  getCurrentVersion() async {
  //   String version = "1.0.0";
  //   return version;
  // }
  // static  getCurrentVersion() async {
  //   PackageInfo packageInfo = await PackageInfo.fromPlatform();
  //
  //   String appName = packageInfo.appName;
  //   String packageName = packageInfo.packageName;
  //   String version = packageInfo.version;
  //   String buildNumber = packageInfo.buildNumber;
  //
  //   return version;
  // }

  // static initPackageInfo() async {
  //   final PackageInfo info = await PackageInfo.fromPlatform();
  //
  //   String appVersion = "";
  //
  //   PackageInfo packageInfo = PackageInfo(
  //     appName: 'Unknown',
  //     packageName: 'Unknown',
  //     version: 'Unknown',
  //     buildNumber: 'Unknown',
  //   );
  //
  //   packageInfo = info;
  //   appVersion = packageInfo.version;
  //   return appVersion;
  // }


  // static checkVersionStore() async {
  //   String os = getOS();
  //   String newVersion ="";
  //   if(os == "android"){
  //     final getVersion = AppVersionChecker(appId: "com.prachakij.pms_app");
  //     await Future.wait([
  //       getVersion
  //           .checkUpdate()
  //           .then((value) => newVersion = value.newVersion.toString()),
  //     ]);
  //   }
  //   else if (os == 'ios'){
  //     String url = "http://itunes.apple.com/lookup?id=1507877291";
  //     final response = await http.get(Uri.parse(url));
  //     Map<String, dynamic> data = json.decode(response.body);
  //     newVersion = data['results'][0]['version'];
  //   }
  //   return newVersion;
  // }
}