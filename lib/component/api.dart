import 'dart:convert';
import 'dart:io';

import 'package:ags_authrest2/ags_authrest.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:http/http.dart' as http;

class AppApi {
  static Future<SecurityContext> get globalContext async {
    // Note: Not allowed to load the same certificate
    final sslCert1 = await rootBundle.load('assets/cert/certificate.pem');
    // final sslCert2 = await rootBundle.load('assets/cert/certificate2.pem');

    SecurityContext sc = SecurityContext(withTrustedRoots: false);
    sc.setTrustedCertificatesBytes(sslCert1.buffer.asInt8List());
    // sc.setTrustedCertificatesBytes(sslCert2.buffer.asInt8List());
    return sc;
  }

  static const String hcsUrl = "https://68wtlqk0s2.execute-api.ap-southeast-1.amazonaws.com/dev/submitMessageTopic";
  static const String hcsKey = "sCzIzOuTio8LOr9OigCJj4nfuwxWudmQ4WCvyfiP";

  static callDocker(
      String url,
      path,
      port,
      Map jsonMap,
      ) async {
    String username = 'ags-ci';
    String password = 'cdWmW,o5HlNPd}gzEZlkbzCJ(zDtP)';

    HttpClient httpClient = HttpClient();
    httpClient.addCredentials(
        Uri.parse(url), "", HttpClientBasicCredentials(username, password));
    HttpClientRequest request = await httpClient.postUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    request.headers.set('path', path.toString());
    request.headers.set('port', port);
    request.add(utf8.encode(json.encode(jsonMap)));
    HttpClientResponse response = await request.close();
    // todo - you should check the response.statusCode
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static post(String url, Map jsonMap) async {
    final SecureStorage secureStorage = SecureStorage();
    var token = await secureStorage.readSecureData("accessToken");
    var urlApi = Uri.parse(url);
    final body = jsonMap;

    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky",
      'Authorization': token ?? "none",
    };
    var response =
    await http.post(urlApi, headers: requestHeaders, body: jsonEncode(jsonMap));
    var res = json.decode(response.body);
    return res;
  }

  static postNormal(String url, Map jsonMap) async {
    var urlApi = Uri.parse(url);
    Map<String,String> requestHeaders = {
      'Content-Type':'application/json',
      'x-api-key': 'FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky'
    };
    var response =
    await http.post(urlApi, headers: requestHeaders, body: jsonEncode(jsonMap));
    var res = json.decode(response.body);
    return res;
  }

  static Future<dynamic> get(String url) async {
    final SecureStorage secureStorage = SecureStorage();
    var token = await secureStorage.readSecureData("accessToken");
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "FIF12OExFj6uoE83hskYI2t03o8WSVF53H3vu9ky",
      'Authorization': token ?? "none",
    };
    var response = await http.get(urlApi, headers: requestHeaders);
    if (response.statusCode == 200) {
      var res = json.decode(response.body);
      return res;
    } else {
      throw Exception('Failed to load data');
    }
  }


  static getNormal(String url) async {
    HttpClient httpClient = HttpClient();
    HttpClientRequest request = await httpClient.getUrl(Uri.parse(url));
    request.headers.set('content-type', 'application/json');
    HttpClientResponse response = await request.close();
    String reply = await response.transform(utf8.decoder).join();
    httpClient.close();
    return json.decode(reply);
  }

  static getEventRegisterByID(context, idActivity) async {
    try{
      Map data = {
        "activity_ID" : idActivity
      };
      final response = await AppApi.post(AppUrl.getEventRegisterByID, data);
      if(response["status"] == 200){
        AppLoader.dismiss(context);
        return response;
      } else if(response["status"] == 403){
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "ไม่พบกิจกรรม", "กรุณาลองใหม่อีกครั้งหรือติดต่อพนักงาน", "ตกลง");
      }
    }catch(e){
      if (kDebugMode) {
        print("$e ==> Error getEventRegisterByID");
      }
    }
  }

  static Future<dynamic> callAPIjwt(String method, String url, Map data) async{
    try{
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'DZp#XbB3%#qs6I94';
      auth.R_USER = 'MAPP_PMS';
      var headers = {
        'Authorization':
        auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      ///'https://agilesoftgroup.com'
      var request = http.Request(method, Uri.parse(url));
      request.body = json.encode(auth.encrypbody(body));
      ///encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);
      return responseData;
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static Future<dynamic> callAPIAnotherWorld(String method, String url, Map data) async{
    try{
      var auth = Ags_restauth();
      auth.SECERT_JWT = 'XPEHsqqm0R5sO9cHkpOMaNEn';
      auth.R_USER = 'FCS_service';
      var headers = {
        'Authorization':
        auth.genTokenEncryp(), // genTokenEncryp() or genToken()
        'Content-Type': 'application/json'
      };
      var body = json.encode(data);
      ///'https://agilesoftgroup.com'
      var request = http.Request(method, Uri.parse(url));
      request.body = json.encode(auth.encrypbody(body));
      ///encrypbody require genTokenEncryp()
      request.headers.addAll(headers);
      http.StreamedResponse response = await request.send();
      var dataRaw = await response.stream.bytesToString();
      var responseData = json.decode(dataRaw);
      return responseData;
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
      return false;
    }
  }

  static postLikePoint(String url, Map jsonMap) async {
    var urlApi = Uri.parse(url);
    Map<String, String> requestHeaders = {
      'content-type': 'application/json',
      'x-api-key': "841JEGYD93NOCDXWKx6ra8NMDw00Pzvu",
    };
    var response =
    await http.post(urlApi, headers: requestHeaders, body: jsonEncode(jsonMap));
    var res = json.decode(response.body);
    return res;
  }

}