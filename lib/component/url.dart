class AppUrl {
  static String apiLink =
      // 'https://4hqtnu10id.execute-api.ap-southeast-1.amazonaws.com/latest'; //api ที่ทำเอง อยู่บน lambda
  'https://dw3xxotzc8.execute-api.ap-southeast-1.amazonaws.com/latest'; //api ที่ทำเอง อยู่บน lambda

  // static String apiLink = 'http://172.16.1.166:5000'; // local

  static String apiCenterLink =
      'https://oxphgjyvu2.execute-api.ap-southeast-1.amazonaws.com/latest'; //api ส่วนกลางทีมแอพ ที่เกมส์ทำ

  static String dockerLink = 'https://api.agsdocker.com';

  static String searchElastic =
      'https://8c5ro9jc1i.execute-api.ap-southeast-1.amazonaws.com/latest/callES/searchES';

  static String apiToken =
      'https://y38cw14k1b.execute-api.ap-southeast-1.amazonaws.com/prod/auth';

  static String saveFeedback = '$apiLink/saveFeedback';

  static String loginUsername = '$apiCenterLink/login_username';
  static String loginFacebook = '$apiCenterLink/login_facebook';
  static String loginLine = '$apiCenterLink/login_line';
  static String loginApple = '$apiCenterLink/login_apple';
  static String checkMemberWithLine = '$apiLink/checkMemberWithLine';
  // static String checkUsernameLogin = '$apiLink/checkUsernameLogin';
  static String checkMemberWithApple = '$apiLink/checkMemberWithApple';

  static String register = '$apiCenterLink/register';
  static String memberRegisterWithSocial = '$apiLink/memberRegisterWithSocial';
  static String saveProfileNotReplace = '$apiLink/saveProfileNotReplace';
  static String savePhotoProfile = '$apiLink/savePhotoProfile';

  //register center new
  // static String registerCenterFind22 =
  //     'https://pmzpeybwf2.execute-api.ap-southeast-1.amazonaws.com/latest'; // not use
  static String registerCenter =
      'https://pmzpeybwf2.execute-api.ap-southeast-1.amazonaws.com/latest/register';

  static String checkVersionAndroid = '$apiCenterLink/checkVersionAndroid';

  static String requertOTP = '$apiCenterLink/requertOTP';
  static String verifyOTP = '$apiCenterLink/verifyOTP';

  static String checkUserExist = '$apiLink/checkUserExist';
  static String checkPhoneExist = '$apiLink/checkPhoneExist';

  static String generatorToken = '$apiLink/generatorToken';
  static String saveNotifyToken = '$apiLink/saveNotifyToken';
  static String getPhoneCenter = '$apiLink/getPhoneCenter';

  static String getProfileById = '$apiLink/getProfileById';
  static String getProfileAndMRCode = '$apiLink/getProfileAndMRCode';
  static String getProfileAndMR = '$apiLink/getProfileAndMR';


  static String getSale = '$apiLink/getSale';
  static String getSa = '$apiLink/getSa';

  static String getListMember = '$apiLink/getListMember';
  static String getListContactType = '$apiLink/getListContactType';

  static String saveActivity = '$apiLink/saveActivity';
  static String sendLineCrashCar = '$apiLink/sendLineCrashCar';

  static String saveLogErrorMApp = '$apiLink/saveLogErrorMapp';

  static String getProvinces = '$apiLink/getProvinces';
  static String getAmphurs = '$apiLink/getAmphurs';
  static String getDistricts = '$apiLink/getDistricts';
  static String getTumbol = '$apiLink/getTumbol';
  static String getZipcode = '$apiLink/getZipcode';

  static String getCar = '$apiLink/getCar';
  static String saveCar = '$apiLink/saveCar';
  static String saveAppointment = '$apiLink/saveAppointment';

  static String getCarServiceForAppPMS = '$apiLink/getCarServiceForAppPMS';

  static String getInsurance = '$apiLink/getInsurance';
  static String getPersonnelPaintBody = '$apiLink/getPersonnelPaintBody';

  static String createBrokenCar = '$apiLink/createBrokenCar2';

  static String createClaimOnline = '$apiLink/createClaimOnline';
  static String createClaimAtHome = '$apiLink/createClaimAtHome';
  static String getClaimHistory = '$apiLink/getClaimHistory';

  static String getPKGLiveForApp = '$apiLink/getPKGLiveForApp';
  static String getFeelWellTVForApp = '$apiLink/getFeelWellTVForApp';
  static String getNewsAndEvent = '$apiLink/getNewsAndEvent';
  static String getFeelWellTVTypeForApp = '$apiLink/getFeelWellTVTypeForApp';
  static String getFeelWellTVByTypeForApp =  '$apiLink/getFeelWellTVByTypeForApp';

  static String getPromotion =  '$apiLink/getPromotion';
  static String getCarCatalog =  '$apiLink/getCarCatalog';

  //MR
  static String getMrById = '$apiLink/getMrById';
  static String updateInformationPersonal = '$apiLink/updateInformationPersonal';
  static String updateInformationBookbank = '$apiLink/updateInformationBookbank';
  static String getInformationMR = '$apiLink/getInformationMR';
  static String createReferralMR = '$apiLink/createReferralMR';
  static String getReferralMR = '$apiLink/getReferralMR';
  static String getTypeReferralMR = '$apiLink/getTypeReferralMR';
  static String getPOImr = '$apiLink/getPOImr';
  static String getRankReferralMR = '$apiLink/getRankReferralMR';
  //MR

  static String connectSocial = '$apiLink/connectSocial';
  static String saveProfile = '$apiLink/saveProfile';
  static String saveProfileByID = '$apiLink/saveProfileByID';

  static String getUserAgreement = '$apiLink/getUserAgreement';

  //EVENT
  static String getEventRegister = '$apiLink/getEventRegister';
  static String getEventRegisterByID = '$apiLink/getEventRegisterByID';
  static String regisActivity = '$apiLink/regisActivity';
  static String getMemberActivity = '$apiLink/getMemberActivity';
  static String saveProfileNameAdd = '$apiLink/saveProfileNameAdd';
  //EVENT

  //LIKE POINT
  static String getBalanceByUID = 'https://new.likepoint.io/getBalanceByUID';
  static String getBalanceByPhoneNumber = 'https://new.likepoint.io/newGetBalanceByphoneNumber';
  static String getAddressByPhoneNumber = 'https://new.likepoint.io/getAddressByPhoneNumber';
  static String checkLockStake = 'https://new.likepoint.io/checkLockStake';
  static String openLikePointWallet = 'https://new.likepoint.io/partnerCreateUser';
  //LIKE POINT

  //POI
  static String checkCanGetLikePOI = '$apiLink/checkCanGetLikePOI';
  static String insertTransactionPOI = '$apiLink/insertTransactionPOI';
  // static String payLikeLock = '$apiCenterLink/payLikeLock';
  /// PMS point
  // static String payLikeLockPMSPoint = 'https://api2.likepoint.io/transactions-activity/pay-poi-in-app';
  //POI

  ///API CF
  static String apiCF = "https://agilesoftgroup.com/PMS";

  static String createCarReg = "$apiCF/license-plate/create-license-plate";
  static String removeCarReg = "$apiCF/license-plate/delete-license-plate/";
  static String getCarReg = "$apiCF/license-plate/get-all-license-plate-by-phone/";

  static String createAppointment = "$apiCF/appointment/create-appointment";
  static String createHomeService = "$apiCF/home-service/create-home-service";

  static String getEvent = "$apiCF/event/get-event";
  static String registerEvent = "$apiCF/event/register-event";
  static String getNews = "$apiCF/news/get-news";
  static String getPromotions = "$apiCF/promotion/get-promotion";
  static String getCatalogs = "$apiCF/catalog/get-catalog";
  static String checkRegisterEvent = "$apiCF/event/check-register-event";

  static String newCar = "$apiCF/new-car/get-new-car";

  static String lifetimeAppointment = "$apiCF/lifetime-appointment/get-lifetime-appointment";

  static String saveActivityCF = "$apiCF/activity/save-activity";

  static String sendOTPMKT = "$apiCF/OTP/sendOTP";
  static String verifyOTPMKT = "$apiCF/OTP/verifyOTP";

  static String registerSocial = "$apiCF/register/social";
  static String registerCF = "$apiCF/register/register";
  static String registerTG = "$apiCF/register/register-tg";
  static String updateIDTG = "$apiCF/register/update-id-tg";
  static String loginTG = "$apiCF/login/login-tg";
  static String connectIdSocial = "$apiCF/connect-social/connect-social";

  static String getCarRepairStatus = "$apiCF/car-repair-status/get-car-repair-status";

  static String updateAgreement = "$apiCF/member/update-agreement";

  static String feedback = "$apiCF/feedback/create-feedback";

  static String updateNotiEsign = "$apiCF/sendNotification/updateNotiForRDSFollowRunning";
  static String getEsignNoti = "$apiCF/sendNotification/getNotiForRDS";
  // static String getEsignNotiFollowRunning = "$apiCF/sendNotification/getNotifyByRunning"; // No use anymore
  static String deleteNotiEsign = "$apiCF/sendNotification/deleteNotifyFollowRunning";
  static String deleteAllNotiEsign = "$apiCF/sendNotification/deleteAllNotiEsign";

  /// POI
  static String payLikeLockPMSPoint = "$apiCF/poi/payLike";


  static String eSignOnDocker = "https://api-e-sign-braqngq4da-as.a.run.app/signOnDocWithWord";
  static String eSignOnDockerMidAndEndPage = "https://api-e-sign-braqngq4da-as.a.run.app/signOnDocWithWordPutAll";

  ///API CF
  /// API Ref Code
  static String CheckAndUpdateMrCode = "$apiCF/MR/Check-MR-Code";
  static String GetHistoryMrCode = "$apiCF/MR/Get-History-MR-Code";
  static String registerWithRefCode = "$apiCF/register/registerWithRefcode";
  static String UpdateRefCode = "$apiCF/RefCode/update-ref-code-mr";
  static String getRefCode = "$apiCF/RefCode/get-ref-code";
  ///PSI
  static String getCouponPSI = "$apiCF/psi/get-coupon-psi";
  static String getCarOwner = "$apiCF/psi/get-car-owner";
  static String getCarImg = "$apiCF/psi/get-car-picture";

  static String getCouponRC = "$apiCF/couponRC/get-couponRC";
  static String claimCouponRC = "$apiCF/couponRC/claim-couponRC";
  static String useCouponRC = "$apiCF/couponRC/used-couponRC";
}