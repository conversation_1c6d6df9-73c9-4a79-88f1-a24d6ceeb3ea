import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppWidget {
  static normalText(context, text, size, color, FontWeight weight){
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        fontFamily: 'Prompt',
        fontSize: size.toDouble(),
        color: color,
        fontWeight: weight,
      ),
    );
  }

  static normalTextS(context, text, size, color, FontWeight weight){
    return Text(
      text,
      textAlign: TextAlign.left,
      style: TextStyle(
        fontFamily: 'Prompt',
        fontSize: size.toDouble(),
        color: color,
        fontWeight: weight,
        shadows: <Shadow>[
          Shadow(
            offset: const Offset(0, 1),
            blurRadius: 5.0,
            color: const Color(0xFF000000).withOpacity(0.1),
          ),
        ],
      ),
    );
  }

  static boldText(context, text, size, color, FontWeight weight){
    return Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontFamily: 'Prompt-Medium',
          fontSize: size.toDouble(),
          color: color,
          fontWeight: weight,
          // letterSpacing: 0.2,
        )
    );
  }

  static boldTextS(context, text, size, color, FontWeight weight){
    return Text(
        text,
        textAlign: TextAlign.left,
        style: TextStyle(
          fontFamily: 'Prompt-Medium',
          fontSize: size.toDouble(),
          color: color,
          fontWeight: weight,
          // letterSpacing: 0.2,
          shadows: <Shadow>[
            Shadow(
              offset: const Offset(0, 1),
              blurRadius: 5.0,
              color: const Color(0xFF000000).withOpacity(0.2),
            ),
          ],
        )
    );
  }

  static InputnormalText(context, _width, _height, headText, size, color, weight, controller){
    return Stack(
      children: [
        SizedBox(
          width: _width.toDouble(),
          height: _height.toDouble(),
          child: Text(
            headText,
            textAlign: TextAlign.left,
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: size.toDouble(),
              color: color,
              fontWeight: weight,
              letterSpacing: 0.2,
            ),
          ),
        ),
        SizedBox(
          width: _width.toDouble(),
          height: _height.toDouble(),
          child: TextField(
            controller: controller,
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: size.toDouble(),
              color: const Color(0xFF707070),
              fontWeight: weight,
              letterSpacing: 0.2,
            ),
            decoration: InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.fromLTRB(0, 20, 0, 0),
              border: InputBorder.none,
              hintText: 'ว่าง',
              hintStyle: TextStyle(
                fontFamily: 'Prompt',
                color: const Color(0xFF707070),
                fontSize: size.toDouble(),
                fontWeight: weight,
                letterSpacing: 0.2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  static showDialogPage(context, page){
    showDialog(
      barrierDismissible: true,
      context: context,
      useSafeArea: false,
      barrierColor: Colors.transparent,
      builder: (_) => page,
    );
  }

  static showDialogPageSlide(context, page){
    showGeneralDialog(
      barrierLabel: "showGeneralDialog",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.6),
      transitionDuration: const Duration(milliseconds: 300),
      context: context,
      pageBuilder: (context, _, __) {
        return page;
      },
      transitionBuilder: (_, animation1, __, child) {
        return SlideTransition(
          position: Tween(
            begin: const Offset(0, 1),
            end: const Offset(0, 0),
          ).animate(animation1),
          child: child,
        );
      },
    );
  }

  static showDatDivider(context){
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(
            (MediaQuery.of(context).size.width / 8).floor(),
                (index) {
              return const Text(
                "-",
                style: TextStyle(
                    fontSize: 12, color: Color(0xFFBCBCBC)),
              );
            }),
      ),
    );
  }
}