import 'dart:ui';

import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../controller/setting_controller/profile_controller.dart';
import '../controller/tutorial_controller.dart';

enum BorderAnimationType {
  lineSweep,
  pulse,
  shimmer,
}

class TutorialScreen extends StatefulWidget {
  const TutorialScreen({super.key});

  @override
  State<TutorialScreen> createState() => _TutorialScreenState();
}

class _TutorialScreenState extends State<TutorialScreen>
    with TickerProviderStateMixin {
  late AnimationController _holeController;
  late AnimationController _borderController;
  late Animation<double> _holeAnimation;
  late Animation<double> _borderAnimation;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();

    _holeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _borderController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _holeAnimation =
        CurvedAnimation(parent: _holeController, curve: Curves.easeInOut);
    _borderAnimation = Tween<double>(begin: 1.3, end: 1.0).animate(
      CurvedAnimation(parent: _borderController, curve: Curves.easeInOut),
    );

    _holeController.forward();
    _startBorderAnimation();
  }

  void _startBorderAnimation() {
    if (!_borderController.isAnimating) {
      _borderController.forward(from: 0.0).whenComplete(() {
        // if (tutorialCtl.currentTutorialStep.value < tutorialCtl.getCurrentTutorialSteps().length - 1) {
        //   _borderController.forward(from: 0.0); // เริ่มใหม่รอบเดียว
        // }
      });
      debugPrint('Border animation started for one cycle');
    }
  }

  void _stopBorderAnimation() {
    if (_borderController.isAnimating) {
      _borderController.stop();
      _borderController.value = 0.0; // รีเซ็ตตำแหน่งเริ่มต้น
      debugPrint('Border animation stopped');
    }
  }

  final tutorialCtl = Get.find<TutorialController>();
  final profileCtl = Get.find<ProfileController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Positioned.fill(
            child: Container(
              height: Get.height,
              width: Get.width,
              color: Colors.black.withOpacity(0.5),
            ),
          ),
          Obx(() => Positioned(
            top: 8,
            left: 8,
            right: 8,
            bottom: 8,
            child: Container(
              height: Get.height,
              width: Get.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                image: DecorationImage(
                  image: AssetImage(_getTutorialBackgroundImage()),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          )),
          Positioned(
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _holeAnimation,
                _borderAnimation,
              ]),
              builder: (context, child) {
                final screenWidth = MediaQuery.of(context).size.width;
                final screenHeight = MediaQuery.of(context).size.height;
                final currentStep = tutorialCtl.getCurrentStep();
                final holeLeft = currentStep['holeLeft'] * screenWidth;
                final holeTop = currentStep['holeTop'] * screenHeight;
                final holeWidth = currentStep['holeWidth'] *
                    screenWidth *
                    _holeAnimation.value;
                final holeHeight = currentStep['holeHeight'] *
                    screenHeight *
                    _holeAnimation.value;

                return Stack(
                  children: [
                    GestureDetector(
                      onTap: () async {
                        if (_holeController.isAnimating || _isProcessing)
                          return;
                        _isProcessing = true;
                        try {
                          if (tutorialCtl.currentTutorialStep.value >=
                              tutorialCtl.getCurrentTutorialSteps().length -
                                  1) {
                            await _holeController.reverse();
                            _stopBorderAnimation();
                            Navigator.of(context).pop();
                          } else {
                            _stopBorderAnimation();
                            await _holeController.reverse().whenComplete(() {
                              tutorialCtl.nextTutorialStep();
                            }).then((_) async {
                              await _holeController.forward();
                              _startBorderAnimation();
                            });
                          }
                        } catch (e) {
                          debugPrint('Error: $e');
                        } finally {
                          _isProcessing = false;
                        }
                      },
                      child: CustomPaint(
                        size: Size(screenWidth, screenHeight),
                        painter: BorderPainter(
                          holeRect: Rect.fromLTWH(
                            holeLeft,
                            holeTop,
                            holeWidth,
                            holeHeight,
                          ),
                          borderScale: _borderAnimation.value,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          Obx(() => Positioned(
            top: _getTutorialTextTop(),
            left: 9,
            right: 13,
            child: Container(
              height: 200,
              width: Get.width,
              // color: Colors.red,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      final ctl = tutorialCtl;
                      ctl.saveTutorialPlayed(ctl.tutorialType.value);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      height: 30,
                      width: 80,
                      child: Center(
                        child: Text(
                          "ข้ามไปก่อน",
                          textAlign: TextAlign.end,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            decoration: TextDecoration.underline,
                            decorationColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  _buildTutorialContainer(),
                ],
              ),
            ),
          )),
          Padding(
            padding: EdgeInsets.only(left: 120, right: 120, top: 35),
            child: Align(
              alignment: Alignment.topCenter,
              child: Text(
                'กดที่ใดก็ได้เพื่อดูต่อ',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTutorialBackgroundImage() {
    final ctl = tutorialCtl;
    if (ctl.tutorialType.value == 'home') {
      switch (ctl.homeStatus.value) {
        case 'not_login':
          return 'assets/image/tutorial/Guesttutorial.png';
        case 'no_car':
          return 'assets/image/tutorial/Home.png';
        case 'has_car':
          return 'assets/image/tutorial/HomeCar.png';
        default:
          return 'assets/image/tutorial/Home.png';
      }
    }
    if (ctl.tutorialType.value == 'mr') {
      return 'assets/image/tutorial/MR.png';
    }
    if (ctl.tutorialType.value == 'profile') {
      switch (ctl.currentTutorialStep.value) {
        case 0:
          return 'assets/image/tutorial/EditProfile.png';
        case 1:
          return 'assets/image/tutorial/EditProfile1.png';
        default:
          return 'assets/image/tutorial/EditProfile.png';
      }
    }
    return '';
  }
  double _getTutorialTextTop() {
    final ctl = tutorialCtl;
    final topPadding = MediaQuery.of(context).padding.top;

    if (ctl.tutorialType.value == "home") {
      if (ctl.homeStatus.value == "not_login") {
        switch (tutorialCtl.currentTutorialStep.value) {
          case 0:
            return 500.h;
          case 1:
            return 500.h;
          case 2:
            return 270.h;
          case 3:
            return 500.h;
          default:
            return 400.h;
        }
      } else if (ctl.homeStatus.value == "no_car") {
        if (ctl.currentTutorialStep.value == 1) {
          return 300.h;
        }
      } else if (ctl.homeStatus.value == "has_car") {
        switch (tutorialCtl.currentTutorialStep.value) {
          case 0:
            return 500.h;
          case 1:
            return 330.h;
          default:
            return 500.h;
        }
      }
    } else if (ctl.tutorialType.value == "profile") {
      switch (tutorialCtl.currentTutorialStep.value) {
        case 0:
          return 220.h;
        case 1:
          return 360.h;
        default:
          return 400.h;
      }
    } else if (ctl.tutorialType.value == "mr") {
      switch (tutorialCtl.currentTutorialStep.value) {
        case 0:
          return 500.h;
        case 1:
          return 190.h;
        case 2:
          return 80.h;
        case 3:
          return 120.h;
        default:
          return 400.h;
      }
    }
    return 500.h;
  }

  Widget _buildTutorialContainer() {
    final ctl = tutorialCtl;
    bool useSpecialFrame = false;

    if (ctl.tutorialType.value == "home") {
      if (ctl.homeStatus.value == "not_login") {
        useSpecialFrame = ctl.currentTutorialStep.value == 2;
      } else if (ctl.homeStatus.value == "no_car") {
        useSpecialFrame = ctl.currentTutorialStep.value == 1;
      } else {
        useSpecialFrame = ctl.currentTutorialStep.value == 1;
      }
    } else if (ctl.tutorialType.value == "mr") {
      useSpecialFrame = ctl.currentTutorialStep.value == 1 ||
          ctl.currentTutorialStep.value == 2 ||
          ctl.currentTutorialStep.value == 3;
    } else if (ctl.tutorialType.value == "profile") {
      useSpecialFrame = ctl.currentTutorialStep.value == 0 ||
          ctl.currentTutorialStep.value == 1;
    }
    if (useSpecialFrame) {
      if (ctl.tutorialType.value == "profile" &&
          ctl.currentTutorialStep.value == 0) {
        return Padding(
          padding: EdgeInsets.only(left: 57.w),
          child: Container(
              height: 83.h,
              width: 305.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/image/tutorial/frameTop.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(top: 20, right: 4),
                child: buildTextWithStep(),
              )),
        );
      } else if (ctl.tutorialType.value == "profile" &&
          ctl.currentTutorialStep.value == 1) {
        return Center(
          child: Container(
              height: 87.h,
              width: 305.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/image/tutorial/frameBelow.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: buildTextWithStep()),
        );
      } else if (ctl.tutorialType.value == "mr" &&
          (ctl.currentTutorialStep.value == 1)) {
        return Center(
          child: Container(
              height: 83.h,
              width: 343.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/image/tutorial/frameTop.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(top: 20, right: 4),
                child: buildTextWithStep(),
              )),
        );
      } else if (ctl.tutorialType.value == "mr" &&
          (ctl.currentTutorialStep.value == 2 ||
              ctl.currentTutorialStep.value == 3)) {
        return Center(
          child: Container(
              height: ctl.currentTutorialStep.value == 3 ? 88.h : 106.h,
              width: ctl.currentTutorialStep.value == 3 ? 343.w : 305.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/image/tutorial/frameBelow.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: buildTextWithStep()),
        );
      } else {
        return Center(
          child: Container(
              height: 145.h,
              width: 305.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/image/tutorial/frame.png'),
                  fit: BoxFit.fill,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(top: 20),
                child: buildTextWithStep(),
              )),
        );
      }
    }

    // Default frame (no specialFrame)
    return Container(
      height: 80.h,
      width: 356.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            height: 80.h,
            width: 80.w,
            decoration: BoxDecoration(
              border: Border.all(
                color: Color(0xFFFF9701),
                width: 1,
              ),
              color: Colors.black.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
                padding: EdgeInsets.only(top: 4),
                child: Lottie.network(
                    'https://cdn.lottielab.com/l/C7yruvhC2E1zZm.json')),
          ),
          Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFF8F8F8),
                ]),
                borderRadius: BorderRadius.circular(12),
              ),
              height: 80.h,
              width: 271.w,
              child: buildTextWithStep()),
        ],
      ),
    );
  }

  Widget buildTextWithStep() {
    final ctl = tutorialCtl;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: 24.w,
            top: ctl.getTopPadding(
                ctl.tutorialType.value, ctl.currentTutorialStep.value, context),
          ),
          child: buildTutorialText(),
        ),
        Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: EdgeInsets.all(4.0),
            child: Text(
              ' ${ctl.currentTutorialStep.value + 1}/${ctl.getCurrentTutorialSteps().length}',
              style: TextStyle(
                color: Colors.black.withOpacity(0.25),
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildTutorialText() {
    return Obx(() {
      final ctl = tutorialCtl;
      List<Widget> textWidgets = [];

      // จำกัดการพิมพ์ debug เพื่อลด spam
      if (!ctl.isAnimating.value) {
        debugPrint("Current step: ${ctl.currentTutorialStep.value}, "
            "Message type: ${ctl.currentMessageTypeString}, "
            "Color: ${ctl.currentMessageColor}");
      }

      for (var message in ctl.displayedMessages) {
        final spans = ctl.getCombinedMessageTextSpans(
          message['step'] as Map<String, dynamic>,
          ctl.messageColors,
        );
        textWidgets.add(
          RichText(
            text: TextSpan(children: spans),
          ),
        );
      }

      int stepIndex = ctl.currentTutorialStep.value;
      final currentStep = ctl.getCurrentTutorialMessages()[stepIndex];
      final spans =
      ctl.getCombinedMessageTextSpans(currentStep, ctl.messageColors);
      final currentText = ctl.extractPlainTextFromSpans(spans);
      final alreadyDisplayed =
      ctl.displayedMessages.any((m) => m['text'] == currentText);

      if (ctl.hasMoreMessages &&
          ctl.shouldPlayNext.value &&
          !alreadyDisplayed) {
        textWidgets.add(
          TypewriterAnimatedTextKit(
            text: [currentText],
            textStyle: TextStyle(
              fontSize: 14.0,
              fontWeight: FontWeight.w500,
              fontFamily: 'Prompt',
              color: ctl.currentMessageColor,
              shadows: [
                Shadow(
                  offset: Offset(0, 1),
                  blurRadius: 2.0,
                  color: Color(0xFF000000).withOpacity(0.2),
                ),
              ],
            ),
            speed: const Duration(milliseconds: 70),
            onFinished: () {
              Future.delayed(const Duration(milliseconds: 70), () {
                ctl.finishCurrentMessage(currentText, currentStep);
              });
            },
            textAlign: TextAlign.start,
            totalRepeatCount: 1,
            repeatForever: false,
            isRepeatingAnimation: false,
          ),
        );
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.center,
          children: textWidgets,
        ),
      );
    });
  }
}

class BorderPainter extends CustomPainter {
  final Rect holeRect;
  final double borderScale;

  BorderPainter({required this.holeRect, required this.borderScale});

  @override
  void paint(Canvas canvas, Size size) {
    canvas.saveLayer(Offset.zero & size, Paint());

    final overlayPaint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final overlayPath = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    final holePath = Path()
      ..addRRect(RRect.fromRectAndRadius(holeRect, const Radius.circular(12)));

    final combinedPath =
    Path.combine(PathOperation.difference, overlayPath, holePath);
    canvas.drawPath(combinedPath, overlayPaint);

    final borderPaint = Paint()
      ..color = Colors.white.withOpacity(1 - (borderScale - 1.0))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    final scaledRect = Rect.fromCenter(
      center: holeRect.center,
      width: holeRect.width * borderScale,
      height: holeRect.height * borderScale,
    );

    final borderRRect =
    RRect.fromRectAndRadius(scaledRect, const Radius.circular(16));
    canvas.drawRRect(borderRRect, borderPaint);

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant BorderPainter oldDelegate) {
    return holeRect != oldDelegate.holeRect ||
        borderScale != oldDelegate.borderScale;
  }
}