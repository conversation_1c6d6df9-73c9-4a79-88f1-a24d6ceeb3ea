import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/connect_social_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class WebViewTelegram extends StatefulWidget {
  const WebViewTelegram({Key? key}) : super(key: key);

  @override
  State<WebViewTelegram> createState() => _WebViewTelegramState();
}

class _WebViewTelegramState extends State<WebViewTelegram> {

  int clickCount = 0;
  int clickCount2 = 0;

  final chatInAppCtl = Get.put(ChatInAppController());
  final profileCtl = Get.put(ProfileController());
  final connectSocialCtl = Get.put(ConnectSocialController());

  @override
  Widget build(BuildContext context) {
        return Scaffold(
            body: Stack(
              children: [
                InAppWebView(
                  onLoadStart: (controller, url) async {
                    chatInAppCtl.webViewCtrl = controller;
                    // chatInAppCtl.approveOpenUI(true);
                    // coveredUI();
                    await chatInAppCtl.checkUse(context);
                  },
                  onLoadStop: (controller, url) async {
                    // print("onLoadStop");
                    // print(url);
                    if (url.toString() ==
                        "https://telegramserv.agilesoftgroup.com/") {
                      // if (chatInAppCtl.chkAlertGroup == false) {
                      //   showWaitingGroupAlert(context);
                      // }
                      await Future.delayed(const Duration(seconds: 10));
                      // chatInAppCtl.changeLoginAutoFill();
                      clickCount++;
                      if (clickCount == 1) {
                        var step0 = await controller.evaluateJavascript(
                            source:
                            'var button = document.querySelector("#auth-phone-number-form > div > form > button.Button.default.primary.text.has-ripple"); console.log("click1"); button.click()');
                      }
                    } else {
                      // setState(() {
                      //   chatInAppCtl.chkAlertGroup = false;
                      // });
                      // Loader.hide(context);
                      // Navigator.pop(context);
                    }
                  },
                  onConsoleMessage: (controller, consoleMessage) async {
                    // print(consoleMessage.message);
                    if (consoleMessage.message == "click1") {
                      await Future.delayed(const Duration(seconds: 6));
                      clickCount2++;
                      if (clickCount2 == 1) {
                        var step1 = await controller.evaluateJavascript(
                            source:
                            'var token = document.getElementsByClassName("qr-container_token")[0].value; console.log("qrToken " + token);');
                      }
                    }
                    if(consoleMessage.message.startsWith("qrToken ")) {
                      chatInAppCtl.token.value = consoleMessage.message.substring(8);
                      final response = await http.post(
                        Uri.parse(
                            'https://telegram.agilesoftgroup.com/loginViaQR'),
                        body: {
                          'phone': chatInAppCtl.phoneCode.value,
                          'base64': "",
                          "QRtoken" : chatInAppCtl.token.value
                        },
                      );
                      var data = jsonDecode(response.body);
                      if (data['status'] == "success") {
                        if(chatInAppCtl.useCount == 0) {
                          await Future.delayed(const Duration(seconds: 2));
                          setState(() async {
                            chatInAppCtl.useCount = 1;
                            chatInAppCtl.box.write('useCount', 1);
                            AppLoader.dismiss(context);
                          });
                        }

                      } else if(data['status'] == "error"){
                        await chatInAppCtl.registerREVOKED(context);
                      } else {
                        var res = await AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
                        if(res) {
                          AppLoader.dismiss(context);
                          Get.back();
                        }
                      }
                    }
                  },

                  initialUrlRequest: URLRequest(url: Uri.parse(chatInAppCtl.url.value)),
                ),
                Container(
                  width: Get.width,
                  height: 104,
                  color: Colors.white,
                  alignment: Alignment.bottomCenter,
                  padding: const EdgeInsets.only(
                    top: 20,
                    right: 20,
                    left: 20,
                    bottom: 10
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(15),
                                topLeft: Radius.circular(15),
                                bottomRight: Radius.circular(15),
                                bottomLeft: Radius.circular(15),
                              ),
                              border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                          child: const Icon(
                            Icons.arrow_back_ios_new,
                            size: 18,
                            color: Color(0xFFFFB100),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Image.asset('assets/icon/contact_icon.png', width: 30,),
                          const SizedBox(
                            width: 6,
                          ),
                          AppWidget.boldText(context, "พูดคุยกับประชากิจฯ", 18, const Color(0xFF282828), FontWeight.w400),
                        ],
                      ),
                      const SizedBox(width: 36,),
                    ],
                  ),
                )
              ],
            )
        );
  }
}
