import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/connect_social_controller.dart';
import 'package:pin_input_text_field/pin_input_text_field.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../controller/setting_controller/chat_in_app_controller.dart';

class VerifyTG extends StatefulWidget {
  const VerifyTG({Key? key}) : super(key: key);

  @override
  State<VerifyTG> createState() => _VerifyTGState();
}

class _VerifyTGState extends State<VerifyTG> {
  Timer? _timer;
  int _start = 0;
  RxBool showSendAgain = false.obs;
  final connectSocialCtl = Get.put(ConnectSocialController());
  final chatInAppCtl = Get.put(ChatInAppController());

  void startTimer() {
    try {
      _start = 60;
      const oneSec = Duration(seconds: 1);
      _timer = Timer.periodic(
        oneSec,
            (Timer timer) => setState(
              () {
            if (_start < 1) {
              timer.cancel();
            } else {
              _start = _start - 1;
            }
          },
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Verify");
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      body: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: [0.0, 0.5, 1.0],
              colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF), Color(0xFFECECEC)],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF664701).withOpacity(1),
                offset: const Offset(0, -2), // changes position of shadow
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 80,
              ),
              //back
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: Container(
                  width: 36,
                  height: 36,
                  margin: const EdgeInsets.only(
                    left: 20,
                    right: 20,
                  ),
                  decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                      ),
                      border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    size: 18,
                    color: Color(0xFFFFB100),
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                width: Get.width,
                margin: const EdgeInsets.only(
                  left: 20,
                  right: 20,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.normalText(context, "ใส่รหัสยืนยัน OTP", 18,
                        const Color(0xFF282828), FontWeight.w400),
                    const SizedBox(
                      height: 10,
                    ),
                    AppWidget.normalText(
                        context,
                        "กรุณาใส่รหัสยืนยันบัญชีใช้งาน 5 หลัก เราได้ส่งรหัสไป",
                        12,
                        const Color(0xFF895F00),
                        FontWeight.w400),
                    const SizedBox(
                      height: 10,
                    ),
                    // AppWidget.boldText(
                    //     context,
                    //     chatInAppCtl.phoneTextController.text,
                    //     12,
                    //     const Color(0xFF664701), FontWeight.w600),
                    // const SizedBox(
                    //   height: 10,
                    // ),
                  ],
                ),
              ),
              const SizedBox(
                height: 30,
              ),
              chatInAppCtl.typeVerify.value == "otp"
                ? Container(
                  margin: const EdgeInsets.only(
                    left: 80,
                    right: 80,
                  ),
                  width: Get.width,
                  height: 50,
                  child: PinInputTextField(
                    pinLength: 5,
                    decoration: UnderlineDecoration(
                      textStyle: TextStyle(
                        color: const Color(0xFF241F35),
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        letterSpacing: 0.3,
                        shadows: <Shadow>[
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: 1.0,
                            color: const Color(0xFF000000).withOpacity(0.15),
                          ),
                        ],
                      ),
                      colorBuilder: PinListenColorBuilder(
                        const Color(0xFFE6E6E6),
                        const Color(0xFFE6E6E6).withOpacity(0.7),
                      ),
                    ),
                    controller: chatInAppCtl.pinTGTextController,
                    textInputAction: TextInputAction.go,
                    enabled: true,
                    autoFocus: true,
                    keyboardType: TextInputType.number,
                    onSubmit: (pin) async {
                      if (pin.length == 5) {
                        // print(chatInAppCtl.typeMenu.value);
                        // //TODO :: CHECK OTP FROM LOGIN
                        // if (loginCtl.typeMenu.value == "registerWithTG") {
                          chatInAppCtl.checkOTPTG(context);
                        // } else if(loginCtl.typeMenu.value == "connect-social-tg"){
                        //   connectSocialCtl.checkOTPTGConnect(context);
                        // }
                      }
                    },
                    onChanged: (pin) async {
                      if (pin.length == 5) {
                        // print(loginCtl.typeMenu.value);
                        // //TODO :: CHECK OTP FROM LOGIN
                        // if (loginCtl.typeMenu.value == "registerWithTG") {
                          chatInAppCtl.checkOTPTG(context);
                        // } else if(loginCtl.typeMenu.value == "connect-social-tg"){
                        //   connectSocialCtl.checkOTPTGConnect(context);
                        // } else if(loginCtl.typeMenu.value == "connect-social-tg-revoke"){
                        //   chatInAppCtl.registerREVOKED(context);
                        // }
                      }
                    },
                  ))
                : Container(
                ///text field
                margin: const EdgeInsets.only(
                  left: 80,
                  right: 80,
                ),
                width: Get.width,
                height: 50,
                alignment: Alignment.center,
                child: TextField(
                  controller: chatInAppCtl.pinTGTextController,
                  textInputAction: TextInputAction.go,
                  style: TextStyle(
                    color: const Color(0xFF241F35),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    letterSpacing: 0.3,
                    shadows: <Shadow>[
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 1.0,
                        color: const Color(0xFF000000).withOpacity(0.15),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                  textAlignVertical: TextAlignVertical.bottom,
                  decoration: InputDecoration(
                    hintText: "รหัสยืนยัน",
                    hintStyle: TextStyle(
                      color: const Color(0xFF241F35).withOpacity(0.5),
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      letterSpacing: 0.3,
                      shadows: <Shadow>[
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: 1.0,
                          color: const Color(0xFF000000).withOpacity(0.15),
                        ),
                      ],
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        color: Color(0xFFE6E6E6),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        color: Color(0xFFE6E6E6),
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: const BorderSide(
                        color: Color(0xFFE6E6E6),
                      ),
                    ),
                  ),

                ),
              ),
              const SizedBox(
                height: 40,
              ),
              chatInAppCtl.typeVerify.value == "otp"
                  ? showSendAgain.value == true
                    ? InkWell(
                  onTap: () async {
                    if (kDebugMode) {
                      print('ส่งรหัสอีกครั้ง');
                    }
                    var status = await chatInAppCtl.sendOTPTG(context);
                    if (status == true) {
                      setState(() {
                        chatInAppCtl.pinTGTextController.text = "";
                        showSendAgain.value = false;
                        startTimer();
                      });
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.only(
                      left: 20,
                      right: 20,
                    ),
                    alignment: Alignment.center,
                    child: RichText(
                      text: const TextSpan(
                        text: 'ไม่ได้รับรหัสยืนยัน? ',
                        style: TextStyle(
                          fontFamily: 'Prompt',
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF555555),
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text: 'ส่งรหัสอีกครั้ง',
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
                    : Align(
                alignment: Alignment.center,
                child: Countdown(
                  seconds: 60,
                  build: (_, double time) => Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalText(
                          context,
                          "ขอรหัสอีกครั้งภายใน ",
                          14,
                          const Color(0xFF707070),
                          FontWeight.w400),
                      AppWidget.normalText(
                          context,
                          "${time.ceil()}",
                          14,
                          const Color(0xFFFFB100),
                          FontWeight.w400),
                    ],
                  ),
                  onFinished: () {
                    setState(() {
                      showSendAgain.value = true;
                    });
                  },
                ),
              )
                  : const SizedBox(),
              const Spacer(),
              chatInAppCtl.typeVerify.value == "otp"
                ? const SizedBox()
                : InkWell(
                onTap: () {
                  // if (loginCtl.typeMenu.value == "registerWithTG") {
                    chatInAppCtl.checkOTPTG(context);
                  // } else if(loginCtl.typeMenu.value == "connect-social-tg"){
                  //   connectSocialCtl.checkOTPTGConnect(context);
                  // } else if(loginCtl.typeMenu.value == "connect-social-tg-revoke"){
                  //   chatInAppCtl.registerREVOKED(context);
                  // }
                },
                child: Container(
                  margin: const EdgeInsets.only(
                    left: 20,
                    right: 20,
                  ),
                  width: Get.width,
                  height: 50,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: const Color(0xFFFFB100),
                  ),
                  child: AppWidget.normalText(
                      context,
                      "ยืนยัน",
                      16,
                      const Color(0xFFFFFFFF),
                      FontWeight.w400),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      ),
    ));
  }
}
