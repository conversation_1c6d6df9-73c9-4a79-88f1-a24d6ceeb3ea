import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class InformationEn extends StatelessWidget {
  // InformationEn({Key? key}) : super(key: key);

  final GlobalKey aboutUsKey = GlobalKey();
  final GlobalKey visionKey = GlobalKey();
  final GlobalKey missionKey = GlobalKey();
  final GlobalKey servicesKey = GlobalKey();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    bool isTablet = size.width >= 600; // กำหนดเงื่อนไขแท็บเล็ต
    bool isMobile = size.width < 600; // กำหนดเงื่อนไขมือถือ
    bool isDesktop = size.width >= 1024; // กำหนดเงื่อนไขเดสก์ท็อป
    return Scaffold(
      key: _scaffoldKey,
      drawer:  buildDrawer(context, _scrollToSection),
      body: Column(
        children: [
          // Header
          Container(
            height: 80,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 0.5, 1.0],
                colors: [
                  Color(0xFFFFC700),
                  Color(0xFFFFB100),
                  Color(0xFFFF9900)
                ],
              ),
            ),child:
              isMobile
                  ? Row(
              children: [
                Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding:  EdgeInsets.all(8.0),
                    child: Image.asset('assets/icon/icon.png'),
                  )),
                Spacer(),
                InkWell(
                  onTap: (){
                    _scaffoldKey.currentState!.openDrawer();
                  },
                    child: Icon(Icons.menu)),
                SizedBox(width: 30,)
              ],
            )
                  : Row(
                // crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Align(
                      alignment: Alignment.topLeft,
                      child: Padding(
                        padding:  EdgeInsets.all(8.0),
                        child: Image.asset('assets/icon/icon.png'),
                      )),
                  Spacer(),
                  TextButton(onPressed: () {
                    _scrollToSection(aboutUsKey);
                  }, child: Text('About Us',
                    style: TextStyle(color: Colors.white,
                      fontSize: 20,
                    ),
                  ),),
                  TextButton(onPressed: () {
                    _scrollToSection(visionKey);
                  }, child: Text('Vision',
                    style: TextStyle(color: Colors.white,fontSize: 20,),
                  ),),
                  TextButton(onPressed: () {
                    _scrollToSection(missionKey);
                  }, child: Text('Mission',
                    style: TextStyle(color: Colors.white,fontSize: 20,),
                  ),),
                  TextButton(onPressed: () {
                    _scrollToSection(servicesKey);
                  }, child: Text('Products and Services',
                    style: TextStyle(color: Colors.white,fontSize: 20,),
                  ),),
                  SizedBox(width: 16,)
                  // Text('Vision'),
                  // Text('Mission'),
                  // Text('Products and Services'),
                ],
              )

          ),
          // Content
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFF3F3F3),
                    Color(0xFFFFFFFF),
                  ],
                ),
              ),
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle('Prachakij Motor Sales Co., Ltd'),
                      const SizedBox(height: 16),
                      Container(
                        key: aboutUsKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('About Us'),
                            const SizedBox(height: 16),
                            const Text(
                              'Established in 1984, Prachakij Motor Sales Co., Ltd. is a premier authorized Isuzu dealership located in Chanthaburi, Thailand. With over four decades of experience, we have been dedicated to providing exceptional automotive services to our community. Our commitment to customer satisfaction and quality service has solidified our reputation as a trusted leader in the automotive industry of Chanthaburi.',
                              style: TextStyle(fontSize: 16, height: 1.5),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      Container(
                        key: visionKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Vision'),
                            const SizedBox(height: 8),
                            const Text(
                              'To be the leading automotive dealership in Chanthaburi, renowned for our unwavering commitment to customer satisfaction, innovation, and community engagement.',
                              style: TextStyle(fontSize: 16, height: 1.5),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      Container(
                        key: missionKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Mission'),
                            const SizedBox(height: 8),
                            _buildBulletPoints([
                              'Customer-Centric Approach: Deliver unparalleled service experiences that exceed customer expectations.',
                              'Quality Assurance: Offer top-tier Isuzu vehicles and services that embody reliability and excellence.',
                              'Community Engagement: Actively contribute to the growth and well-being of the Chanthaburi community.',
                              'Continuous Improvement: Embrace innovation and continuous development to meet the evolving needs of our customers.',
                            ]),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      Container(
                        key: servicesKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle('Products and Services'),
                            const SizedBox(height: 8),
                            _buildBulletPoints([
                              'New Vehicle Sales: Explore the latest Isuzu models, including the D-Max series, MU-X SUVs, and a variety of commercial trucks.',
                              'Certified Pre-Owned Vehicles: Browse our selection of thoroughly inspected, high-quality pre-owned vehicles.',
                              'Service Center: State-of-the-art maintenance and repair services.',
                              'Genuine Parts and Accessories: Authentic Isuzu parts and accessories.',
                              'Financing Solutions: Tailored financing and leasing options.',
                              'Customer Support: Dedicated team for inquiries and consultations.',
                              'After-Sales Service: Scheduled maintenance, warranty services, and roadside assistance.',
                            ]),
                            const SizedBox(height: 16),
                            const Text(
                              'Visit us at our Chanthaburi showroom to experience our commitment to excellence firsthand. We look forward to serving you and becoming your preferred automotive partner.',
                              style: TextStyle(fontSize: 16, height: 1.5),
                            ),
                            SizedBox(height: 16),
                            Row(
                              children: [
                                Text("IOS:",
                                  style: TextStyle(fontSize: 16, height: 1.5),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    const url = 'https://apps.apple.com/us/app/prachakij/id1507877291'; // ลิงก์ไปยัง App Store
                                    if (await canLaunch(url)) {
                                      await launch(url); // เปิดลิงก์
                                    } else {
                                      throw 'Could not launch $url';
                                    }
                                  },
                                  child: Text('Download',
                                    style: TextStyle(fontSize: 16, height: 1.5, color: Colors.black,decoration: TextDecoration.underline),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Text("ANDROID:",
                                  style: TextStyle(fontSize: 16, height: 1.5),
                                ),
                                TextButton(
                                  onPressed: () async {
                                    const url = 'https://play.google.com/store/apps/details?id=com.prachakij.pms_app&pli=1'; // ลิงก์ไปยัง App Store
                                    if (await canLaunch(url)) {
                                      await launch(url); // เปิดลิงก์
                                    } else {
                                      throw 'Could not launch $url';
                                    }
                                  },
                                  child: Text('Download',
                                    style: TextStyle(fontSize: 16, height: 1.5, color: Colors.black,decoration: TextDecoration.underline),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // Footer
          Container(
            // height: 100,
            width: double.infinity,
            color: Colors.grey[200],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.phone, color: Colors.grey[600]),
                    SizedBox(width: 8),
                    Text('Phone: ************',
                        style:
                            TextStyle(fontSize: 14, color: Colors.grey[600])),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.access_time, color: Colors.grey[600]),
                    SizedBox(width: 8),
                    Text('Open Hours: Mon-Sat 8:15 AM - 5:00 PM',
                        style:
                            TextStyle(fontSize: 14, color: Colors.grey[600])),
                  ],
                ),
                Text(
                  '© 1984 Prachakij Motor Sales Co., Ltd. All Rights Reserved.',
                  textAlign: TextAlign.center, // จัดข้อความให้อยู่ตรงกลาง
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                  softWrap: true, // อนุญาตให้ตัดบรรทัดอัตโนมัติ
                ),
                Text('Flutter V3 App Modified by AGS', style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                SizedBox(height: 10),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildBulletPoints(List<String> points) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: points.map((point) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '\u2022', // Bullet point
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  point,
                  style: const TextStyle(fontSize: 16, height: 1.5),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _scrollToSection(GlobalKey key) {
    final RenderBox renderBox = key.currentContext!.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    _scrollController.animateTo(
      offset.dy,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Widget buildDrawer(BuildContext context, Function(GlobalKey) scrollToSection) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
         Align(
           alignment: Alignment.topLeft,
           child: Container(
             margin: EdgeInsets.only(top: 20, left: 16),
             height: 80,
             width: 80,
             decoration: BoxDecoration(
               borderRadius: BorderRadius.circular(10),
               image: DecorationImage(
                 image: AssetImage('assets/icon/icon.png'),
                 // fit: BoxFit.cover,
               ),
             ),
           ),
         ),
          ListTile(
            title: Text('About Us'),
            onTap: () {
              scrollToSection(aboutUsKey);
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text('Vision'),
            onTap: () {
              scrollToSection(visionKey);
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text('Mission'),
            onTap: () {
              scrollToSection(missionKey);
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: Text('Products and Services'),
            onTap: () {
              scrollToSection(servicesKey);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}
