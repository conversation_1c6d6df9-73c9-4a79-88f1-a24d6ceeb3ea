import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/promotion_list.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/promotion_view.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/talksc/talk_SC.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class HomePromotionTabletPage extends StatefulWidget {
  const HomePromotionTabletPage({Key? key}) : super(key: key);

  @override
  State<HomePromotionTabletPage> createState() => _HomePromotionTabletPageState();
}

class _HomePromotionTabletPageState extends State<HomePromotionTabletPage> {

  int _currentPromotion = 0;
  RxInt _currentCarIndex = 0.obs;

  String month = AppService.getMonth();

  final promotionCtl = Get.put(PromotionController());
  final pageCtl = Get.put(PageSelectController());


  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "HomePromotion");
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Obx((){
        if(promotionCtl.isLoading.value){
          return AppLoader.loaderWaitPage(context);
        } else {
          return NotificationListener(
            onNotification: (notification) {
              if (notification is ScrollUpdateNotification && notification.metrics.axis == Axis.vertical) {
                pageCtl.scrollParam.value = notification.metrics.pixels;
              }
              return true;
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(
                    height: 130,
                  ),
                  buildPromotion(),
                  buildCatalog(),
                  const Padding(padding: EdgeInsets.only(bottom: 100))
                ],
              ),
            ),
          );
        }
      }),
    );
  }

  buildPromotion(){
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.only(
              left: 54,
              right: 54
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  AppWidget.boldTextS(
                      context,
                      "โปรโมชั่นรถอีซูซุ ",
                      13,
                      Colors.black,
                      FontWeight.w400),
                  AppWidget.normalTextS(
                      context,
                      "ประจำเดือน ",
                      13,
                      Colors.black,
                      FontWeight.w400),
                  AppWidget.normalTextS(
                      context,
                      month,
                      13,
                      const Color(0xFF664701),
                      FontWeight.w400),
                ],
              ),
              InkWell(
                onTap: (){
                  Navigator.push(context,
                    MaterialPageRoute(
                        builder: (context) => const PromotionListPage()
                    ),
                  );
                },
                child: Row(
                  children: [
                    AppWidget.normalTextS(
                        context,
                        "ดูทั้งหมด ",
                        13,
                        const Color(0xFF91908E),
                        FontWeight.w400),
                    const Icon(Icons.arrow_forward_ios_rounded,
                      size:13, color: Color(0xFFFF9300),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        ),
        SizedBox(
          child: promotionCtl.promotionList.data!.isNotEmpty ? carouselRatio11Promotion(promotionCtl.promotionList.data!) : const SizedBox(),
        )
      ],
    );
  }

  Widget carouselRatio11Promotion(List<dynamic> listData){
    return CarouselSlider.builder(
      itemCount: listData.length,
      options: CarouselOptions(
        height: 450,
        enlargeCenterPage: true,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: true,  // เลื่อนสไลด์ได้ไกล
        viewportFraction: 0.5,  // ส่วนที่เปิดเผยของรายการภาพในแต่ละสไลด์
        aspectRatio: 1 / 1,  // อัตราส่วนกว้างต่อสูงของภาพในแต่ละสไลด์
        enlargeFactor: 0
      ),
      itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width: 450,
                height: 450,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF282828),
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image.network(
                    '${promotionCtl.urlEvent.value}${promotionCtl.promotionList.data![itemIndex].promotionPic.toString()}',
                    fit: BoxFit.cover,
                    errorBuilder: (BuildContext context,
                        Object exception,
                        StackTrace? stackTrace) {
                      return Container(
                        padding: const EdgeInsets.all(10),
                        child: const Icon(
                          Icons.error,
                          size: 50,
                          color: Color(0xFFFFB100),
                        ),);
                    },
                    loadingBuilder: (BuildContext context,
                        Widget child,
                        ImageChunkEvent? loadingProgress) {
                      if (loadingProgress == null) {
                        return child;
                      }
                      return Align(
                        alignment: Alignment.center,
                        child: SizedBox(
                          width: 50,
                          height: 50,
                          child:
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            color:
                            const Color(0xFFFFFFFF),
                            value: loadingProgress
                                .expectedTotalBytes !=
                                null
                                ? loadingProgress
                                .cumulativeBytesLoaded /
                                loadingProgress
                                    .expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                    width: 106,
                    height: 34,
                    margin: const EdgeInsets.only(bottom: 34),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0x00FFC700).withOpacity(0.9),
                          const Color(0x00FFB100).withOpacity(0.9),
                          const Color(0xFFFF9900).withOpacity(0.9),
                        ],
                      ),
                      border: Border.all(
                        color: const Color(0xFF282828),
                        width: 2,
                      ),
                    ),
                    alignment: Alignment.center,
                    child: InkWell(
                      onTap: (){
                        Get.to(() => PromotionViewPage(
                            promotionCtl.promotionList.data![itemIndex].promotionId,
                            '${promotionCtl.urlEvent.value}${promotionCtl.promotionList.data![itemIndex].promotionPic.toString()}',
                            promotionCtl.promotionList.data![itemIndex].promotionName,
                            AppService.parseHtmlString(
                                promotionCtl.promotionList.data![itemIndex].promotionBody.toString()
                            )));
                      },
                      child: AppWidget.boldText(
                          context,
                          "ดูเพิ่มเติม",
                          14,
                          const Color(0xFFFFFFFF),
                          FontWeight.w500),
                    )
                ),
              )
            ],
          ),
    );
  }

  buildCatalog(){
    return Column(
      children: [
        const SizedBox(
          height: 30,
        ),
        Container(
          width: Get.width,
          height: 47,
          margin: const EdgeInsets.only(
              left: 54,
              right: 54
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 125,
                height: 35,
                child: Stack(
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 6,
                          height: 35,
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(5),
                            ),
                            color: Color(0xFFCA0E1E),
                          ),
                        ),
                        Container(
                            width: 100,
                            height: 35,
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topRight:  Radius.circular(15,),
                              ),
                              color: Color(0xFF282828),
                            ),
                            padding: const EdgeInsets.only(left: 15),
                            alignment: Alignment.centerLeft,
                            child: AppWidget.boldTextS(
                                context,
                                "โชว์รูมรถ",
                                12,
                                Colors.white,
                                FontWeight.w400)
                        ),
                      ],
                    ),
                    Positioned(
                        right: 0,
                        child: Image.asset('assets/image/service/menu_sa.png', width: 51,))
                  ],
                ),
              ),
              InkWell(
                onTap: (){
                  Get.to(() => const TalkSCPage());
                },
                child: SizedBox(
                  height: 47,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Row(
                        children: [
                          Container(
                              width: 180,
                              height: 35,
                              decoration: const BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topLeft:  Radius.circular(15,),
                                ),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xFFE8E6E2),
                                    Color(0xFFD9D8D5),
                                  ],
                                ),
                              ),
                              padding: const EdgeInsets.only(right: 10),
                              alignment: Alignment.centerRight,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  AppWidget.normalTextS(
                                      context,
                                      ": ติดต่อเซลส์อีซูซุ ",
                                      12,
                                      Colors.black,
                                      FontWeight.w400),
                                  const Text('คลิก!',
                                    style: TextStyle(
                                        decoration: TextDecoration.underline,
                                        fontSize: 12,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w700,
                                        fontFamily: "Prompt-Medium"
                                    ),),
                                ],
                              )
                          ),
                          Container(
                            width: 6,
                            height: 35,
                            decoration: const BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topRight: Radius.circular(5),
                              ),
                              color: Color(0xFFCA0E1E),
                            ),
                          ),
                        ],
                      ),
                      Positioned(
                          left: 0,
                          child: Image.asset('assets/image/service/menu_sale.png', width: 51,))
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 30,
        ),
        SizedBox(
          child: carouselRatio169(promotionCtl.carCatalogList.data!),
        ),
        const SizedBox(
          height: 20,
        ),
        GradientText(
          '${promotionCtl.carCatalogList.data![_currentCarIndex.value].catalogTitle}',
          textAlign: TextAlign.end,
          style: const TextStyle(
            fontFamily: 'Prompt-Medium',
            color: Color(0xFF282828),
            fontSize: 17,
            letterSpacing: 0.1,
          ),
          gradientDirection: GradientDirection.ttb,
          colors: [
            const Color(0xFF000000).withOpacity(0.7),
            const Color(0xFF000000)
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GradientText(
              ': คลิกที่รูปรถ',
              textAlign: TextAlign.end,
              style: const TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize: 15,
                letterSpacing: 0.1,
              ),
              gradientDirection: GradientDirection.ttb,
              colors: const [
                Color(0xFF664701),
                Color(0xFF1D1400)
              ],
            ),
            GradientText(
              ' เพื่อดูรายละเอียดเพิ่มเติม',
              textAlign: TextAlign.end,
              style: const TextStyle(
                  fontFamily: 'Prompt',
                  fontSize: 15,
                  letterSpacing: 0.1,
                  fontWeight: FontWeight.w100
              ),
              gradientDirection: GradientDirection.ttb,
              colors: const [
                Color(0xFF664701),
                Color(0xFF1D1400)
              ],
            ),
          ],
        )
      ],
    );
  }

  Widget carouselRatio169(List<dynamic> listData){
    return CarouselSlider.builder(
      itemCount: listData.length,
      options: CarouselOptions(
        height: 130,
        enlargeCenterPage: true,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: true,  // เลื่อนสไลด์ได้ไกล
        viewportFraction: 0.3,  // ส่วนที่เปิดเผยของรายการภาพในแต่ละสไลด์
        aspectRatio: 16 / 9,  // อัตราส่วนกว้างต่อสูงของภาพในแต่ละสไลด์
        enlargeFactor: 0.5,
        onPageChanged: (index, reason) {
          _currentCarIndex.value = index;
        },
      ),
      itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
          InkWell(
            onTap: (){
              AppService.launchUrl(promotionCtl.carCatalogList.data![itemIndex].catalogUrl);
            },
            child: Image.network(
              '${listData[itemIndex].catalogImage}',
              // width: 174,
              height: 130,
              fit: BoxFit.cover,
              errorBuilder: (BuildContext context,
                  Object exception,
                  StackTrace? stackTrace) {
                return Container(
                  padding: const EdgeInsets.all(10),
                  child: const Icon(
                    Icons.error,
                    size: 50,
                    color: Color(0xFFFFB100),
                  ),);
              },
              loadingBuilder: (BuildContext context,
                  Widget child,
                  ImageChunkEvent? loadingProgress) {
                if (loadingProgress == null) {
                  return child;
                }
                return Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                    width: 50,
                    height: 50,
                    child:
                    CircularProgressIndicator(
                      strokeWidth: 2,
                      color:
                      const Color(0xFFFFFFFF),
                      value: loadingProgress
                          .expectedTotalBytes !=
                          null
                          ? loadingProgress
                          .cumulativeBytesLoaded /
                          loadingProgress
                              .expectedTotalBytes!
                          : null,
                    ),
                  ),
                );
              },
            ),
          ),
    );
  }


}
