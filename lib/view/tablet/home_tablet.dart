import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/lifetime_appointment_controller.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/home/<USER>/dialog_newcar.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/event_register/detail_event_register.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/list_service.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/minilike/mini_like.dart';
import 'package:mapp_prachakij_v3/view/tablet/list_service_tablet.dart';
import 'package:mapp_prachakij_v3/view/tester/webview.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../component/widget.dart';
import '../../controller/setting_controller/MR_controller.dart';
import '../../controller/setting_controller/car_repair_controller.dart';
import '../../controller/setting_controller/chat_in_app_controller.dart';
import '../../controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import '../../controller/setting_controller/login_controller/login_controller.dart';
import '../../controller/setting_controller/new_car_controller.dart';
import '../../controller/setting_controller/page_controller.dart';
import '../../controller/setting_controller/profile_controller.dart';
import '../../controller/setting_controller/setting_controller.dart';
import '../car_repair_status/home_car_repair_status.dart';
import '../home/<USER>/my-car.dart';
import '../home/<USER>/new_car.dart';

class HomeTablet extends StatefulWidget {
  const HomeTablet({Key? key}) : super(key: key);

  @override
  State<HomeTablet> createState() => _HomeTabletState();
}

class _HomeTabletState extends State<HomeTablet> {

  final profileCtl = Get.put(ProfileController());
  final loginCtl = Get.put(LoginController());
  final pageCtl = Get.put(PageSelectController());
  final centerCtl = Get.put(SettingController());
  final likePointCtl = Get.put(LikePointController());
  final mrCtl = Get.put(ReferralMRController());
  final newCarCtl = Get.put(NewCarController());
  final lifetimeAppointment = Get.put(LifetimeAppointmentController());

  final webviewCtl = Get.put(WebViewLikePointController());

  final chatInAppCtl = Get.put(ChatInAppController());
  final carRepairCtl = Get.put(CarRepairController());
  @override
  Widget build(BuildContext context) {
    return Obx(() => NotificationListener(
      onNotification: (notification) {
        if (notification is ScrollUpdateNotification && notification.metrics.axis == Axis.vertical) {
          pageCtl.scrollParam.value = notification.metrics.pixels;
        }
        if(pageCtl.scrollParam.value > 100 ){
          newCarCtl.isShow.value = false;
        }
        return true;
      },
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 130,),
                // chatInApp(),
                profileCtl.token.value != null ? infoCard() : Container(),
                const SizedBox(
                  height: 10,
                ),
                // const HomeCarRepairStatusPage(),
                profileCtl.token.value != null ? newCarCtl.status.value == 200 && newCarCtl.newCar.newCarList!.isNotEmpty ? const NewCarCard() : const SizedBox() : const SizedBox(),
                SizedBox(
                  height: newCarCtl.status.value == 200 ? 10 : 0,
                ),
                carDetail(),
                SizedBox(
                  height: lifetimeAppointment.status.value == 200 ? 10 : 0,
                ),
                const ListServiceTabletPage(),
                const SizedBox(
                  height: 10,
                ),
                InkWell(
                  onTap: (){
                    if(profileCtl.token.value == null){
                      AppWidget.showDialogPageSlide(context, const LoginPage());
                    } else {
                      mrCtl.navigatorMR();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.only(left: 8,right: 8),
                    width: Get.width,
                    height: 176,
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            width: Get.width,
                            height: 130,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                stops: [0.0,1.0],
                                colors: [
                                  Color(0xFFFEFEFE),
                                  Color(0xFFF8F8F8),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  spreadRadius: 0,
                                  blurRadius: 10,
                                  offset: const Offset(0, 4), // changes position of shadow
                                ),
                              ],
                            ),
                          ),
                        ),
                        Align(
                            alignment: Alignment.topLeft,
                            child: Container(
                              padding: const EdgeInsets.only(
                                  left: 16,
                                  bottom: 50
                              ),
                              child: Image.asset("assets/image/MR/LadySky.png"),
                            )
                        ),
                        Container(
                          padding: const EdgeInsets.only(
                              left: 10,
                              right: 10,
                              bottom: 8
                          ),
                          alignment: Alignment.bottomCenter,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.boldText(context, "กิจกรรมแนะนำลูกค้า", 12, const Color(0xFF282828), FontWeight.w600),
                              RichText(
                                text: const TextSpan(
                                    children: [
                                      TextSpan(
                                          text: "มาร่วมเป็นส่วนหนึ่งกับเรา",
                                          style: TextStyle(
                                              color: Color(0xFF282828),
                                              fontSize: 12,
                                              fontFamily: 'Prompt',
                                              fontWeight: FontWeight.w300
                                          )
                                      ),
                                      TextSpan(
                                          text: " ยิ่งแนะนำ ยิ่งได้มาก ",
                                          style: TextStyle(
                                              color: Color(0xFF282828),
                                              fontSize: 12,
                                              fontFamily: 'Prompt-Medium',
                                              fontWeight: FontWeight.w600
                                          )
                                      ),
                                      TextSpan(
                                          text: "\nสร้างรายได้แบบง่ายๆ",
                                          style: TextStyle(
                                              color: Color(0xFF282828),
                                              fontSize: 12,
                                              fontFamily: 'Prompt',
                                              fontWeight: FontWeight.w300
                                          )
                                      ),
                                    ]
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Container(
                                  width: Get.width,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: [0.0,1.0],
                                      colors: [
                                        Color(0xFF664701),
                                        Color(0xFF1D1400),
                                      ],
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: RichText(
                                    text: const TextSpan(
                                        children: [
                                          TextSpan(
                                              text: "แนะนำ รับคะแนนเพิ่ม ",
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 14,
                                                  fontFamily: 'Prompt',
                                                  fontWeight: FontWeight.w200
                                              )
                                          ),
                                          TextSpan(
                                              text: "คลิก!",
                                              style: TextStyle(
                                                  color: Color(0xFFFFB100),
                                                  fontSize: 14,
                                                  fontFamily: 'Prompt-Medium',
                                                  fontWeight: FontWeight.w700
                                              )
                                          ),
                                        ]
                                    ),
                                  )
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                // profileCtl.token.value != null && webviewCtl.isOpen.isTrue ?
                // InkWell(
                //   onTap: ()  {
                //     // webviewCtl.phoneEncode.value = base64Encode(utf8.encode(profileCtl.profile.value.phoneFirebase.toString()));
                //     // webviewCtl.firstName.value = profileCtl.profile.value.firstname.toString();
                //     // webviewCtl.lastName.value = profileCtl.profile.value.lastname.toString();
                //     // Get.to(() => const WebViewPage());
                //
                //     Get.to(() => const SigDetail());
                //   },
                //   child: Container(
                //     margin: const EdgeInsets.only(
                //       left: 18,
                //       right: 18,
                //     ),
                //     alignment: Alignment.center,
                //     width: Get.width,
                //     height: 50,
                //     decoration: BoxDecoration(
                //       borderRadius: BorderRadius.circular(10),
                //       color: Colors.red,
                //     ),
                //     child: AppWidget.boldText(context, "For Tester LikePoint", 20, Colors.white, FontWeight.w400),
                //   ),
                // ) : const SizedBox(),
                const SizedBox(
                  height: 90,
                ),
              ],
            ),
            Column(
              children: [
                const SizedBox(
                  height: 130,
                ),
                newCarCtl.isShow.isTrue ? const DialogNewCar() : const SizedBox(),
              ],
            ),
          ],
        ),
      ),
    ));
  }

  Widget carDetail() {
    if(profileCtl.token.value != null) {
      if(carRepairCtl.status.value == 200) {
        return const HomeCarRepairStatusPage();
      } else if(lifetimeAppointment.status.value == 200) {
        return const MyCarCard();
      } else {
        return const SizedBox();
      }
    } else {
      return const SizedBox();
    }
  }

  Widget infoCard() {
    return Padding(
      padding: const EdgeInsets.only(
        left: 8,
        right: 8,
      ),
      child: Container(
          width: Get.width,
          height: 140,
          decoration: ShapeDecoration(
            gradient: const LinearGradient(
              begin: Alignment(0.00, -1.00),
              end: Alignment(0, 1),
              colors: [Colors.white, Color(0xFFF7F7F7)],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            shadows: const [
              BoxShadow(
                color: Color(0x0C000000),
                blurRadius: 10,
                offset: Offset(0, 4),
                spreadRadius: 0,
              )
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // TODO :: SCAN QR CODE
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    child: InkWell(
                        hoverColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () async {
                          var value = await Get.to(() => const GetQrCodePage());
                          if(value != null){
                            setState(() {
                              AppLoader.loader(context);
                            });
                            var responseEvent = await AppApi.getEventRegisterByID(context, value);
                            if(responseEvent["status"] == 200){
                              Get.to(() => DetailEventRegisterPage(responseEvent["result"]));
                            }
                          }
                        },
                        child: SvgPicture.asset("assets/image/home/<USER>")),
                  ),
                  // TODO :: SCAN QR CODE

                  // TODO :: LIKEWALLET
                  Container(
                    width: 145,
                    margin: const EdgeInsets.only(top: 5, bottom: 5, right: 8),
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    decoration: const BoxDecoration(
                      border: Border(
                        left: BorderSide(width: 1, color: Color(0xFFE8E6E2)),
                        // right: BorderSide(width: 1, color: Color(0xFFE8E6E2)),
                      ),
                    ),
                    child: InkWell(
                        hoverColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        onTap: () async {
                          try {
                            if (profileCtl.token.value != null) {
                              await AppWidget.showDialogPage(context, const MiniLikePage());
                            } else {
                              await AppWidget.showDialogPageSlide(context, const LoginPage());
                            }
                          } catch (e) {
                            // Handle any exceptions or errors here
                            print('Navigation error: $e');
                          }
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Image.asset('assets/image/home/<USER>', width: 16,),
                                Obx(() => profileCtl.token.value != null
                                    ? likePointCtl.likePoint.value != 0.00
                                    ? AppWidget.boldTextS(
                                    context,
                                    " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                                    14,
                                    const Color(0xFF282828),
                                    FontWeight.w700)
                                    : AppWidget.boldTextS(
                                    context,
                                    " 0",
                                    14,
                                    const Color(0xFF282828),
                                    FontWeight.w700)
                                    : AppWidget.boldTextS(
                                    context,
                                    " 0",
                                    14,
                                    const Color(0xFF282828),
                                    FontWeight.w700),),
                              ],
                            ),

                            Obx(() => profileCtl.token.value != null
                                ? likePointCtl.likePoint.value != 0.00
                                ? AppWidget.boldTextS(
                                context,
                                "฿ ${AppService.numberFormat(likePointCtl.likePoint.value/100)}",
                                10,
                                const Color(0xFF895F00),
                                FontWeight.w500)
                                : AppWidget.boldTextS(
                                context,
                                " 0",
                                10,
                                const Color(0xFF895F00),
                                FontWeight.w500)
                                : AppWidget.boldTextS(
                                context,
                                " 0",
                                11,
                                const Color(0xFF895F00),
                                FontWeight.w500),),
                          ],
                        )
                    ),
                  ),
                  // TODO :: LIKEWALLET

                  // // TODO :: LIKEPOINT 2.0
                  // InkWell(
                  //   hoverColor: Colors.transparent,
                  //   focusColor: Colors.transparent,
                  //   highlightColor: Colors.transparent,
                  //   splashColor: Colors.transparent,
                  //   onTap: () async {
                  //     try {
                  //       webviewCtl.phoneEncode.value = base64Encode(utf8.encode(profileCtl.profile.value.phoneFirebase.toString()));
                  //       webviewCtl.firstName.value = profileCtl.profile.value.firstname.toString();
                  //       webviewCtl.lastName.value = profileCtl.profile.value.lastname.toString();
                  //
                  //       print("phoneEncode => ${webviewCtl.phoneEncode.value}");
                  //       print("firstName => ${webviewCtl.firstName.value}");
                  //       print("lastName => ${webviewCtl.lastName.value}");
                  //
                  //       // await Navigator.of(context).push(MaterialPageRoute(
                  //       //   builder: (context) => const WebViewPage(),
                  //       // ));
                  //
                  //       await Get.to(() => const WebViewPage());
                  //     } catch (e) {
                  //       // Handle any exceptions or errors here
                  //       print('Navigation error: $e');
                  //     }
                  //   },
                  //   child: Container(
                  //     width: 145,
                  //     margin: const EdgeInsets.only(top: 5, bottom: 5),
                  //     child: Column(
                  //       mainAxisAlignment: MainAxisAlignment.center,
                  //       crossAxisAlignment: CrossAxisAlignment.start,
                  //       children: [
                  //         Row(
                  //           children: [
                  //             Image.asset('assets/image/icon-likepoint.png', width: 18,),
                  //             const SizedBox(width: 5),
                  //             AppWidget.boldTextS(
                  //                 context,
                  //                 webviewCtl.balanceLikePoint.value,
                  //                 14,
                  //                 const Color(0xFF282828),
                  //                 FontWeight.w700),
                  //           ],
                  //         ),
                  //
                  //         AppWidget.boldTextS(
                  //             context,
                  //             "฿ ${ webviewCtl.balanceLikePointTHB.value}",
                  //             10,
                  //             const Color(0xFF895F00),
                  //             FontWeight.w500),
                  //       ],
                  //     ),
                  //   ),
                  // )
                  // // TODO :: LIKEPOINT 2.0

                ],
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      margin: const EdgeInsets.only(left: 16, right: 16),
                      child:   Divider(
                        color: const Color(0xFFE8E6E2),
                        thickness: 1,
                      )
                  ),

                  Container(
                    margin: const EdgeInsets.only(left: 16, right: 16),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(),
                          child:  Image.asset('assets/image/info-red.png', height: 10,),
                        ),
                        const SizedBox(width: 6),
                        AppWidget.boldText(
                            context,
                            "ประกาศปรับรูปแบบคะแนนสะสมใหม่!",
                            14,
                            const Color(0xFF664701),
                            FontWeight.w500),
                      ],
                    ),
                  ),
                  Container(
                      margin: const EdgeInsets.only(left: 16, right: 16),
                      width: Get.width,
                      child:  AppWidget.normalText(
                          context,
                          "สร้างประสบการณ์กับคะแนนสะสมรูปแบบใหม่ และสิทธิประโยชน์\nที่มากกว่าเดิม ที่นี่เร็วๆ นี้",
                          12,
                          const Color(0xBF1A1818),
                          FontWeight.w500)
                  ),

                ],
              )
            ],
          )
      ),
    );

  }

  Widget chatInApp(){
    if(profileCtl.token.value != null) {
      return Obx(() => Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 8,
              right: 8,
            ),
            child: InkWell(
              hoverColor: Colors.transparent,
              onTap: () async {
                if(chatInAppCtl.createGroup.isFalse){
                  // await chatInAppCtl.checkRegister(context);
                  var ckSubRegister = await chatInAppCtl.firstCheckTG();

                  if(ckSubRegister == 'false'){
                    await chatInAppCtl.sendOTPTG(context);
                  } else if (ckSubRegister == "success") {
                    // Get.find<NotifyController>().delNotiChat();
                    Get.to(() => const WebViewTelegram());
                    return ;
                  }
                }
                ///
              },
              child: Container(
                width: Get.width,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0.0,1.0],
                    colors: chatInAppCtl.createGroup.isFalse
                        ? [
                      const Color(0xFFFFFFFF),
                      const Color(0xFFF8F8F8),
                    ]
                        : [
                      const Color(0xFF000000).withOpacity(0.2),
                      const Color(0xFF000000).withOpacity(0.2),
                    ],
                  ),
                ),
                child: chatInAppCtl.createGroup.isFalse ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset('assets/icon/contact_icon.png', width: 30,),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.boldTextS(
                        context,
                        "พูดคุยกับประชากิจฯ ",
                        14,
                        const Color(0xFF282828),
                        FontWeight.w500),
                    AppWidget.normalTextS(
                        context,
                        "ได้ที่นี่",
                        14,
                        const Color(0xFF905F00),
                        FontWeight.w400)
                  ],
                ) : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset('assets/icon/contact_icon.png', width: 30,),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.boldTextS(
                        context,
                        "กำลังสร้างกลุ่ม กรุณารอสักครู่",
                        14,
                        const Color(0xFF282828),
                        FontWeight.w500),
                    const SizedBox(
                      width: 10,
                    ),
                    Countdown(
                      seconds: 90,
                      build: (BuildContext context, double time) => Text(
                        time.toString(),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF905F00),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      interval: const Duration(milliseconds: 1000),
                      onFinished: () {
                        chatInAppCtl.createGroup.value = false;
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ));
    } else {
      return const SizedBox();
    }
  }
}
