import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class AboutAppPage extends StatefulWidget {
  const AboutAppPage({Key? key}) : super(key: key);

  @override
  State<AboutAppPage> createState() => _AboutAppPageState();
}

class _AboutAppPageState extends State<AboutAppPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: 1.sw,
            height: 1.sh,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0, 1.0],
                colors: [
                  Color(0xFFE8E6E2),
                  Color(0xFFD9D8D5),
                ],
              ),
            ),
          ),
          Container(
            width: 1.sw,
            height: 0.2.sh,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFFFFB100).withOpacity(1),
                  const Color(0xFFFFC700).withOpacity(0.7),
                  const Color(0xFFFFC700).withOpacity(0.4),
                  const Color(0xFFFFC700).withOpacity(0),
                ],
              ),
            ),
            child: Container(
              margin: EdgeInsets.only(
                left: 0.05.sw,
                right: 0.05.sw,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: (){
                      Navigator.pop(context);
                    },
                    child: SizedBox(
                      width: 0.04.sh,
                      height: 0.04.sh,
                      child: Icon(Icons.arrow_back_ios_new,
                        color: const Color(0xFF282828),
                        size: 0.02.sh,
                      ),
                    ),
                  ),
                  AppWidget.boldTextS(
                    context,
                    "เกี่ยวกับแอพ",
                    18.sp,
                    const Color(0xFF282828),
                    FontWeight.w300,
                  ),
                  SizedBox(
                    width: 0.03.sh,
                    height: 0.03.sh,
                    // color: Colors.red,
                  ),
                ],
              ),
            ),
          ),
          buildContent(),
        ],
      ),
    );
  }

  buildContent(){
    return Container(
      margin: EdgeInsets.only(
        top: 0.17.sh,
        left: 0.08.sw,
        right: 0.08.sw
      ),
      child: Column(
        children: [
          InkWell(
            onTap: (){
              AppService.launchUrl('https://www.agilesoftgroup.com/pms_term.html');
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppWidget.normalText(
                  context,
                  "ข้อกำหนดการใช้บริการ",
                  17.sp,
                  const Color(0xFF282828),
                  FontWeight.w300,
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 0.04.sw,
                  color: const Color(0xFF895F00),
                )
              ],
            )
          ),
          SizedBox(
            height: 0.03.sh,
          ),
          InkWell(
              onTap: (){
                AppService.launchUrl('https://www.agilesoftgroup.com/pms_policy.html');
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppWidget.normalText(
                    context,
                    "นโยบายคุ้มครองข้อมูลส่วนบุคคล",
                    17.sp,
                    const Color(0xFF282828),
                    FontWeight.w300,
                  ),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 0.04.sw,
                    color: const Color(0xFF895F00),
                  )
                ],
              )
          ),
        ],
      ),
    );
  }
}
