// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:mapp_prachakij_v3/component/alert.dart';
// import 'package:mapp_prachakij_v3/component/firestore.dart';
// import 'package:mapp_prachakij_v3/component/version.dart';
// import 'package:mapp_prachakij_v3/component/widget.dart';
// import 'package:mapp_prachakij_v3/controller/service/service.dart';
// import 'package:mapp_prachakij_v3/view/drawer/contact/contact_us.dart';
// import 'package:mapp_prachakij_v3/view/profile/profile.dart';
// import 'package:simple_gradient_text/simple_gradient_text.dart';
// import 'package:flutter/foundation.dart';
//
//
// class DrawersPage extends StatefulWidget {
//   const DrawersPage(BuildContext context, {Key? key ,this.homeContext}) : super(key: key);
//
//   final homeContext;
//
//   @override
//   State<DrawersPage> createState() => _DrawersPageState(this.homeContext);
// }
//
// class _DrawersPageState extends State<DrawersPage> {
//   final homeContext;
//
//   _DrawersPageState(this.homeContext);
//
//   String currentVersion = "";
//
//   getVersion() async {
//     try {
//       var version = await FireStore.getCurrentVersion();
//       setState(() {
//         currentVersion = version.toString();
//       });
//       print("currentVersion");
//       print(currentVersion);
//     } catch (e) {
//       AppService.sendError(e, 'ERROR : getVersion in Drawers');
//     }
//   }
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     getVersion();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       child: SingleChildScrollView(
//         child: ConstrainedBox(
//           constraints: BoxConstraints(
//             minHeight: 1.sh,
//           ),
//           child: Container(
//             decoration: BoxDecoration(
//               color: const Color(0xffFFFFFF).withOpacity(0.5),
//             ),
//             child: Column(
//               children: [
//                 SizedBox(
//                   height: 0.08.sh,
//                 ),
//                 Container(
//                   margin: EdgeInsets.only(
//                     left: 0.2.sw,
//                   ),
//                   child: SvgPicture.asset(
//                     'assets/image/drawer/drawer_logo.svg',
//                     width: 0.1.sw,
//                     height: 0.15.sh,
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.05.sh,
//                 ),
//                 InkWell(
//                   onTap: (){
//                     Navigator.push(context,
//                         MaterialPageRoute(builder: (context) => const ProfilePage()));
//                   },
//                   child: Container(
//                     width: 1.sw,
//                     padding: EdgeInsets.only(
//                       right: 0.05.sw
//                     ),
//                     child: GradientText(
//                       'ข้อมูลส่วนตัว',
//                       textAlign: TextAlign.end,
//                       style: TextStyle(
//                         fontFamily: 'Prompt',
//                         color: const Color(0xFF282828),
//                         fontSize: 17.sp,
//                         letterSpacing: 0.1,
//                       ),
//                       gradientDirection: GradientDirection.ttb,
//                       colors: [
//                         const Color(0xFF000000).withOpacity(0.5),
//                         const Color(0xFF141322)
//                       ],
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 const Divider(
//                   thickness: 0.5,
//                   indent: 0,
//                   endIndent: 0,
//                   color: Color(0xFFF5F5F5),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 InkWell(
//                   onTap: (){
//                     Navigator.push(context,
//                         MaterialPageRoute(builder: (context) => const ContactUsPage()));
//                   },
//                   child: Container(
//                     width: 1.sw,
//                     padding: EdgeInsets.only(
//                         right: 0.05.sw
//                     ),
//                     child: GradientText(
//                       'ติดต่อเรา',
//                       textAlign: TextAlign.end,
//                       style: TextStyle(
//                         fontFamily: 'Prompt',
//                         color: const Color(0xFF282828),
//                         fontSize: 17.sp,
//                         letterSpacing: 0.1,
//                       ),
//                       gradientDirection: GradientDirection.ttb,
//                       colors: [
//                         const Color(0xFF000000).withOpacity(0.5),
//                         const Color(0xFF141322)
//                       ],
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 const Divider(
//                   thickness: 0.5,
//                   indent: 0,
//                   endIndent: 0,
//                   color: Color(0xFFF5F5F5),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 InkWell(
//                   onTap: (){
//                     AppAlert.showNewAccept(context, "ข้อเสนอแนะ", "ยังไม่ได้เชื่อม ข้อเสนอแนะ", "ตกลง");
//                   },
//                   child: Container(
//                     width: 1.sw,
//                     padding: EdgeInsets.only(
//                         right: 0.05.sw
//                     ),
//                     child: GradientText(
//                       'ข้อเสนอแนะ',
//                       textAlign: TextAlign.end,
//                       style: TextStyle(
//                         fontFamily: 'Prompt',
//                         color: const Color(0xFF282828),
//                         fontSize: 17.sp,
//                         letterSpacing: 0.1,
//                       ),
//                       gradientDirection: GradientDirection.ttb,
//                       colors: [
//                         const Color(0xFF000000).withOpacity(0.5),
//                         const Color(0xFF141322)
//                       ],
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 const Divider(
//                   thickness: 0.5,
//                   indent: 0,
//                   endIndent: 0,
//                   color: Color(0xFFF5F5F5),
//                 ),
//                 SizedBox(
//                   height: 0.01.sh,
//                 ),
//                 InkWell(
//                   onTap: (){
//                     AppAlert.showNewAccept(context, "เกี่ยวกับแอพ", "ยังไม่ได้เชื่อม เกี่ยวกับแอพ", "ตกลง");
//                   },
//                   child: Container(
//                     width: 1.sw,
//                     padding: EdgeInsets.only(
//                         right: 0.05.sw
//                     ),
//                     child: GradientText(
//                       'เกี่ยวกับแอพ',
//                       textAlign: TextAlign.end,
//                       style: TextStyle(
//                         fontFamily: 'Prompt',
//                         color: const Color(0xFF282828),
//                         fontSize: 17.sp,
//                         letterSpacing: 0.1,
//                       ),
//                       gradientDirection: GradientDirection.ttb,
//                       colors: [
//                         const Color(0xFF000000).withOpacity(0.5),
//                         const Color(0xFF141322)
//                       ],
//                     ),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.3.sh,
//                 ),
//                 InkWell(
//                   onTap: (){
//                     alertLogout();
//                   },
//                   child: Container(
//                     width: 0.35.sw,
//                     height: 0.05.sh,
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.only(
//                         topLeft: Radius.circular(0.03.sh),
//                         topRight: Radius.circular(0.03.sh),
//                         bottomLeft: Radius.circular(0.03.sh),
//                         bottomRight: Radius.circular(0.03.sh),
//                       ),
//                       color: const Color(0xFF282828),
//                     ),
//                     alignment: Alignment.center,
//                     child: AppWidget.normalTextS(
//                         context,
//                         "ออกจากระบบ",
//                         16.sp,
//                         Colors.white70,
//                         FontWeight.w400),
//                   ),
//                 ),
//                 SizedBox(
//                   height: 0.02.sh,
//                 ),
//                 Row(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     AppWidget.normalTextS(
//                         context,
//                         "Version : ",
//                         14.sp,
//                         Colors.black,
//                         FontWeight.w400),
//                     AppWidget.boldTextS(
//                         context,
//                         currentVersion,
//                         14.sp,
//                         Colors.black,
//                         FontWeight.w400),
//                   ],
//                 )
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   void alertLogout() async {
//     var result = await AppAlert.showConfirm(
//       context,
//       'Logout?',
//       'คุณต้องการออกจากระบบ?',
//       'ตกลง',
//     );
//
//     if (result == true) {
//       AppService.logout(context);
//     }
//   }
//
// }
