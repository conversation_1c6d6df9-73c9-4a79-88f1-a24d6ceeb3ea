import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/center_controller.dart';
import 'package:mapp_prachakij_v3/model/phone_callcenter.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';


class ContactUsPage extends StatefulWidget {
  const ContactUsPage({Key? key}) : super(key: key);

  @override
  State<ContactUsPage> createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  Completer<GoogleMapController> _controller = Completer();

  ResponsePhoneCenter? resPhoneCenter;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  getData() async {
    await getPhoneCallcenter();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: 1.sw,
            height: 1.sh,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0, 1.0],
                colors: [
                  Color(0xFFF3F3F3),
                  Color(0xFFFFFFFF),
                ],
              ),
            ),
          ),
          Container(
            width: 1.sw,
            height: 0.2.sh,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFFFFB100).withOpacity(1),
                  const Color(0xFFFFC700).withOpacity(0.7),
                  const Color(0xFFFFC700).withOpacity(0.4),
                  const Color(0xFFFFC700).withOpacity(0),
                ],
              ),
            ),
            child: Container(
              margin: EdgeInsets.only(
                left: 0.05.sw,
                right: 0.05.sw,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: (){
                      Navigator.pop(context);
                      },
                    child: SizedBox(
                      width: 0.04.sh,
                      height: 0.04.sh,
                      child: Icon(Icons.arrow_back_ios_new,
                        color: const Color(0xFF282828),
                        size: 0.02.sh,
                      ),
                    ),
                  ),
                  AppWidget.boldTextS(
                    context,
                    "ติดต่อเรา",
                    18.sp,
                    const Color(0xFF282828),
                    FontWeight.w300,
                  ),
                  SizedBox(
                    width: 0.04.sh,
                    height: 0.04.sh,
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
              top: 0.2.sh,
              left: 0.06.sw,
              right: 0.06.sw,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppWidget.boldText(
                  context,
                  "ที่อยู่บริษัท",
                  17.sp,
                  const Color(0xFF664701),
                  FontWeight.w300,
                ),
                SizedBox(
                  height: 0.015.sh,
                ),
                AppWidget.normalText(
                  context,
                  "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด",
                  16.sp,
                  const Color(0xFF707070).withOpacity(0.8),
                  FontWeight.w300,
                ),
                SizedBox(
                  height: 0.015.sh,
                ),
                AppWidget.normalText(
                  context,
                  "50/11 ม.2 ถ.สุขุมวิท ต.ท่าช้าง อ.เมือง จ.จันทบุรี",
                  16.sp,
                  const Color(0xFF707070).withOpacity(0.8),
                  FontWeight.w300,
                ),
                SizedBox(
                  height: 0.025.sh,
                ),
                SizedBox(
                  width: 1.sw,
                  height: 0.25.sh,
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                      bottomRight: Radius.circular(25),
                      bottomLeft: Radius.circular(25),
                    ),
                    child: GoogleMap(
                      markers: {
                        const Marker(
                          markerId: MarkerId("Prachakij"),
                          position: LatLng(12.6628558, 102.0784695),
                          infoWindow: InfoWindow(
                            title: "Prachakij",
                            snippet: "บริษัท ประชากิจ",
                          ),
                        ),
                      },
                      initialCameraPosition: const CameraPosition(
                        target: LatLng(12.6628558, 102.0784695),
                        zoom: 12,
                      ),
                      mapType: MapType.normal,
                      myLocationEnabled: true,
                      zoomControlsEnabled: false,
                      myLocationButtonEnabled: true,
                      onMapCreated: (GoogleMapController controller) {
                        _controller.complete(controller);
                      },
                    ),
                  ),
                ),
                SizedBox(
                  height: 0.025.sh,
                ),
                const Divider(
                  thickness: 0.5,
                  indent: 0,
                  endIndent: 0,
                  color: Color(0xFFF5F5F5),
                ),
                SizedBox(
                  height: 0.015.sh,
                ),
                Center(
                  child: AppWidget.boldText(
                    context,
                    "ช่องทางการติดต่อ",
                    17.sp,
                    const Color(0xFF664701),
                    FontWeight.w300,
                  ),
                ),
                SizedBox(
                  height: 0.015.sh,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: (){
                        AppService.callPhone(
                          resPhoneCenter!.phoneNumber,
                        );
                      },
                      child: Image.asset('assets/image/drawer/contactus/IconblackContact.png',
                        width: 0.05.sh,
                        height: 0.05.sh,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 0.05.sw,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  getPhoneCallcenter() async {
    resPhoneCenter = await CenterController.getPhoneCenter(context, "คอลเซนเตอร์");
  }

}
