import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';

import '../../home/<USER>';

class FeedbackPage extends StatefulWidget {
  const FeedbackPage({Key? key}) : super(key: key);

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {

  final TextEditingController _commentController = TextEditingController();
  final webViewLikePointController = Get.find<WebViewLikePointController>();

  RxInt starRating = 0.obs;
  RxList<dynamic> listComment = [].obs;
  String? optionSelect;

  final profileCtl = Get.put(ProfileController());
  final settingCtl = Get.put(SettingController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFE8E6E2),
                    Color(0xFFD9D8D5),
                  ],
                ),
              ),
            ),
            Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFFFFB100).withOpacity(1),
                    const Color(0xFFFFC700).withOpacity(0.7),
                    const Color(0xFFFFC700).withOpacity(0.4),
                    const Color(0xFFFFC700).withOpacity(0),
                  ],
                ),
              ),
            ),
            Container(
              width: Get.width,
              margin: const EdgeInsets.only(
                top: 50,
              ),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFF5F5F5),
                    Color(0xFFFFFFFF),
                  ],
                ),
              ),
              child: Container(
                margin: const EdgeInsets.only(
                  top: 20,
                  left: 20,
                  right: 20,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: (){
                              Navigator.pop(context);
                            },
                            child: const SizedBox(
                              width: 50,
                              height: 50,
                              child: Icon(Icons.arrow_back_ios_new,
                                color: Color(0xFF282828),
                                size: 18,
                              ),
                            ),
                          ),
                          AppWidget.boldTextS(
                            context,
                            "ข้อเสนอแนะ",
                            18,
                            const Color(0xFF282828),
                            FontWeight.w300,
                          ),
                          const SizedBox(
                            width: 50,
                            height: 50,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      SizedBox(
                        height: Get.height * 0.9,
                        child: buildContent(),
                      )
                    ],
                  ),
                )
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildContent(){
    return Column(
      children: [
        AppWidget.boldTextS(
          context,
          "กรุณาให้ดาวสำหรับความพึงพอใจ",
          16,
          const Color(0xFF707070),
          FontWeight.w300,
        ),
        const SizedBox(
          height: 14,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            buildStar(1),
            buildStar(2),
            buildStar(3),
            buildStar(4),
            buildStar(5),
          ],
        ),
        const SizedBox(
          height: 14,
        ),
        AppWidget.boldTextS(
          context,
          "สิ่งที่คุณพึงพอใจสำหรับการใช้งาน?",
          16,
          const Color(0xFF707070),
          FontWeight.w300,
        ),
        const SizedBox(
          height: 14,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildOption(
                'แอพพลิเคชั่นนี้ มีประโยชน์มาก', 0),
            _buildOption(
                'ใช้งานง่ายกว่าเวอชั่นก่อนหน้านี้', 1),
            _buildOption(
                'สะดวกสบาย รวดเร็ว', 2),
            _buildOption(
                'อื่นๆ...(แนะนำได้เลยค่ะ)', 3),
          ],
        ),
        Container(
          margin: const EdgeInsets.only(
            left: 10,
            right: 10,
          ),
          padding: const EdgeInsets.all(
            20,
          ),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(
              Radius.circular(
                25,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              )
            ],
            color: Colors.white,
          ),
          child: TextField(
            controller: _commentController,
            maxLines: 8,
            keyboardType: TextInputType.multiline,
            style: TextStyle(
              fontFamily: 'Prompt',
              fontSize: 16,
              color: const Color(0xFF707070),
              letterSpacing: 0.4,
              shadows: <Shadow>[
                Shadow(
                  offset: const Offset(0, 1),
                  blurRadius: 1.0,
                  color: const Color(0xFF000000).withOpacity(0.15),
                ),
              ],
            ),
            decoration: InputDecoration.collapsed(
              hintText: "กรอกคำแนะนำได้ที่นี่เลย...",
              hintStyle: TextStyle(
                fontFamily: 'Prompt',
                fontSize: 16,
                color: const Color(0xFF707070),
                letterSpacing: 0.4,
                shadows: <Shadow>[
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 1.0,
                    color: const Color(0xFF000000).withOpacity(0.15),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            Text(
              'ระบบปฏิบัติการ : ',
              style: TextStyle(
                fontFamily: 'Prompt',
                color: const Color(0xFF4B4B4B),
                fontSize: 14,
                letterSpacing: 0.4,
                shadows: <Shadow>[
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 1.0,
                    color:
                    const Color(0xFF000000).withOpacity(0.15),
                  ),
                ],
              ),
            ),
            Text(
              settingCtl.os.value,
              style: TextStyle(
                fontFamily: 'Prompt',
                color: const Color(0xFF4B4B4B),
                fontSize: 14,
                letterSpacing: 0.4,
                shadows: <Shadow>[
                  Shadow(
                    offset: const Offset(0, 1),
                    blurRadius: 1.0,
                    color:
                    const Color(0xFF000000).withOpacity(0.15),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 16,
        ),
        SizedBox(
          width: 120,
          height: 50,
          child: GestureDetector(
            onTap: () async {
              FocusScope.of(context).unfocus();
              await saveFeedback();
            },
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(
                  Radius.circular(
                    25
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  )
                ],
                color: const Color(0xFFFFB100),
              ),
              child: Text(
                "ส่งข้อมูล",
                style: TextStyle(
                  fontFamily: 'Prompt',
                  fontSize: 16,
                  letterSpacing: 0.4,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  shadows: <Shadow>[
                    Shadow(
                      offset: const Offset(0, 1),
                      blurRadius: 1.0,
                      color:
                      const Color(0xFF000000).withOpacity(0.15),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  buildStar(number){
    return InkWell(
      onTap: (){
        setState(() {
          starRating.value = number;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
        ),
        child: starRating.value >= number
            ? const Icon(
          // FontAwesomeIcons.solidStar,
          Icons.star,
          size: 18,
          color: Color(0xFFFFB100),
        )
            : const Icon(
          // FontAwesomeIcons.star,
          Icons.star_border,
          size: 18,
          color: Color(0xFFFFB100),
        ),
      ),
    );
  }

  _buildOption(text, index) {
    return Container(
      padding: const EdgeInsets.only(
        bottom: 10
      ),
      child: GestureDetector(
        onTap: () {
          setState(() {
            var findIndex = listComment.indexWhere((x) => x['index'] == index);
            if (findIndex == -1) {
              listComment.add({"index": index, "comment": text});
            } else {
              listComment.removeAt(findIndex);
            }
          });
        },
        child: SizedBox(
          width: 250,
          child: Row(
            children: <Widget>[
              optionSelectIcon(index),
              const SizedBox(
                width: 10,
              ),
              Text(
                text,
                style: TextStyle(
                  fontFamily: 'Prompt',
                  fontSize: 15,
                  color: const Color(0xFF707070),
                  letterSpacing: 0.4,
                  shadows: <Shadow>[
                    Shadow(
                      offset: const Offset(0, 1),
                      blurRadius: 1.0,
                      color: const Color(0xFF000000).withOpacity(0.15),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  optionSelectIcon(int index) {
    var resIndex = listComment.indexWhere((x) => x['index'] == index);
    if (resIndex != -1) {
      return const Icon(
        // FontAwesomeIcons.dotCircle,
        Icons.circle,
        size: 16,
        color: Color(0xFFFFB100),
      );
    } else {
      return const Icon(
        // FontAwesomeIcons.circle,
        Icons.circle_outlined,
        size: 16,
        color: Color(0xFFFFB100),
      );
    }
  }

  saveFeedback() async {
    try {
      if (starRating.value != 0 && listComment.isNotEmpty) {
        AppLoader.loader(context);

        var comment = [];
        for (var x in listComment) {
          comment.add(x["comment"]);
        }
        setState(() {
          optionSelect = comment.join(" , ");
        });
        Map data = {
          "mobile": profileCtl.profile.value.mobile,
          "name": profileCtl.profile.value.fullname,
          "os": settingCtl.os.value,
          "version": settingCtl.currentVersion.value,
          "star": starRating.value,
          "impressive": optionSelect,
          "comment": _commentController.text,
          "merchantID": webViewLikePointController.merchantID.value
        };

        final response = await AppApi.callAPIjwt("POST", AppUrl.feedback, data);
        int status = response['status'];
        if (status == 200) {
          setState(() {
            starRating.value = 0;
            optionSelect = null;
            listComment.value = [];
            _commentController.text = '';
          });

          Get.snackbar(
              'คุณได้รับ ${AppService.numberFormatNon0(response['result']['resSaveActivity']['amountPay'])} PMSpoint',
              'จากกิจกรรม ${response['result']['resSaveActivity']['activityName']}',
              colorText: Colors.white,
              snackPosition: SnackPosition.TOP
          );
          AppService.saveActivity('ข้อเสนอแนะ', 'ส่งข้อเสนอแนะ', 'ข้อเสนอแนะ');
          AppLoader.dismiss(context);
          Get.offAll(() => const HomeNavigator());
        } else {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(
              context, 'ไม่สำเร็จ', 'ส่งข้อเสนอแนะเรียบร้อยไม่สำเร็จ', 'ตกลง');
        }
      } else {
        AppAlert.showNewAccept(context, 'ข้อเสนอแนะ', 'กรอกข้อมูลไม่ครบ', 'ตกลง');
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : saveFeedback in Feedback');
    }
  }
}

