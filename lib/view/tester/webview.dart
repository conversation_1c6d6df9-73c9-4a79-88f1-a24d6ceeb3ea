import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:permission_handler/permission_handler.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({Key? key}) : super(key: key);

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  final GlobalKey webViewKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    requestCameraPermission();
    pullToRefreshController = PullToRefreshController(
      options: PullToRefreshOptions(
        color: Colors.blue,
      ),
      onRefresh: () async {
        if (Platform.isAndroid) {
          webViewController?.reload();
        } else if (Platform.isIOS) {
          webViewController?.loadUrl(
              urlRequest: URLRequest(url: await webViewController?.getUrl()));
        }
      },
    );
  }

  requestCameraPermission() async {
    var status = await Permission.camera.request();

    if (status.isGranted) {
      // Have camera permission
      print("Have camera permission");
    } else {
      // Do not have permission
      print("No camera permission");
    }
  }

  InAppWebViewController? webViewController;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        useShouldOverrideUrlLoading: true,
        clearCache: true,
        mediaPlaybackRequiresUserGesture: true,
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
      ),

      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));

  late PullToRefreshController pullToRefreshController;
  String url = "";
  double progress = 0;
  final urlController = TextEditingController();

  final webViewCtl = Get.put(WebViewLikePointController());

  @override
  void dispose() {
    super.dispose();
  }

  saveBase64ToFile(String base64String) async {
    Uint8List fileBytes = base64Decode(base64String);

    await ImageGallerySaver.saveImage(fileBytes);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            // Icon(Icons.file_download, color: Colors.white), // Save icon
            SizedBox(width: 8.0), // Space between icon and text
            Text('บันทึกรูปภาพแล้ว'), // Your message
          ],
        ),
        duration: Duration(milliseconds: 1500),
        width: 300.0, // Width of the SnackBar.
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: InAppWebView(
        key: webViewKey,
        initialUrlRequest: URLRequest(
            url: Uri.parse("${webViewCtl.urlMiniLikePoint}/?phone=${webViewCtl.phoneEncode.value}&merchant=${webViewCtl.merchantID.value}&firstName=${webViewCtl.firstName.value}&lastName=${webViewCtl.lastName.value}")),
        initialOptions: options,
        pullToRefreshController: pullToRefreshController,
        onWebViewCreated: (controller) {
          webViewController = controller;
        },
        onConsoleMessage: (controller, consoleMessage) async {
          print(consoleMessage.message);
          if(consoleMessage.message == "BACK_TO_MAIN") {
            await webViewCtl.getPocketBalance();
            print(consoleMessage.message);
            Get.back();
          }else if (consoleMessage.message.startsWith("#saveImage#")) {
            String base64Image = consoleMessage.message.replaceAll("#saveImage#", "");
            await saveBase64ToFile(base64Image);
          }
        },
        onLoadStart: (controller, url) {
          // print("${webViewCtl.urlMiniLikePoint}/?phone=${webViewCtl.phoneEncode.value}&merchant=${webViewCtl.merchantID.value}&firstName=${webViewCtl.firstName.value}&lastName=${webViewCtl.lastName.value}");
          setState(() {
            this.url = url.toString();
            urlController.text = this.url;
          });
        },
        androidOnPermissionRequest: (controller, origin, resources) async {
          return PermissionRequestResponse(
              resources: resources,
              action: PermissionRequestResponseAction.GRANT);
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          var uri = navigationAction.request.url!;

          if (![
            "http",
            "https",
            "file",
            "chrome",
            "data",
            "javascript",
            "about"
          ].contains(uri.scheme)) {
            if (await canLaunch(url)) {
              // Launch the App
              await launch(
                url,
              );
              // and cancel the request
              return NavigationActionPolicy.CANCEL;
            }
          }

          return NavigationActionPolicy.ALLOW;
        },
        onLoadStop: (controller, url) async {
          pullToRefreshController.endRefreshing();
          setState(() {
            this.url = url.toString();
            urlController.text = this.url;
          });
        },
        onLoadError: (controller, url, code, message) {
          pullToRefreshController.endRefreshing();
        },
        onProgressChanged: (controller, progress) {
          if (progress == 100) {
            pullToRefreshController.endRefreshing();
          }
          setState(() {
            this.progress = progress / 100;
            urlController.text = this.url;
          });
        },

        onUpdateVisitedHistory: (controller, url, androidIsReload) {
          setState(() {
            this.url = url.toString();
            urlController.text = this.url;
          });
        },
      ),
    );
  }
}
