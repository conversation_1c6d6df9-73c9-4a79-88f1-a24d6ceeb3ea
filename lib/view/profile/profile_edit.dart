import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/connect.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/crop_image_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/connect_social_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/save_activity.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pick_location.dart';
import 'package:mapp_prachakij_v3/view/profile/crop_image.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:image_picker/image_picker.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({Key? key}) : super(key: key);

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  final SecureStorage secureStorage = SecureStorage();

  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _idCardController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();
  final TextEditingController _mooController = TextEditingController();
  final TextEditingController _roadController = TextEditingController();
  final TextEditingController _zipcodeController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  String _birthDay = '';
  String _dateChangeBirthDay = '';

  List<String> listProvinces = [];
  List<String> listAmphurs = [];
  List<String> listTumbols = [];
  String? tumbol;
  String? amphur;
  String? province;

  final profileCtl = Get.put(ProfileController());
  final centerCtl = Get.put(SettingController());
  final cropImageCtl = Get.put(CropImageController());
  final connectSocialCtl = Get.put(ConnectSocialController());

  bool locationStatus = false;
  String? locationName = "";
  String? locationLatLng;

  bool showSwitchStatus = false;
  bool lineStatus = false;
  RxBool tgStatus = false.obs;

  bool isLoading = true;

  final saveActivityCtl = Get.put(SaveActivityController());
  // File? _sample;
  // File? _file;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  getData() async {
    analytics.setCurrentScreen(screenName: "ProfileEdit");
    await setController();
    await getListAddress();
    setState(() {
      isLoading = false;
    });
  }


  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return GetBuilder<ProfileController>(
        init: ProfileController(),
        builder: (controller){
      return Scaffold(
          body: Obx(() => GestureDetector(
            onTap: (){
              AppService.closeKeyboard(context);
            },
            child: Stack(
              children: [
                Container(
                  width: Get.width,
                  height: Get.height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFF3F3F3),
                        Color(0xFFFFFFFF),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: Get.width,
                  height: 170,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFFFFB100).withOpacity(1),
                        const Color(0xFFFFC700).withOpacity(0.7),
                        const Color(0xFFFFC700).withOpacity(0.4),
                        const Color(0xFFFFC700).withOpacity(0),
                        // Color(0xFFFFB100),
                      ],
                    ),
                  ),
                  child: Container(
                    margin: EdgeInsets.only(
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: (){
                            Get.back();
                          },
                          child: Container(
                            width: 30,
                            height: 30,
                            alignment: Alignment.centerLeft,
                            child: const Icon(Icons.arrow_back_ios_new,
                              color: Color(0xFF282828),
                              size: 12,
                            ),
                          ),
                        ),
                        AppWidget.boldTextS(
                          context,
                          "แก้ไขข้อมูลส่วนตัว",
                          16,
                          const Color(0xFF282828),
                          FontWeight.w400,
                        ),
                        const SizedBox(
                          width: 30,
                          height: 30,
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(
                    top: 120,
                  ),
                  width: Get.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: 55,
                        width: 55,
                        alignment: Alignment.center,
                        child: Stack(
                          children: [
                            Container(
                              height: 55,
                              width: 55,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      width: 2.0,
                                      color: const Color(0xFF282828),
                                      style: BorderStyle.solid),
                                  shape: BoxShape.circle),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(50.0),
                                child: Image(
                                  image: profileCtl.profile.value.profilePicture == null || profileCtl.profile.value.profilePicture == ""
                                      ? const AssetImage(
                                      'assets/image/home/<USER>') as ImageProvider
                                      : NetworkImage("${profileCtl.profile.value.profilePicture}"),
                                  width: 50,
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: ()async{
                                changePictureProfile();
                              },
                              child: Align(
                                alignment: Alignment.bottomRight,
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Color(0xFF282828),
                                  ),
                                  child: const Icon(
                                    Icons.photo_camera_rounded,
                                    color: Colors.white,
                                    size: 14,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                    top: 210,
                    left: Get.width < 500 ? 18 : 54,
                    right: Get.width < 500 ? 18 : 54,
                  ),
                  width: Get.width,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            AppWidget.InputnormalText(
                                context,
                                100,
                                40,
                                "ชื่อ",
                                14,
                                const Color(0xFF282828),
                                FontWeight.w400,
                                _firstNameController
                            ),
                            AppWidget.InputnormalText(
                                context,
                                100,
                                40,
                                "นามสกุล",
                                14,
                                const Color(0xFF282828),
                                FontWeight.w400,
                                _lastNameController
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppWidget.InputnormalText(
                          context,
                          200,
                          40,
                          "เลขที่บัตรประชาชน",
                          14,
                          const Color(0xFF282828),
                          FontWeight.w400,
                          _idCardController,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        AppWidget.normalText(
                          context,
                          "วันเกิด",
                          14,
                          const Color(0xFF282828),
                          FontWeight.w400,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            selectDay(context),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            AppWidget.InputnormalText(
                              context,
                              90,
                              40,
                              "บ้านเลขที่",
                              14,
                              const Color(0xFF282828),
                              FontWeight.w400,
                              _numberController,
                            ),
                            const SizedBox(
                              width: 34,
                            ),
                            AppWidget.InputnormalText(
                              context,
                              90,
                              40,
                              "หมู่",
                              14,
                              const Color(0xFF282828),
                              FontWeight.w400,
                              _mooController,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            AppWidget.InputnormalText(
                              context,
                              90,
                              40,
                              "ถนน",
                              14,
                              const Color(0xFF282828),
                              FontWeight.w400,
                              _roadController,
                            ),
                          ],
                        ),
                        const SizedBox(
                            height: 10
                        ),
                        Row(
                          children: [
                            provinceSelect(context, listProvinces),
                            const SizedBox(
                              width: 34,
                            ),
                            amphurSelect(context, listAmphurs),
                          ],
                        ),
                        const SizedBox(
                          height: 10
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            tumbolSelect(context, listTumbols),
                            const SizedBox(
                              width: 34,
                            ),
                            AppWidget.InputnormalText(
                              context,
                              90,
                              40,
                              "รหัสไปรษณีย์",
                              14,
                              const Color(0xFF282828),
                              FontWeight.w400,
                              _zipcodeController,
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Row(
                          children: [
                            AppWidget.normalText(
                              context,
                              "ตำแหน่งที่ตั้ง",
                              14,
                              const Color(0xFF282828),
                              FontWeight.w400,
                            ),
                            const SizedBox(
                              width: 6,
                            ),
                            AppWidget.normalText(
                              context,
                              "บริการซ่อมถึงบ้าน",
                              13,
                              const Color(0xFF707070),
                              FontWeight.w400,
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        InkWell(
                          onTap: () async {
                            await Permission.locationWhenInUse.request();
                            List<String> result = await Get.to(() => const PickLocationPage());
                            if (result != null) {
                              //เลือกตำแหน่งแล้ว
                              setState(() {
                                locationName = result[0];
                                locationLatLng = result[1];
                                locationStatus = true;
                              });
                            } else {
                              //ไม่ได้เลือกตำแหน่ง
                              setState(() {
                                locationName = "";
                                locationLatLng = "";
                                locationStatus = false;
                              });
                            }
                          },
                          child: locationLatLng == ""
                              ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const Icon(Icons.my_location,
                                color: Color(0xFF282828),
                                size: 16,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              AppWidget.normalText(
                                context,
                                "เพิ่มตำแหน่งที่ตั้ง",
                                13,
                                const Color(0xFF895F00),
                                FontWeight.w400,
                              ),
                            ],
                          )
                              : Center(
                            child: Container(
                              width: Get.width,
                              height: 40,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Colors.black45,
                                    offset: Offset(1, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                                color: const Color(0xFF282828),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.my_location,
                                    color: Color(0xFFFFB100),
                                    size: 16,
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  AppWidget.normalText(
                                    context,
                                    locationLatLng ?? "",
                                    13,
                                    const Color(0xFFFFFFFF),
                                    FontWeight.w400,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 14,
                        ),
                        AppWidget.InputnormalText(
                          context,
                          200,
                          40,
                          "อีเมล์",
                          14,
                          const Color(0xFF282828),
                          FontWeight.w400,
                          _emailController,
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        AppWidget.normalText(
                          context,
                          "เชื่อมต่อบัญชีโซเชียลมิเดีย",
                          14,
                          const Color(0xFF282828),
                          FontWeight.w400,
                        ),
                        const SizedBox(
                          height: 4,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                SvgPicture.asset('assets/image/profile/black_logo_line.svg'),
                                const SizedBox(
                                  width: 10,
                                ),
                                AppWidget.normalText(
                                  context,
                                  "LINE",
                                  14,
                                  const Color(0xFF707070),
                                  FontWeight.w400,
                                ),
                              ],
                            ),
                            buildLineSwitch(),
                          ],
                        ),
                        // const SizedBox(
                        //   height: 4,
                        // ),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //   children: [
                        //     Row(
                        //       children: [
                        //         // SvgPicture.asset('assets/image/profile/black_logo_line.svg'),
                        //         const Icon(Icons.telegram, color: Colors.black, size: 22,),
                        //         const SizedBox(
                        //           width: 8,
                        //         ),
                        //         AppWidget.normalText(
                        //           context,
                        //           "TELEGRAM",
                        //           14,
                        //           const Color(0xFF707070),
                        //           FontWeight.w400,
                        //         ),
                        //       ],
                        //     ),
                        //     buildTGSwitch(),
                        //   ],
                        // ),
                        const SizedBox(
                          height: 50,
                        ),
                        InkWell(
                          onTap: ()async{
                            AppLoader.loader(context);
                            var status = await controller.saveProfile(
                                profileCtl.profile.value.id.toString(),
                                _firstNameController.text,
                                _lastNameController.text,
                                _idCardController.text,
                                _birthDay,
                                _numberController.text,
                                _mooController.text,
                                _roadController.text,
                                tumbol, amphur, province,
                                _zipcodeController.text,
                                _emailController.text,
                                locationLatLng);
                            if (status == 200) {
                              AppService.saveActivity(
                                  'ข้อมูลส่วนตัว', 'แก้ไขข้อมูลส่วนตัว', 'ข้อมูลส่วนตัว');
                              await AppService.setPref('bool', 'prefProfileHome', false);
                              await AppService.setPref('bool', 'prefProfileDrawer', false);
                              await AppAlert.showNewAccept(context, 'Success', 'แก้ไขข้อมูลเรียบร้อย', 'ตกลง');
                              saveActivityCtl.saveActivity("แก้ไขข้อมูลส่วนตัว", "","");
                              AppLoader.dismiss(context);
                              Get.back();
                            } else {
                              AppLoader.dismiss(context);
                              AppAlert.showError(context, 'แก้ไขข้อมูลไม่สำเร็จ', 'ตกลง');
                            }
                          },
                          child: Container(
                            width: Get.width,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black45,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                              color: const Color(0xFF282828),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AppWidget.normalText(
                                  context,
                                  "บันทึกข้อมูล",
                                  14,
                                  const Color(0xFFFFFFFF),
                                  FontWeight.w400,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Padding(padding: EdgeInsets.only(bottom: 30))
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),)
      );
      }
    );
  }

  setController() async {
    try{
      setState(() {
        _firstNameController.text = profileCtl.profile.value.firstname!;
        _lastNameController.text = profileCtl.profile.value.lastname!;
        _idCardController.text = profileCtl.profile.value.idcard!;
        _numberController.text = profileCtl.profile.value.addressNumber!;
        _mooController.text = profileCtl.profile.value.addressMoo!;
        _roadController.text = profileCtl.profile.value.addressRoad!;
        tumbol = profileCtl.profile.value.addressTumbol!;
        amphur = profileCtl.profile.value.addressAmphur!;
        province = profileCtl.profile.value.addressProvince!;
        _zipcodeController.text = profileCtl.profile.value.addressZipcode!;
        _emailController.text = profileCtl.profile.value.email!;
        locationLatLng = profileCtl.profile.value.locationHome ?? "";
        if (profileCtl.profile.value.birthday != "0000-00-00"){
          _birthDay = profileCtl.profile.value.birthday!;
        } else {
          DateTime now = DateTime.now();
          _birthDay = "${now.year.toString().padLeft(4, '0')}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";
        }
        if (profileCtl.profile.value.lineConnect == 'Y' &&
            profileCtl.profile.value.userIdLine != null) {
          setState(() {
            lineStatus = true;
          });
        }
        if (profileCtl.profile.value.userIdTg != "N" && profileCtl.profile.value.userIdTg != "" && profileCtl.profile.value.userIdTg != null) {
          tgStatus.value = true;
        }
        setState(() {
          showSwitchStatus = true;
        });
      });
    }catch(e){
      if (kDebugMode) {
        print("$e => error setController");
      }
    }
  }

  getListAddress() async {
    try {
      await getProvince();
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      AppService.sendError(e, 'ERROR : getListAddress in Profile');
    }
  }

  getProvince() async {
    try {
      Map data = {};
      final response = await AppApi.post(AppUrl.getProvinces, data);
      int status = response['status'];
      if (status == 200) {
        List<String> items = [];
        for (var i = 0; i < response['result'].length; i++) {
          var province = response['result'][i]['name_th'];
          items.add(province);
        }
        setState(() {
          listProvinces = items;
        });
      } else {
        AppService.sendError(response.toString(), 'getProvince in profile');
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getProvince in Profile');
    }
  }

  getAmphur() async {
    try {
      if (province != null && province != '') {
        Map data = {"province": province};
        final response = await AppApi.post(AppUrl.getAmphurs, data);
        int status = response['status'];
        if (status == 200) {
          List<String> items = [];
          for (var i = 0; i < response['result'].length; i++) {
            var amphur = response['result'][i]['name_th'];
            items.add(amphur);
          }
          setState(() {
            listAmphurs = items;
          });
        } else {
          AppService.sendError(response.toString(), 'getAmphur in profile');
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getAmphur in profile');
    }
  }

  getTumbol() async {
    try {
      if (amphur != null && amphur != '') {
        Map data = {"amphur": amphur};
        final response = await AppApi.post(AppUrl.getTumbol, data);
        int status = response['status'];
        if (status == 200) {
          List<String> items = [];
          for (var i = 0; i < response['result'].length; i++) {
            var tumbol = response['result'][i]['name_th'];
            items.add(tumbol);
          }
          setState(() {
            listTumbols = items;
          });
        } else {
          AppService.sendError(response.toString(), 'getTumbol in profile');
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getTumbol in profile');
    }
  }

  getZipcode() async {
    try {
      if (tumbol != null &&
          tumbol != '' &&
          amphur != null &&
          amphur != '') {
        Map data = {
          "tambol": tumbol,
          "amphur": amphur,
        };
        final response = await AppApi.post(AppUrl.getZipcode, data);
        int status = response['status'];
        if (status == 200) {
          var zipcode = response['result'][0]['zip_code'].toString();
          setState(() {
            _zipcodeController.text = zipcode;
          });
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getZipcode in profile');
    }
  }

  selectDay(context){
    return GestureDetector(
      onTap: () {
        var dateLast = DateTime(1980, 1, 1);
        var dateBirth;
        if(_birthDay != ""){
          dateBirth = DateTime.parse(_birthDay);
        } else {
          dateBirth = DateTime.now();
        }
        var newDateBirth = DateTime(dateBirth.year+543, dateBirth.month, dateBirth.day);
        var newDateLast = DateTime(dateLast.year+543, dateLast.month, dateLast.day);
        showCupertinoModalPopup(
          context: context,
          builder: (context){
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: Colors.black,
                            fontSize: 16,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          if (_dateChangeBirthDay != "") {
                            setState(() {
                              _birthDay = _dateChangeBirthDay;
                            });
                          }
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Colors.black,
                            fontSize: 16,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoTheme(
                    data: const CupertinoThemeData(
                      scaffoldBackgroundColor: Colors.amber,
                      textTheme: CupertinoTextThemeData(
                        dateTimePickerTextStyle: TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          fontFamily: 'Prompt-Medium',
                        ),
                      ),
                    ),
                    child: CupertinoDatePicker(
                      backgroundColor: Colors.white,
                      initialDateTime: _birthDay == "" ? newDateLast : newDateBirth,
                      onDateTimeChanged: (DateTime newDate) async {
                        var dtArray = newDate.toString().split(" ");
                        _birthDay = dtArray[0];
                        var bdArray = _birthDay.toString().split("-");
                        int year = int.parse(bdArray[0])-543;
                        _birthDay = "$year-${bdArray[1]}-${bdArray[2]}";
                        var dataSelect = await _birthDay;
                        setState(() {
                          _dateChangeBirthDay = dataSelect;
                        });
                      },
                      mode: CupertinoDatePickerMode.date,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
      child: Container(
        child: _birthDay == ""
            ? const Text(
          'ว่าง',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF707070),
            fontSize: 14,
          ),
        )
            : Text(
          AppService.dateThaiDate(_birthDay),
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF707070),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  provinceSelect(context, items) {
    return GestureDetector(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (province != null) {
              selectTempIndex = items.indexOf(province);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'จังหวัด',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          setState((){
                            province = items[selectTempIndex];
                            amphur = null;
                            tumbol = null;
                            _zipcodeController.text = '';
                          });
                          getAmphur();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: const TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 14,
                            color: Color(0xFF282828),
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: SizedBox(
        width: 90,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(
              context,
              "จังหวัด",
              14,
              const Color(0xFF282828),
              FontWeight.w400,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                province == null
                    ? SizedBox(
                  width: 90,
                  child: Row(
                    children: const [
                      Text(
                        'ว่าง',
                        style:  TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF707070),
                          fontSize: 14
                        ),
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                )
                    : SizedBox(
                  width: 90,
                  child: Row(
                    children: [
                      RichText(
                        text: TextSpan(
                            children: [
                              TextSpan(
                                text: province!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF707070),
                                  fontSize: 14
                                ),
                              ),
                            ]
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      const Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  amphurSelect(context, items) {
    return GestureDetector(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (amphur != null) {
              selectTempIndex = items.indexOf(amphur);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'อำเภอ',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          setState((){
                            amphur = items[selectTempIndex];
                            tumbol = null;
                            _zipcodeController.text = '';
                          });
                          getTumbol();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: const TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 14,
                            color: Color(0xFF282828),
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: SizedBox(
        width: 150,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(
              context,
              "อำเภอ",
              14,
              const Color(0xFF282828),
              FontWeight.w400,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                amphur == null
                    ? SizedBox(
                  width: 150,
                  child: Row(
                    children: const [
                      Text(
                        'ว่าง',
                        style:  TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF707070),
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                )
                    : SizedBox(
                  width: 150,
                  child: Row(
                    children: [
                      RichText(
                        text: TextSpan(
                            children: [
                              TextSpan(
                                text: amphur!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF707070),
                                  fontSize: 14,
                                ),
                              ),
                            ]
                        ),
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      const Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  tumbolSelect(context, items) {
    return GestureDetector(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (tumbol != null) {
              selectTempIndex = items.indexOf(tumbol);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตำบล',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          setState((){
                            tumbol = items[selectTempIndex];
                          });
                          getZipcode();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color: const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: const TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 14,
                            color: Color(0xFF282828),
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: SizedBox(
        width: 90,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppWidget.normalText(
              context,
              "ตำบล",
              14,
              const Color(0xFF282828),
              FontWeight.w400,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                tumbol == null
                    ? SizedBox(
                  width: 90,
                  child: Row(
                    children: const [
                      Text(
                        'ว่าง',
                        style:  TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF707070),
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                )
                    : SizedBox(
                  width: 90,
                  child: Row(
                    children: [
                      RichText(
                        text: TextSpan(
                            children: [
                              TextSpan(
                                text: tumbol!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF707070),
                                  fontSize: 14,
                                ),
                              ),
                            ]
                        ),
                      ),
                      const SizedBox(
                        width: 5
                      ),
                      const Icon(Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFF895F00),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  buildLineSwitch() {
    return Row(
      children: <Widget>[
        Transform.scale(
          scale: 0.7,
          child: showSwitchStatus
              ? FlutterSwitch(
            width: 50,
            height: 25,
            toggleSize: 20,
            borderRadius: 50,
            padding: 3,
            value: lineStatus,
            showOnOff: false,
            activeColor: const Color(0xFFFFB100),
            onToggle: (val) async {
              setState(() {
                lineStatus = val;
              });
              if (kDebugMode) {
                print("Line : $val");
              }
              if (val) {
                var lineID = await lineLogin();
                if (kDebugMode) {
                  print("lineID : $lineID");
                }
                connectSocial("Line", val, lineID);
              } else {
                connectSocial("Line", val, null);
              }
            },
          )
              : Container(),
        )
      ],
    );
  }

  static lineLogin() async {
    await LineSDK.instance.login();
    final profile = await LineSDK.instance.getProfile();
    return profile.userId;
  }

  connectSocial(socialType, socialStatus, socialID) async {
    try {
      AppLoader.loader(context);
      Map data = {};
      if (socialType == 'Facebook') {
        data = {
          "phone": profileCtl.profile.value.mobile,
          "field_connect": "facebook_connect",
          "status_connect": socialStatus,
          "field_id_connect": "userID_fb",
          "id_connect": socialID,
        };
      } else if (socialType == 'Line') {
        data = {
          "phone": profileCtl.profile.value.mobile,
          "field_connect": "line_connect",
          "status_connect": socialStatus,
          "field_id_connect": "userID_line",
          "id_connect": socialID,
        };
      }
      final response = await AppApi.post(AppUrl.connectSocial, data);
      int status = response['status'];
      AppLoader.dismiss(context);
    } catch (e) {
      AppService.sendError(e, 'ERROR : connectSocial in Profile');
      AppLoader.dismiss(context);
    }
  }

  void changePictureProfile() async {
    if (centerCtl.os.value == "android") {
      showModalBottomSheet(
          context: context,
          builder: (BuildContext bc) {
            return Wrap(
              children: [
                ListTile(
                  leading: const Icon(Icons.add_a_photo),
                  title: const Text("Take a Photo"),
                  onTap: () {
                    editProfile(ImageSource.camera);
                    Get.back();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Photo Library'),
                  onTap: () {
                    editProfile(ImageSource.gallery);
                    Get.back();
                  },
                ),
              ],
            );
          });
    } else {
      showCupertinoModalPopup(
          context: context,
          builder: (BuildContext context) {
            return CupertinoActionSheet(
              actions: <Widget>[
                CupertinoActionSheetAction(
                  onPressed: () async {
                    PermissionStatus status = await Permission.camera.request();
                    print(status);
                    editProfile(ImageSource.camera);
                    Get.back();
                  },
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 18,
                      right: 18
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: const <Widget>[
                        Text(
                          'Take a Photo',
                          style: TextStyle(
                            color: Color(0xFF282828),
                            fontFamily: "Prompt",
                            fontSize: 18,
                          ),
                        ),
                        Icon(
                          Icons.camera_alt,
                          color: Color(0xFF0581fe),
                        ),
                      ],
                    ),
                  ),
                ),
                CupertinoActionSheetAction(
                  onPressed: () {
                    editProfile(ImageSource.gallery);
                    Get.back();
                  },
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 18,
                      right: 18,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: const <Widget>[
                        Text(
                          'Photo Library',
                          style: TextStyle(
                            fontFamily: "Prompt",
                            color: Color(0xFF282828),
                            fontSize: 18,
                          ),
                        ),
                        Icon(
                          Icons.photo_library,
                          color: Color(0xFF0581fe),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              cancelButton: CupertinoActionSheetAction(
                isDefaultAction: true,
                child: const Text(
                  'Cancel',
                  style: TextStyle(
                    fontFamily: "Prompt",
                    color: Color(0xFF0581fe),
                    fontSize: 18
                  ),
                ),
                onPressed: () {
                  Get.back();
                },
              ),
            );
          });
    }
  }

  editProfile(imageSource) async {
    try {
      final ImagePicker picker = ImagePicker();
      final image = await picker.pickImage(source: imageSource);
      print(image!.path);

      if(image.path != "" || image.path != null){
        cropImageCtl.editProfile2(image.path, context);
      } else {
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "อัพโหลดรูปไม่สำเร็จ\nลองใหม่อีกครั้ง", "ตกลง");
      }

    } catch (e) {
      print('ERROR : editProfile in drawer => $e');
    }
  }
}
