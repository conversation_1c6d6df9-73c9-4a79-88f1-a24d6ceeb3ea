import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/crop_image_controller.dart';
import 'package:crop/crop.dart';

class CropImagePage extends StatefulWidget {
  const CropImagePage({Key? key}) : super(key: key);

  @override
  State<CropImagePage> createState() => _CropImagePageState();
}

class _CropImagePageState extends State<CropImagePage> {

  final cropImageCtl = Get.put(CropImageController());
  late CropController _cropController;

  @override
  void initState() {
    super.initState();
    _cropController = CropController();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        width: Get.width,
        height: Get.height,
        color: Colors.black.withOpacity(0.4),
        alignment: Alignment.center,
        child: Container(
          width: Get.width * 0.8,
          height: Get.height * 0.6,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(
                height: 20,
              ),
              AppWidget.boldTextS(context, "ตัดกรอบรูปภาพ", 18, const Color(0xFF282828), FontWeight.normal),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                width: Get.width * 0.8,
                height: Get.height * 0.45,
                child: Crop(
                    controller: _cropController,
                  padding: EdgeInsets.zero,
                  shape: BoxShape.circle,
                  dimColor: Colors.black.withOpacity(0.5),
                    child: Image.file(cropImageCtl.imageCropFile!,),
                )
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                focusColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onTap: () async {
                  final pixelRatio = MediaQuery.of(context).devicePixelRatio;
                  final cropped = await _cropController.crop(pixelRatio: pixelRatio);
                  final croppedByteData = await cropped?.toByteData(format: ImageByteFormat.png);
                  print(croppedByteData!.buffer.asUint8List()); // แปลง ByteData เป็น Uint8List
                  await cropImageCtl.saveCrop(context, croppedByteData);
                },
                child: Container(
                  width: 80,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: const Color(0xFFFFB100),
                  ),
                  alignment: Alignment.center,
                  child: AppWidget.boldText(context, "ตกลง", 14, const Color(0xFF282828), FontWeight.w400),
                ),
              )
            ],
          ),
        ),
      ),
    );

  }
}
