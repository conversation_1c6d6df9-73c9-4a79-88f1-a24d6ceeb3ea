import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/profile/edit_MR/show_detail_edit_MR.dart';

class EditBookBankMRPage extends StatefulWidget {
  const EditBookBankMRPage({Key? key}) : super(key: key);

  @override
  State<EditBookBankMRPage> createState() => _EditBookBankMRPageState();
}

class _EditBookBankMRPageState extends State<EditBookBankMRPage> {
  final TextEditingController _bookbankName = TextEditingController();
  final TextEditingController _bookbankID = TextEditingController();

  List<String> ListBank = [
    'ธนาคารกสิกรไทย',
    'ธนาคารกรุงเทพ',
    'ธนาคารกรุงไทย',
    'ธนาคารกรุงศรีอยุธยา',
    'ธนาคารไทยพาณิชย์',
    'ธนาคารทหารไทยธนาชาต',
    'ธนาคารออมสิน',
    'ธนาคารธ.ก.ส.',
    'ธนาคารซีไอเอ็มบี',
    'ธนาคารยูโอบี',
    'ธนาคารเกียรตินาคินภัทร',
    'ธนาคาซิตี้แบงก์',
    'ธนาคารอาคารสงเคราะห์',
    'ธนาคารทิสโก้',
  ];
  String? bankSelect;
  bool openBank = false;

  bool openButton = false;
  bool isLoading = true;

  final profileCtl = Get.put(ProfileController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "EditBookBankMR");
    bankSelect = profileCtl.profile.value.bankNameMR ?? "";
    _bookbankID.text = profileCtl.profile.value.bookBankNoMR ?? "";
    _bookbankName.text = profileCtl.profile.value.bookBankNameMR ?? "";
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(new FocusNode());
            },
            child: Scaffold(
              body: Stack(
                children: <Widget>[
                  Container(
                    width: width,
                    height: height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: width,
                    height: 125.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        // stops: [1.0, 1.0],
                        colors: [
                          const Color(0xFFFFB100).withOpacity(1),
                          const Color(0xFFFFC700).withOpacity(0.7),
                          const Color(0xFFFFC700).withOpacity(0.4),
                          const Color(0xFFFFC700).withOpacity(0),
                          // Color(0xFFFFB100),
                        ],
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(
                            left: 23.w,
                            top: 12.h,
                          ),
                          // color: Colors.red,
                          width: width,
                          child: Stack(
                            children: [
                              Column(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 12.h),
                                    width: 400.w,
                                    height: 80.h,
                                    child: Row(
                                      mainAxisAlignment:
                                      MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            Navigator.pop(context);
                                          },
                                          child: SizedBox(
                                            height: 30.h,
                                            child: Icon(
                                              Icons.arrow_back_ios_new,
                                              color: const Color(0xFF282828),
                                              size: 15.w,
                                            ),
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(
                                              top: 27.h, left: 16.w),
                                          width: 185.w,
                                          height: 50.h,
                                          child: RichText(
                                            text: TextSpan(children: [
                                              TextSpan(
                                                text: 'ข้อมูลบัญชีธนาคาร',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: 'Prompt-Medium',
                                                  color: const Color(0xFF282828),
                                                  fontSize: 14.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text:
                                                '\nกรุณาใส่รายละเอียดข้อมูลให้ครบถ้วน',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF664701),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                            ]),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      top: 26.h,
                                      right: 24.w,
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'ข้อมูลบัญชีธนาคาร',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Colors.black,
                                            fontSize: 12.w,
                                          ),
                                        ),
                                        Container(
                                            child: selectBank(context, ListBank)),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'เลขบัญชีธนาคาร',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              onChanged: (value) {
                                                showButton();
                                              },
                                              controller: _bookbankID,
                                              keyboardType:
                                              TextInputType.number,
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText:
                                                'กรอกหมายเลขบัญชีธนาคาร',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070)
                                                      .withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder:
                                                UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: const Color(0xFF895F00)
                                                          .withOpacity(0.5)),
                                                ),
                                                focusedBorder:
                                                const UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'ชื่อบัญชีธนาคาร',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              onChanged: (value) {
                                                showButton();
                                              },
                                              controller: _bookbankName,
                                              // keyboardType: TextInputType.number,
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText: 'กรอกชื่อบัญชีธนาคาร',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070)
                                                      .withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder:
                                                UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: const Color(0xFF895F00)
                                                          .withOpacity(0.5)),
                                                ),
                                                focusedBorder:
                                                const UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        // Container(
                                        //   child: Column(
                                        //     mainAxisAlignment:
                                        //         MainAxisAlignment.start,
                                        //     crossAxisAlignment:
                                        //         CrossAxisAlignment.start,
                                        //     children: [
                                        //       Row(
                                        //         children: [
                                        //           RichText(
                                        //             text: TextSpan(children: [
                                        //               TextSpan(
                                        //                 text: 'อัพโหลดรูปภาพ',
                                        //                 style: TextStyle(
                                        //                   fontWeight:
                                        //                       FontWeight.w400,
                                        //                   fontFamily: 'Prompt',
                                        //                   color:
                                        //                       Color(0xFF282828),
                                        //                   fontSize: 12.w,
                                        //                 ),
                                        //               ),
                                        //               TextSpan(
                                        //                 text:
                                        //                     ' สำเนาหน้าบัญชีธนาคาร',
                                        //                 style: TextStyle(
                                        //                   fontWeight:
                                        //                       FontWeight.w400,
                                        //                   fontFamily: 'Prompt',
                                        //                   color:
                                        //                       Color(0xFF664701),
                                        //                   fontSize: 12.w,
                                        //                 ),
                                        //               ),
                                        //             ]),
                                        //           ),
                                        //           SizedBox(width: 7.w),
                                        //           Padding(
                                        //             padding:
                                        //                 EdgeInsets.only(top: 2.h),
                                        //             child: Image.asset(
                                        //               'assets/images/NewMR/!_icons.png',
                                        //               width: 11.h,
                                        //               height: 11.h,
                                        //             ),
                                        //           ),
                                        //         ],
                                        //       ),
                                        //       Container(
                                        //         margin: new EdgeInsets.only(
                                        //           top: 15.h,
                                        //         ),
                                        //         child: fileBookbank == ""
                                        //             ? GestureDetector(
                                        //                 onTap: () {
                                        //                   uploadFile(context, 1);
                                        //                 },
                                        //                 child: Container(
                                        //                   width: 138.w,
                                        //                   height: 32.h,
                                        //                   decoration:
                                        //                       BoxDecoration(
                                        //                     borderRadius:
                                        //                         new BorderRadius
                                        //                             .all(
                                        //                       Radius.circular(
                                        //                         5.h,
                                        //                       ),
                                        //                     ),
                                        //                     border: Border.all(
                                        //                         color: Color(
                                        //                                 0xff895F00)
                                        //                             .withOpacity(
                                        //                                 0.6)),
                                        //                   ),
                                        //                   child: Center(
                                        //                     child: Text(
                                        //                       "อัพโหลด",
                                        //                       style: TextStyle(
                                        //                         fontWeight: FontWeight.w400,
                                        //                         fontFamily: 'Prompt-Medium',
                                        //                         fontSize: 13.w,
                                        //                         letterSpacing: 0.4,
                                        //                         color: Color(0xFF895F00),
                                        //                       ),
                                        //                     ),
                                        //                   ),
                                        //                 ),
                                        //               )
                                        //             : Row(
                                        //                 // mainAxisAlignment: MainAxisAlignment.center,
                                        //                 children: [
                                        //                   GestureDetector(
                                        //                     child: Container(
                                        //                       width: 138.w,
                                        //                       height: 32.h,
                                        //                       decoration:
                                        //                           BoxDecoration(
                                        //                         borderRadius:
                                        //                             new BorderRadius
                                        //                                 .all(
                                        //                           Radius.circular(
                                        //                             5.h,
                                        //                           ),
                                        //                         ),
                                        //                         color: Color(
                                        //                             0xFFFFB100),
                                        //                       ),
                                        //                       child: Center(
                                        //                           child: Row(
                                        //                         mainAxisAlignment:
                                        //                             MainAxisAlignment
                                        //                                 .center,
                                        //                         children: [
                                        //                           Container(
                                        //                             width: 77.w,
                                        //                             height: 20.h,
                                        //                             child: Text(
                                        //                               "$fileBookbank",
                                        //                               overflow:
                                        //                                   TextOverflow
                                        //                                       .ellipsis,
                                        //                               style: TextStyle(
                                        //                                 fontWeight: FontWeight.w500,
                                        //                                 fontFamily:'Prompt',
                                        //                                 fontSize: 13.w,
                                        //                                 letterSpacing: 0.4,
                                        //                                 color: Color(0xFF664701),
                                        //                               ),
                                        //                             ),
                                        //                           ),
                                        //                           // SizedBox(
                                        //                           //   width: 10.w,
                                        //                           // ),
                                        //                           SvgPicture.asset(
                                        //                               'assets/images/NewMR/check_ring_round_duotone.svg')
                                        //                         ],
                                        //                       )),
                                        //                     ),
                                        //                   ),
                                        //                   SizedBox(
                                        //                     width: 10.w,
                                        //                   ),
                                        //                   GestureDetector(
                                        //                     onTap: () {
                                        //                       AppAlert
                                        //                           .showImageBookBank(
                                        //                               context,
                                        //                               fileBookbank);
                                        //                       print(fileBookbank);
                                        //                     },
                                        //                     child: Container(
                                        //                       // margin: new EdgeInsets.only(
                                        //                       //   right: 20.w,
                                        //                       //   top: 5.h,
                                        //                       // ),
                                        //                       child: Image.asset(
                                        //                         'assets/images/NewMR/Img_box_fill.png',
                                        //                         width: 30.w,
                                        //                       ),
                                        //                     ),
                                        //                   ),
                                        //                   SizedBox(
                                        //                     width: 7.w,
                                        //                   ),
                                        //                   GestureDetector(
                                        //                     onTap: () {
                                        //                       uploadFile(
                                        //                           context, 1);
                                        //                     },
                                        //                     child: Container(
                                        //                       child: Text(
                                        //                         "แก้ไข",
                                        //                         style: TextStyle(
                                        //                           fontSize: 12.w,
                                        //                           color: Color(
                                        //                               0xFF895F00),
                                        //                           decoration:
                                        //                               TextDecoration
                                        //                                   .underline,
                                        //                         ),
                                        //                       ),
                                        //                     ),
                                        //                   )
                                        //                 ],
                                        //               ),
                                        //       ),
                                        //     ],
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              Positioned(
                                top: 30.h,
                                right: -10.w,
                                child: Image.asset(
                                  'assets/image/MR/LadyWhite.png',
                                  width: 137.w,
                                  height: 127.h,
                                  opacity: const AlwaysStoppedAnimation(.6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      openButton != false
                          ? Padding(
                        padding: EdgeInsets.only(bottom: 30.h),
                        child: InkWell(
                          onTap: () {
                            saveData();
                          },
                          child: Container(
                              margin: EdgeInsets.only(top: 30.h),
                              width: 316.w,
                              height: 50.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.h),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Colors.black45,
                                    offset: Offset(1, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                                color: const Color(0xFF282828),
                              ),
                              child: Center(
                                child: Text(
                                  'ดำเนินการต่อ',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Colors.white,
                                    fontSize: 14.h,
                                  ),
                                ),
                              )),
                        ),
                      )
                          : Container(),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  //เลือกธนาคาร
  selectBank(context, items) {
    return GestureDetector(
      onTap: () {
        setState(() {
          openBank = true;
        });
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (bankSelect != null) {
              selectTempIndex = items.indexOf(bankSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          setState(() {
                            openBank = false;
                          });
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                            // shadows: <Shadow>[
                            //   Shadow(
                            //     offset: Offset(0, 1),
                            //     blurRadius: 1.0,
                            //     color: Color(0xFF000000).withOpacity(0.15),
                            //   ),
                            // ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกธนาคาร',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                            // shadows: <Shadow>[
                            //   Shadow(
                            //     offset: Offset(0, 1),
                            //     blurRadius: 1.0,
                            //     color: Color(0xFF000000).withOpacity(0.15),
                            //   ),
                            // ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          setState(() {
                            openBank = false;
                            bankSelect = items[selectTempIndex];
                            showButton();
                          });
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                            // shadows: <Shadow>[
                            //   Shadow(
                            //     offset: Offset(0, 1),
                            //     blurRadius: 1.0,
                            //     color: Color(0xFF000000).withOpacity(0.15),
                            //   ),
                            // ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 150,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            fontSize: 13.w,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.4,
                            // shadows: <Shadow>[
                            //   Shadow(
                            //     offset: Offset(0, 1),
                            //     blurRadius: 1.0,
                            //     color: Color(0xFF000000).withOpacity(0.15),
                            //   ),
                            // ],
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: Container(
        width: 375.w,
        padding: EdgeInsets.symmetric(
          vertical: 10.h,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            bankSelect == null
                ? SizedBox(
              width: 310.w,
              // height: 50.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'เลือกธนาคาร',
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 12.w,
                    ),
                  ),
                  openBank == false
                      ? Icon(
                    Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(
                    Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            )
                : SizedBox(
              width: 310.w,
              // height: 50.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    bankSelect!,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF664701),
                      fontSize: 12.w,
                    ),
                  ),
                  openBank == false
                      ? Icon(
                    Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(
                    Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            ),
            // Image.asset(
            //   'assets/images/home/<USER>',
            //   width: mediaQuery2(context, 'w', 20),
            //   height: mediaQuery2(context, 'w', 20),
          ],
        ),
      ),
    );
  }

//เลือกธนาคาร

//โชว์ปุ่ม
  void showButton() {
    if (bankSelect != "" &&
        _bookbankID.text.length == 10 &&
        _bookbankName.text != "") {
      setState(() {
        openButton = true;
      });
    }
  }
//โชว์ปุ่ม

//savedata
  saveData() async {
    try{
      if(
      bankSelect != "" &&
          _bookbankID.text.length == 10 &&
          _bookbankName.text != ""
      ){
        AppLoader.loader(context);
        Map data = {
          'MrCode': profileCtl.profile.value.mrCode,
          "bankName" : bankSelect,
          "bookBankNo" : _bookbankID.text,
          "bookBankName" : _bookbankName.text,
        };
        final response = await AppApi.post(AppUrl.updateInformationBookbank, data);
        if (response['status'] == 200) {
          AppLoader.dismiss(context);
          Get.to(() => const ShowDetailEditMRPage());
        } else {
          AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
          AppLoader.dismiss(context);
        }
      } else {
        if (kDebugMode) {
          print('กรอกข้อมูลไม่ครบ');
        }
      }
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
    }
  }
//savedata
}
