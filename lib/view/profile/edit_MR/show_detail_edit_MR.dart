import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/profile/edit_MR/edit_bookbank_MR.dart';

class ShowDetailEditMRPage extends StatefulWidget {
  const ShowDetailEditMRPage({Key? key}) : super(key: key);

  @override
  State<ShowDetailEditMRPage> createState() => _ShowDetailEditMRPageState();
}

class _ShowDetailEditMRPageState extends State<ShowDetailEditMRPage> {
  final SecureStorage secureStorage = SecureStorage();
  bool isLoading = true;

  String? StatusAcc;
  String? StatusBb;

  String? bankName;
  String? bookBankNo;
  String? bookBankName;
  String? fullName;
  String? idCard;
  String? phone;
  String? career;


  bool selectRegisBb = false;

  final profileCtl = Get.put(ProfileController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
  FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    super.initState();
    analytics.setCurrentScreen(screenName: "DetailEditMR");
    getData();
  }

  Future getData() async {
    await Future.delayed(Duration.zero);
    setState(() async {
      await setStatusAcc();
      await setStatusBb();
      setState(() {
        isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Scaffold(
            body: Stack(
              children: <Widget>[
                Container(
                  width: _width,
                  height: _height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFE8E6E2),
                        Color(0xFFD9D8D5),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  height: 125.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      // stops: [1.0, 1.0],
                      colors: [
                        const Color(0xFFFFB100).withOpacity(1),
                        const Color(0xFFFFC700).withOpacity(0.7),
                        const Color(0xFFFFC700).withOpacity(0.4),
                        const Color(0xFFFFC700).withOpacity(0),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 10.h),
                      // color: Colors.red,
                      width: _width,
                      // height: 150.h,
                      child: Stack(
                        children: [
                          Column(
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 13.w),
                                width: 255.w,
                                // height: 100.h,
                                child: Container(
                                  margin: EdgeInsets.only(
                                      top: 5.h,
                                      left: 10.w
                                  ),
                                  width: 245.w,
                                  // height: 50.h,
                                  child: Row(
                                    children: [
                                      InkWell(
                                        onTap: (){
                                          Get.back();
                                        },
                                        child: SizedBox(
                                          height: 30.h,
                                          child: Icon(Icons.arrow_back_ios_new,
                                            color: const Color(0xFF282828),
                                            size: 15.w,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 20.w,),
                                      Padding(
                                        padding: EdgeInsets.only(top: 40.h),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('ข้อมูลลงทะเบียนสมัคร',
                                                style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Prompt-Medium',
                                                color: const Color(0xFF282828),
                                                fontSize: 14.w,
                                              ),
                                            ),
                                            Text('ตัวแทนผู้แนะนำลูกค้า MR',
                                                style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Prompt-Medium',
                                                color: const Color(0xFF282828),
                                                fontSize: 14.w,
                                              ),
                                            ),
                                            Text('เลือกรายการที่ต้องการ',
                                                style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 11.w,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Positioned(
                            top: 30.h,
                            right: -10.w,
                            child: Image.asset('assets/image/MR/LadyWhite.png',
                              width: 137.w,
                              height: 127.h,
                              opacity: const AlwaysStoppedAnimation(.5),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 120.h),
                            child: Column(
                              children: [
                                InkWell(
                                  onTap: (){
                                    Navigator.of(context).push(MaterialPageRoute(
                                      builder: (context) => const EditBookBankMRPage(),
                                    ));
                                    setState(() {
                                      selectRegisBb = true;
                                    });
                                  },
                                  child: Container(
                                    width: 323.w,
                                    height: 90.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10.h),
                                      border: Border.all(
                                          width: 1,
                                          color: Colors.white
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.3),
                                          offset: const Offset(1, 1),
                                          blurRadius: 30,
                                        ),
                                      ],
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        // stops: [1.0, 1.0],
                                        colors: [
                                          const Color(0xFFFFFFFF).withOpacity(1),
                                          const Color(0xFFF3F3F3).withOpacity(0.6),
                                        ],
                                      ),
                                    ),
                                    child: Stack(
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(
                                              top: 8.h,
                                              left: 15.w
                                          ),
                                          child: Image.asset('assets/image/MR/credit_card_duotone.png',
                                            width: 30.h,
                                            height: 30.h,
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(
                                              top: 10.h,
                                              left: 50.w
                                          ),
                                          width: 200.w,
                                          height: 40.h,
                                          child: RichText(text: TextSpan(
                                            children:[
                                              TextSpan(
                                                text: 'ข้อมูลบัญชีธนาคาร',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: 'Prompt-Medium',
                                                  color: const Color(0xFF282828),
                                                  fontSize: 12.w,
                                                ),

                                              ),
                                              TextSpan(
                                                text: '\nลงทะเบียนสำหรับการรับรางวัลเงินสด',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                            ] ,
                                          ),
                                          ),
                                        ),
                                        StatusBb != "success"
                                            ? Container(
                                          margin: EdgeInsets.only(
                                              top: 50.h,
                                              left: 50.w
                                          ),
                                          width: 88.w,
                                          height: 24.h,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(13.h),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withOpacity(0.3),
                                                offset: const Offset(1, 1),
                                                blurRadius: 30,
                                              ),
                                            ],
                                            color: const Color(0xFFD9D8D5),
                                          ),
                                          child: Center(
                                            child: Text('ยังไม่ลงทะเบียน',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Prompt-Medium',
                                                color: const Color(0xFF895F00),
                                                fontSize: 10.w,
                                              ),
                                            ),
                                          ),
                                        )
                                            : Container(
                                          margin: EdgeInsets.only(
                                              top: 50.h,
                                              left: 50.w
                                          ),
                                          width: 99.w,
                                          height: 24.h,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(13.h),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withOpacity(0.3),
                                                offset: const Offset(1, 1),
                                                blurRadius: 30,
                                              ),
                                            ],
                                            color: const Color(0xFFD9D8D5),
                                          ),
                                          child: Center(
                                            child: Text('ลงทะเบียนเรียบร้อย',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Prompt-Medium',
                                                color: const Color(0xFF5AB13B),
                                                fontSize: 10.w,
                                              ),
                                            ),
                                          ),
                                        ),
                                        StatusBb != "success"
                                            ? Container()
                                            : Padding(
                                          padding: EdgeInsets.only(right: 25.w),
                                          child: Align(
                                            alignment: Alignment.centerRight,
                                            child: Image.asset('assets/image/MR/Edit_duotone.png',
                                              width: 25.w,
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: 27.h,
                                ),
                                Container(
                                  child: selectRegisBb != false
                                      ? showText()
                                      : Container(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                  ],
                ),

              ],
            ),
          );
        }
    );
  }

  setStatusAcc() {
    try{
      if(
      profileCtl.profile.value.fullNameMR != "" &&
          profileCtl.profile.value.idCardMR != "" &&
          profileCtl.profile.value.phoneNumberMR != "" &&
          profileCtl.profile.value.careerMR != ""
      ) {
        setState(() {
          StatusAcc = "success";
        });
      } else {
        StatusAcc = "unsuccess";
      }
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  setStatusBb() {
    try{
      if(
      profileCtl.profile.value.bookBankNameMR != "" &&
          profileCtl.profile.value.bookBankNoMR != "" &&
          profileCtl.profile.value.bankNameMR != ""
      ){
        setState(() {
          StatusBb = "success";
        });
      } else {
        StatusBb = "unsuccess";
      }
    }catch(e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  showText(){
    if(StatusAcc != "success"){
      return SizedBox(
        width: 323.w,
        child: Row(
          children: [
            Container(
                width: 12.w,
                height: 43.h,
                alignment: Alignment.topLeft,
                child: Image.asset('assets/image/MR/!_icons.png',width: 11.h,)),
            SizedBox(
              width: 8.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('กรุณาเลือกลงทะเบียนตามลำดับรายการ',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Prompt-Medium',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
                Text(' 1. ข้อมูลสมัครตัวแทนผู้แนะนำ',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
                Text(' 2. ข้อมูลบัญชีธนาคาร',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
              ],
            )
          ],
        ),
      );
    }
  }
}
