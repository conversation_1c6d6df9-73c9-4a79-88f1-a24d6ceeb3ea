import 'dart:io';
import 'dart:ui';

import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/view/psi_page/psi_detail.dart';

class PsiCoupon extends StatefulWidget {
  const PsiCoupon({super.key});

  @override
  State<PsiCoupon> createState() => _PsiCouponState();
}

class _PsiCouponState extends State<PsiCoupon> {
  // PsiController psiCtrl = Get.put(PsiController());
  PsiController psiCtrl = Get.find<PsiController>();
  List<bool> isExpanded = [false, false, false, false, false];
  List<bool> isUseCoupon = [false, false, false, false, false];
  // late List<Map<String, dynamic>> psiData;
  // Initialized in initState

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      body: ColorfulSafeArea(
        color: Color(0xFFF3F3F3),
        top: true,
        bottom: false,
        child: Stack(
          // alignment: Alignment.topCenter,
          children: [
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.00, -1.00),
                  end: Alignment(0, 1),
                  colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                ),
              ),
            ),
            Column(
              children: [
                _buildAppBar(),
                _buildTab(),
                Expanded(
                  child: RefreshIndicator(
                    color: Colors.orangeAccent,
                    backgroundColor: Colors.white,
                    onRefresh: () async {
                      await Future.delayed(Duration(seconds: 2));
                      psiCtrl.getCouponPSI();
                      print('Refresh');
                    },
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: List.generate(
                        psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'].length,
                            (index) {
                          if (psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['isExpired'] == true) {
                            if (psiCtrl.showTab.value == false) {
                              return Column(
                                children: [
                                  const SizedBox(height: 8),
                                  _buildExpireCouponList(
                                    index,
                                    psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_total'],
                                    DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['canInDate'].toString()),
                                    DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['expireDate'].toString()),
                                  ),
                                  const SizedBox(height: 8),
                                ],
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          } else if (!psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['isExpired'] &&
                              psiCtrl.showTab.value) {
                            return Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Container(
                                    width: MediaQuery.of(context).size.width,
                                    child: psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['alreadyInUse'] == false
                                        ? _buildListCoupon(
                                      index,
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_total'],
                                      DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['canInDate'].toString()),
                                      DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['expireDate'].toString()),
                                      "\n・${psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_detail'][0]['title'].toString()}\n・${psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_detail'][1]['title'].toString()}\n・${psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_detail'][2]['title'].toString()}",
                                      "・${psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_detail'][3]['title'].toString()}",
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_sum'].toString(),
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_discount'].toString(),
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_net'].toString(),
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_vat'].toString(),
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_total'].toString(),
                                    )
                                        : _buildDisableCouponList(
                                      index,
                                      psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['psi_total'],
                                      DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['canInDate'].toString()),
                                      DateTime.parse(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCoupon'][index]['expireDate'].toString()),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                              ],
                            );
                          } else {
                            return const SizedBox.shrink();
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }

  // ฟังก์ชันสำหรับแปลงวันที่เป็นรูปแบบไทย
  String formatThaiDate(DateTime date) {
    final thaiMonths = [
      "ม.ค.", "ก.พ.", "มี.ค.", "เม.ย.", "พ.ค.", "มิ.ย.",
      "ก.ค.", "ส.ค.", "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค."
    ];
    String month = thaiMonths[date.month - 1];
    String year = ((date.year + 543) % 100).toString().padLeft(2, '0'); // เพิ่ม 543 ให้เป็นปีพุทธศักราช
    return "${date.day} $month $year";
  }

  Widget _buildTab() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            psiCtrl.changeShowTab(true);
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: psiCtrl.showTab.value
                      ? const BoxDecoration(
                    color: Color(0xFFFFB100),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.00, -1.00),
                      end: Alignment(0, 1),
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                    border: Border(right: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(18),
                    ),
                  )),
              psiCtrl.showTab.value
                  ? Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 47,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("คูปองที่ใช้ได้",
                    style: TextStyle(
                        fontSize: 14,
                        color: psiCtrl.showTab.value
                            ? const Color(0xFF282828)
                            : const Color(0xFF282828).withOpacity(0.5))),
              ),
            ],
          ),
        ),
        InkWell(
          onTap: () {
            psiCtrl.changeShowTab(false);
            setState(() {});
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: !psiCtrl.showTab.value
                      ? const BoxDecoration(
                    color: Color(0xFFFFB100),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.00, -1.00),
                      end: Alignment(0, 1),
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                    border: Border(
                      left: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),
                    ),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(18),
                    ),
                  )),
              !psiCtrl.showTab.value
                  ? Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 47,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("คูปองหมดอายุ",
                    style: TextStyle(
                        fontSize: 14,
                        color: !psiCtrl.showTab.value
                            ? const Color(0xFF282828)
                            : const Color(0xFF282828).withOpacity(0.5))),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF3F3F3),
            const Color(0xFFFFFFFF).withOpacity(0)
          ],
        ),
      ),
      // color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.2,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Navigator.pop(context);
                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //         builder: (context) => const MobileBodyPage()));
              },
              child: Container(
                width: 34,
                height: 34,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  border: Border.all(
                      width: 1,
                      color: const Color(0xFFBCBCBC80).withOpacity(0.5)),
                ),
                child: const Center(
                  child: Icon(
                    Icons.arrow_back_ios_new_rounded,
                    size: 14,
                    color: Color(0xFFFFB100),
                  ),
                ),
              ),
            ),
          ),
          Text(
            "สิทธิพิเศษ อุ่นใจเช็กฟรี",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF2B1710),
              shadows: [
                Shadow(
                  blurRadius: 2.0,
                  color: Colors.black.withOpacity(0.2),
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.2,
          ),
        ],
      ),
    );
  }

  Widget _buildListCoupon(int index, double discountInput, DateTime date, DateTime expire,String psiDetail,String psiDetail1,String psiServiceFee,String psiDiscount,String psiNet,String psiVat,String psiTotal) {
    int discount = discountInput.ceil();
    String formattedDiscount = NumberFormat("#,###").format(discount);
    return Stack(
      alignment: Alignment.center,
      children: [
        AnimatedContainer(
          width: MediaQuery.of(context).size.width * 0.9,
          height: isExpanded[index] ? 566 : 132,
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            // backgroundBlendMode: isUseCoupon[index]== true? BlendMode.color : null,
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors:
              isExpanded[index]
                  ? [
                const Color(0xFFFFFFFF),
                const Color(0xFFFFF6E3),
                const Color(0xFFFFF5E0)
              ]
                  : [Colors.white,  Colors.white],
            ),
            // color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.orange, // สีของเส้นขอบ
              width: 1.0, // ความหนาของเส้นขอบ
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, 4), // changes position of shadow
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child:
            /// ส่วนของรายละเอียดคูปอง
            !isExpanded[index]
                ? Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment:
                  MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      // color: Colors.red,
                      width: 260,
                      height: 40,
                      child: Row(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment:
                        MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'มูลค่า',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight:
                                    FontWeight.w400),
                              ),
                              Text.rich(TextSpan(children: [
                                const TextSpan(
                                    text: "฿",
                                    style: TextStyle(
                                        color:
                                        Color(0xFF895F00),
                                        fontSize: 12,
                                        fontWeight:
                                        FontWeight.w300)),
                                TextSpan(
                                    text: formattedDiscount,
                                    style: const TextStyle(
                                        color:
                                        Color(0xFF895F00),
                                        fontSize: 16,
                                        fontWeight:
                                        FontWeight.w500)),
                              ]))
                            ],
                          ),
                          Container(
                            width: 1, // ความกว้างของเส้น
                            height: 40, // ความสูงของเส้น
                            color: const Color(
                                0xFFBCBCBC), // สีของเส้น
                            // margin: const EdgeInsets.symmetric(vertical: 10), // ระยะห่างด้านบนและล่าง
                          ),
                          Column(
                            children: [
                              const Text(
                                'รับคูปองในวันที่',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight:
                                    FontWeight.w400),
                              ),
                              Text(
                                formatThaiDate(date),
                                style: const TextStyle(
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                    fontWeight:
                                    FontWeight.w400),
                              )
                            ],
                          ),
                          Container(
                            width: 1, // ความกว้างของเส้น
                            height: 40, // ความสูงของเส้น
                            color: const Color(
                                0xFFBCBCBC), // สีของเส้น
                            // margin: const EdgeInsets.symmetric(vertical: 10), // ระยะห่างด้านบนและล่าง
                          ),
                          Column(
                            children: [
                              const Text(
                                'คูปองหมดอายุ',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight:
                                    FontWeight.w400),
                              ),
                              Text(
                                formatThaiDate(expire),
                                style: const TextStyle(
                                    color: Color(0xFFEB2227),
                                    fontSize: 12,
                                    fontWeight:
                                    FontWeight.w400),
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        isExpanded[index] = !isExpanded[index];
                        setState(() {});
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: const Color(0xFFBCBCBC),
                            width: 1,
                          ),
                          borderRadius:
                          BorderRadius.circular(10),
                        ),
                        child: const Center(
                            child: Icon(
                              Icons.keyboard_arrow_down_rounded,
                              size: 24,
                              color: Color(0xFF282828),
                            )),
                      ),
                    )
                  ],
                ),
                // SizedBox(height: 8,),
                const DottedLine(
                  dashColor: Color(0xFF895F00),
                  lineThickness: 0.5,
                ),
                // SizedBox(height: 5,),
                Row(
                  children: [
                    Container(
                        height: 22,
                        width: 22,
                        child: Image.asset(
                            'assets/image/psiIcon/receipt-disscount.png')),
                    Text(
                      'ส่วนลดสำหรับตรวจเช็กระยะ ครั้งที่ ${index + 1}',
                      style: const TextStyle(
                          color: Color(0xFF282828),
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    )
                  ],
                ),
                // SizedBox(height: 1.5,),
                const Text(
                  'คูปองส่วนลดสำหรับลูกค้าประชากิจฯ สำหรับตรวจเช็กตามรายการที่กำหนด',
                  maxLines: 1,
                  overflow: TextOverflow
                      .ellipsis, // เมื่อข้อความเกิน ให้แสดง ...
                  style: TextStyle(
                      color: Color(0xFF282828),
                      fontSize: 12,
                      fontWeight: FontWeight.w400),
                ),
                const SizedBox(
                  height: 12,
                )
              ],
            )
                :Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment:
                  MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      // color: Colors.red,
                      width: 260,
                      height: 40,
                      child: Row(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment:
                        MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            child:  Column(
                              crossAxisAlignment:
                              CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'มูลค่า',
                                  style: TextStyle(
                                      color: Color(0xFF777777),
                                      fontSize: 10,
                                      fontWeight:
                                      FontWeight.w400),
                                ),
                                Text.rich(TextSpan(children: [
                                  TextSpan(
                                      text: "฿",
                                      style: TextStyle(
                                          color:
                                          Color(0xFF895F00),
                                          fontSize: 12,
                                          fontWeight:
                                          FontWeight.w300)),
                                  TextSpan(
                                      text:formattedDiscount,
                                      style: TextStyle(
                                          color:
                                          Color(0xFF895F00),
                                          fontSize: 16,
                                          fontWeight:
                                          FontWeight.w500)),
                                ]))
                              ],
                            ),
                          ),
                          Container(
                            width: 1, // ความกว้างของเส้น
                            height: 40, // ความสูงของเส้น
                            color: const Color(
                                0xFFBCBCBC), // สีของเส้น
                            // margin: const EdgeInsets.symmetric(vertical: 10), // ระยะห่างด้านบนและล่าง
                          ),
                          Column(
                            children: [
                              const Text(
                                'รับคูปองในวันที่',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight:
                                    FontWeight.w400),
                              ),
                              Text(
                                formatThaiDate(date),
                                style: const TextStyle(
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                    fontWeight:
                                    FontWeight.w400),
                              )
                            ],
                          ),
                          Container(
                            width: 1, // ความกว้างของเส้น
                            height: 40, // ความสูงของเส้น
                            color: const Color(
                                0xFFBCBCBC), // สีของเส้น
                            // margin: const EdgeInsets.symmetric(vertical: 10), // ระยะห่างด้านบนและล่าง
                          ),
                          Column(
                            children: [
                              const Text(
                                'คูปองหมดอายุ',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight:
                                    FontWeight.w400),
                              ),
                              Text(
                                formatThaiDate(expire),
                                style: const TextStyle(
                                    color: Color(0xFFEB2227),
                                    fontSize: 12,
                                    fontWeight:
                                    FontWeight.w400),
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        isExpanded[index] = !isExpanded[index];
                        setState(() {});
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFF9300)
                              .withOpacity(0.2),
                          // border: Border.all(
                          //   color: const Color(0xFFBCBCBC),
                          //   width: 1,
                          // ),
                          borderRadius:
                          BorderRadius.circular(10),
                        ),
                        child: const Center(
                            child: Icon(
                              Icons.keyboard_arrow_up_rounded,
                              size: 24,
                              color: Color(0xFF282828),
                            )),
                      ),
                    )
                  ],
                ),
                // const SizedBox(
                //   height: 5,
                // ),
                Row(
                  children: [
                    Container(
                        height: 30,
                        width: 30,
                        child: Image.asset(
                            'assets/image/psiIcon/receipt-disscount.png')),
                    Text(
                      'ส่วนลดสำหรับตรวจเช็กระยะ ครั้งที่ ${index + 1}',
                      style: const TextStyle(
                          color: Color(0xFF282828),
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    )
                  ],
                ),
                const Text(
                  'คูปองส่วนลดสำหรับลูกค้าประชากิจฯ สำหรับตรวจเช็กตามรายการที่กำหนด',
                  // maxLines: 1,
                  // overflow: TextOverflow
                  //     .ellipsis, // เมื่อข้อความเกิน ให้แสดง ...
                  style: TextStyle(
                      color: Color(0xFF282828),
                      fontSize: 12,
                      height: 2,
                      letterSpacing: 0.5,
                      fontWeight: FontWeight.w400),
                ),
                // const SizedBox(height: 8,),
                const DottedLine(
                  dashColor:  Color(0xFF895F00),
                  lineThickness: 0.5,
                ),
                 Text.rich(
                    TextSpan(
                        style: TextStyle(
                            color: Color(0xFF282828),
                            fontSize: 12,
                            height: 2,
                            letterSpacing: 0.5,
                            fontWeight: FontWeight.w400
                        ),
                        children: [
                          TextSpan(
                              text: 'รายละเอียดคูปอง อุ่นใจเช็กฟรี ที่ประชากิจฯ',
                              style: TextStyle(
                                  fontSize: 12,
                                  height: 2,
                                  letterSpacing: 0.5,
                                  fontWeight: FontWeight.w600
                              )
                          ),
                          TextSpan(
                              text: psiDetail
                              // text: '\n・ตรวจเช็กสภาพทั่วไป\n・การรับประกันงานซ่อม และอะไหล่ 6 เดือน หรือ 10,000 กม.\n・ฟรีตรวจเช็ก 8 ระบบสำคัญ (ครอบคลุม 30 รายการ)\n・ฟรีเช็กแบตเตอรี่\n・ฟรีเช็กยางรถยนต์'
                          ),
                          TextSpan(
                              text: '\nรายการแนะนำเพิ่มเติม',
                              style: TextStyle(
                                  fontSize: 12,
                                  height: 2,
                                  letterSpacing: 0.5,
                                  fontWeight: FontWeight.w600
                              )
                          ),
                        ]
                    )),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                     Text(
                       psiDetail1,
                      // '・หัวเชื่อบำรุงรักษาเครื่อง',
                      style: TextStyle(
                          color: Color(0xFF282828),
                          fontSize: 12,
                          height: 2,
                          letterSpacing: 0.5,
                          fontWeight: FontWeight.w400
                      ),
                    ),
                    InkWell(
                      onTap: (){
                        print(psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCustomer']['url_psi']);
                        Get.to(
                              () => PsiDetail(
                            dataCustomer: psiCtrl.psiList[psiCtrl.indexTicket.value]['dataCustomer']['url_psi'],
                          ),
                        );
                      },
                      child: Container(
                        child: const Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text('รายละเอียดสิทธิพิเศษ',
                              style: TextStyle(
                                  color: Color(0xFF895F00),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  decoration: TextDecoration.underline
                              ),
                            ),
                            Icon(Icons.keyboard_arrow_right_rounded)
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                // const SizedBox(height: 8,),
                const DottedLine(
                  dashColor:  Color(0xFF895F00),
                  lineThickness: 0.5,
                ),
                 Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.error_outline_rounded,color: Colors.red,),
                        SizedBox(width: 5,),
                        Text('ค่าบริการ',
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                              height: 2,
                              fontWeight: FontWeight.w600
                          ),)
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                // psiServiceFee,
                                "・ค่าบริการ (ไม่รวม VAT)",
                                style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                height: 2
                            )),
                            Text( "・คูปองส่วนลด",style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                height: 2
                            )),
                            Text( "・จำนวนเงินรวม หลังหักส่วนลด",style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                height: 2
                            )),
                            Text( "・ภาษีมูลค่าเพิ่ม 7%",style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                height: 2
                            )),
                            Text("ค่าใช้จ่ายทั้งหมด (รวม VAT)",style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                height: 2
                            )),
                          ],),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text.rich(
                                TextSpan(
                                    children: [
                                      TextSpan(
                                          text: '฿',
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w300,
                                              height: 2
                                          )
                                      ),
                                      TextSpan(
                                          text: psiServiceFee,
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              height: 2
                                          )
                                      ),
                                    ]
                                )
                            ),
                            Text.rich(
                                TextSpan(
                                    children: [
                                      TextSpan(
                                          text: '฿',
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w300,
                                              height: 2
                                          )
                                      ),
                                      TextSpan(
                                          text: psiDiscount,
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              height: 2
                                          )
                                      ),
                                    ]
                                )
                            ),
                            Text.rich(
                                TextSpan(
                                    children: [
                                      TextSpan(
                                          text: '฿',
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w300,
                                              height: 2
                                          )
                                      ),
                                      TextSpan(
                                          text: psiNet,
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              height: 2
                                          )
                                      ),
                                    ]
                                )
                            ),
                            Text.rich(
                                TextSpan(
                                    children: [
                                      TextSpan(
                                          text: '฿',
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w300,
                                              height: 2
                                          )
                                      ),
                                      TextSpan(
                                          text: psiVat,
                                          style: TextStyle(
                                              color: Color(0xFF777777),
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400,
                                              height: 2
                                          )
                                      ),
                                    ]
                                )
                            ),
                            Text('${psiTotal} บาท',style: TextStyle(
                                color: Color(0xFF282828),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                height: 2
                            )),
                          ],),
                      ],
                    ),
                    Text('เงื่อนไขสิทธิประโยชน์ที่ได้รับเป็นไปตามที่บริษัทกำหนด',style: TextStyle(
                      color: Color(0xFF895F00),
                      fontSize: 12,
                      height: 3,
                      fontWeight: FontWeight.w400,
                    ),)
                  ],
                ),
                const SizedBox(
                  height: 12,
                )
                // SizedBox(height: 1.5,),
                // const
                // const SizedBox(
                //   height: 12,
                // )
              ],
            ),
          ),
        ),
        // วงกลมซ้าย-ขวา
        Positioned(
          bottom: MediaQuery.of(context).size.height * 0.05,
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.95,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // วงกลมซ้าย
                Container(
                  width: MediaQuery.of(context).size.width * 0.05,
                  height: MediaQuery.of(context).size.width * 0.05,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFFEDEDED),
                    border: Border.all(
                      color: Colors.orange, // สีของเส้นขอบ
                      width: 1.0, // ความหนาของเส้นขอบ
                    ),
                  ),
                ),
                // วงกลมขวา
                Container(
                  width: MediaQuery.of(context).size.width * 0.05,
                  height: MediaQuery.of(context).size.width * 0.05,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFFEDEDED),
                    border: Border.all(
                      color: Colors.orange, // สีของเส้นขอบ
                      width: 1.0, // ความหนาของเส้นขอบ
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.05,
                  height: 200,
                  color: const Color(0xFFEDEDED),
                ),
                Container(
                  width: MediaQuery.of(context).size.width * 0.05,
                  height: 200,
                  color: const Color(0xFFEDEDED),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDisableCouponList(int index, double discountInput, DateTime date, DateTime expire, ) {
    int discount = discountInput.ceil();
    String formattedDiscount = NumberFormat("#,###").format(discount);
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: 132,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                            height: 22,
                            width: 22,
                            child: Image.asset(
                                'assets/image/psiIcon/receipt-disscount.png')),
                        Text(
                          'ส่วนลดสำหรับตรวจเช็กระยะ ครั้งที่ ${index + 1}',
                          style: const TextStyle(
                              color: Color(0xFF282828),
                              fontSize: 14,
                              fontWeight: FontWeight.w500),
                        ),

                      ],
                    ),
                    Container(
                      height: 18,
                      width: 52,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF9300).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Text(
                          'ใช้แล้ว',
                          style: TextStyle(
                              color: const Color(0xFF895F00),
                              fontSize: 12,
                              fontWeight: FontWeight.w500
                          ),),
                      ),
                    )
                  ],
                ),
                const Text(
                  'คูปองส่วนลดสำหรับลูกค้าประชากิจฯ สำหรับตรวจเช็กตามรายการที่กำหนด',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: Color(0xFF282828),
                      fontSize: 12,
                      fontWeight: FontWeight.w400),
                ),
                const DottedLine(
                  dashColor: Color(0xFF895F00),
                  lineThickness: 0.5,
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 260,
                      height: 40,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'มูลค่า',
                                style: TextStyle(
                                    color: Color(0xFF777777),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400),
                              ),
                              Text.rich(TextSpan(children: [
                                const TextSpan(
                                    text: "฿",
                                    style: TextStyle(
                                        color: Color(0xFF895F00),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w300)),
                                TextSpan(
                                    text:formattedDiscount,
                                    style: const TextStyle(
                                        color: Color(0xFF895F00),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500)),
                              ]))
                            ],
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: const Color(0xFFBCBCBC),
                          ),
                          Container(
                            child: Column(
                              children: [
                                const Text(
                                  'รับคูปองในวันที่',
                                  style: TextStyle(
                                      color: Color(0xFF777777),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400),
                                ),
                                Text(
                                  formatThaiDate(date),
                                  style: const TextStyle(
                                      color: Color(0xFF282828),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: const Color(0xFFBCBCBC),
                          ),
                          Container(
                            child: Column(
                              children: [
                                const Text(
                                  'คูปองหมดอายุ',
                                  style: TextStyle(
                                      color: Color(0xFF777777),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400),
                                ),
                                Text(
                                  formatThaiDate(expire),
                                  style: const TextStyle(
                                      color: Color(0xFFEB2227),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        isExpanded[index] = !isExpanded[index];
                        setState(() {});
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: const Color(0xFFBCBCBC),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Center(
                            child: Icon(
                              Icons.keyboard_arrow_down_rounded,
                              size: 24,
                              color: Color(0xFF282828),
                            )),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: 132,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFFBCBCBC),
              width: 1.0,
            ),
          ),
        ),
      ],
    );
  }
  Widget _buildExpireCouponList(int index, double discountInput, DateTime date, DateTime expire,){
    int discount = discountInput.ceil();
    String formattedDiscount = NumberFormat("#,###").format(discount);
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: 132,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                            height: 22,
                            width: 22,
                            child: Image.asset(
                                'assets/image/psiIcon/receipt-disscount.png')),
                        Text(
                          'ส่วนลดสำหรับตรวจเช็กระยะ ครั้งที่ ${index+1}',
                          style: const TextStyle(
                              color: Color(0xFF282828),
                              fontSize: 14,
                              fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    Container(
                      height: 18,
                      width: 52,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEB2227).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Center(
                        child: Text(
                          'หมดอายุ',
                          style: TextStyle(
                              color: const Color(0xFFEB2227),
                              fontSize: 12,
                              fontWeight: FontWeight.w500
                          ),),
                      ),
                    )
                  ],
                ),
                const Text(
                  'คูปองส่วนลดสำหรับลูกค้าประชากิจฯ สำหรับตรวจเช็กตามรายการที่กำหนด',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: Color(0xFF282828),
                      fontSize: 12,
                      fontWeight: FontWeight.w400),
                ),
                const DottedLine(
                  dashColor: Color(0xFF777777),
                  lineThickness: 0.5,
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 260,
                      height: 40,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'มูลค่า',
                                style: TextStyle(
                                    color: Color(0xFF282828),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400),
                              ),
                              Text.rich(TextSpan(children: [
                                const TextSpan(
                                    text: "฿",
                                    style: TextStyle(
                                        color: Color(0xFF282828),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w300)),
                                TextSpan(
                                    text: formattedDiscount.toString(),
                                    style: const TextStyle(
                                        color: Color(0xFF282828),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500)),
                              ]))
                            ],
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: const Color(0xFFBCBCBC),
                          ),
                          Container(
                            child: Column(
                              children: [
                                const Text(
                                  'รับคูปองในวันที่',
                                  style: TextStyle(
                                      color: Color(0xFF777777),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400),
                                ),
                                Text(
                                  formatThaiDate(date),
                                  style: const TextStyle(
                                      color: Color(0xFF282828),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: const Color(0xFFBCBCBC),
                          ),
                          Container(
                            child: Column(
                              children: [
                                const Text(
                                  'คูปองหมดอายุ',
                                  style: TextStyle(
                                      color: Color(0xFF777777),
                                      fontSize: 10,
                                      fontWeight: FontWeight.w400),
                                ),
                                Text(
                                  formatThaiDate(expire),
                                  style: const TextStyle(
                                      color: Color(0xFF282828),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // InkWell(
                    //   onTap: () {
                    //     isExpanded[index] = !isExpanded[index];
                    //     setState(() {});
                    //   },
                    //   child: Container(
                    //     width: 30,
                    //     height: 30,
                    //     decoration: BoxDecoration(
                    //       border: Border.all(
                    //         color: const Color(0xFFBCBCBC),
                    //         width: 1,
                    //       ),
                    //       borderRadius: BorderRadius.circular(10),
                    //     ),
                    //     child: const Center(
                    //         child: Icon(
                    //           Icons.keyboard_arrow_down_rounded,
                    //           size: 24,
                    //           color: Color(0xFF282828),
                    //         )),
                    //   ),
                    // )
                  ],
                ),
              ],
            ),
          ),
        ),
        Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: 132,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFFBCBCBC),
              width: 1.0,
            ),
          ),
        ),
      ],
    );
  }
}
