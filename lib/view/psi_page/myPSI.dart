import 'package:cached_network_image/cached_network_image.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/car_repair_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/insurance/insurance.dart';
import 'package:mapp_prachakij_v3/view/psi_page/psiCoupon.dart';

import '../../controller/setting_controller/lifetime_appointment_controller.dart';

class PSIpage extends StatefulWidget {
  const PSIpage({super.key});

  @override
  State<PSIpage> createState() => _PSIpageState();
}

class _PSIpageState extends State<PSIpage> {
  // final appointmentCtl = Get.put(AppointmentController());
  final psiCtl = Get.find<PsiController>();
  int index = 0;
  @override
  Widget build(BuildContext context) {
    return  Obx((){
      if(psiCtl.isLoading.value){
        return AppLoader.loaderWaitPage(context);
      }else{
        return Scaffold(
          body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 130,
                  ),
                  Padding(
                    padding:  EdgeInsets.only(left: 16,right: 16),
                    child: Container(
                      // width: MediaQuery.of(context).size.width * 0.9,
                      // height: MediaQuery.of(context).size.height * 0.5,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                const Text(
                                  "รถของฉัน",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const Spacer(),
                                psiCtl.psiList[index]['checkPSI'] == true?
                                InkWell(
                                  onTap: () {
                                    psiCtl.changeIndexTicket(index);
                                    Get.to(() => PsiCoupon());
                                  },
                                  child: Container(
                                    child:  Row(
                                      children: [
                                        Container(
                                            height: 22,
                                            width: 22,
                                            child: Image.asset(
                                                'assets/image/psiIcon/receipt-disscount.png')),
                                        SizedBox(width: 4),
                                        Text(
                                          "อุ่นใจเช็กฟรี",
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF282828)),
                                        ),
                                        SizedBox(width: 4),
                                        Icon(
                                          Icons.arrow_forward_ios_rounded,
                                          size: 14,
                                          color: Color(0xFF282828),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                                    :Container(),
                              ],
                            ),
                            AppWidget.showDatDivider(context),
                            Column(
                              children: [
                                const SizedBox(height: 16),
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Positioned(
                                      top: 0,
                                      left: 0,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.grey[200],
                                          borderRadius: BorderRadius.circular(16),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 4, horizontal: 12),
                                        child: Text(
                                          "${index + 1}/${psiCtl.carOwnerInfo.carList?.length ?? 0}",
                                          style: const TextStyle(
                                              fontSize: 14, color: Colors.black),
                                        ),
                                      ),
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        // ปุ่มย้อนกลับ
                                        InkWell(
                                          onTap: () {
                                            if (index > 0) {
                                              setState(() {
                                                index--; // ลดค่า index เพื่อแสดงภาพก่อนหน้า
                                              });
                                            }
                                          },
                                          child: Container(
                                            width: 36,
                                            height: 36,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              border: Border.all(
                                                  width: 1,
                                                  color: const Color(0xFFD9D8D5)),
                                            ),
                                            child: const Icon(
                                              Icons.arrow_back_ios_new_rounded,
                                              size: 18,
                                              color: Color(0xFF282828),
                                            ),
                                          ),
                                        ),
                                        // รูปภาพรถยนต์
                                        psiCtl.carPicLoader[index] ? Container(
                                          width: 190,
                                          height: 125,
                                          alignment: Alignment.center,
                                          child: Image.asset("assets/image/car_detail/car_mockup.png",),
                                        )
                                            : psiCtl.carPic[index] == "" ? Container(
                                            width: 190,
                                            height: 125,
                                            alignment: Alignment.center,
                                            child: Image.asset("assets/image/car_detail/car_mockup.png",))
                                            :CachedNetworkImage(
                                          imageUrl: psiCtl.carPic[index].toString(),
                                          width: 190,
                                          height: 125,
                                          placeholder: (context, url) => Container(
                                            width: 190,
                                            height: 125,
                                            alignment: Alignment.center,
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                Image.asset(
                                                  "assets/image/car_detail/car_mockup.png",
                                                  width: 320,
                                                  height: 100,
                                                  fit: BoxFit.cover,
                                                ),
                                                CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  color: Colors.white,
                                                ),
                                              ],
                                            ),
                                          ),
                                          errorWidget: (context, url, error) => Container(
                                            width: 190,
                                            height: 125,
                                            padding: const EdgeInsets.all(10),
                                            child: Image.asset(
                                              "assets/image/car_detail/car_mockup.png",
                                            ),
                                          ),
                                          fit: BoxFit.cover,
                                        ),
                                        // ปุ่มถัดไป
                                        InkWell(
                                          onTap: () {
                                            if (index <
                                                psiCtl
                                                    .carOwnerInfo
                                                    .carList!
                                                    .length -
                                                    1) {
                                              setState(() {
                                                index++; // เพิ่มค่า index เพื่อแสดงภาพถัดไป
                                              });
                                            }
                                          },
                                          child: Container(
                                            width: 36,
                                            height: 36,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(15),
                                              border: Border.all(
                                                  width: 1,
                                                  color: const Color(0xFFD9D8D5)),
                                            ),
                                            child: const Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 18,
                                              color: Color(0xFF282828),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                // const SizedBox(height: 16),
                                // // _buildDetailRow("ทะเบียน",  psiCtl
                                // //     .carOwnerInfo.carList![index].,),
                                // AppWidget.showDatDivider(context),
                                // _buildDetailRow("รุ่นรถ", "บิ๊กอัพ 4 ประตู"),
                                // AppWidget.showDatDivider(context),
                                // _buildDetailRow("เกรด", "Hi-Lander 3.0 Ddi M A/T",
                                //     valueColor: Colors.orange),
                                // AppWidget.showDatDivider(context),
                                // _buildDetailRow("เลขประจำตัวรถ", "MP092345983"),
                                const SizedBox(height: 16),
                              ],
                            ),
                            buildListViewCarData(index),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey[300]!,
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                  ),
                  // const SizedBox(
                  //   height: 16,
                  // ),
                  Padding(
                    padding: EdgeInsets.only(top: 12,left: 16,right: 16),
                    child:
                    psiCtl.carOwnerInfo.carList![index].miles!.isNotEmpty
                        ?buildListViewMiles(index):SizedBox(),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 12, left: 16, right: 16),
                    child: psiCtl.carOwnerInfo.carList != null &&
                        psiCtl.carOwnerInfo.carList!.isNotEmpty &&
                        psiCtl.carOwnerInfo.carList![index].rustproof != null &&
                        psiCtl.carOwnerInfo.carList![index].rustproof!.isNotEmpty
                        ? buildListViewRust(index)
                        : SizedBox(),
                  ),
                  Padding(padding: EdgeInsets.only(top: 12,left: 16,right: 16),child:
                  psiCtl.carOwnerInfo.carList![index].insurance!.length > 0 ?
                  buildListViewInsurance(index) : const SizedBox(),),
                  SizedBox(
                    height: 200,
                  )
                ],
              )
          ),
        );
      }
    });
  }

  Widget _buildDetailRow(String label, String value,
      {Color valueColor = Colors.black87}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  buildListViewCarData(int indexList) {
    return GetBuilder<PsiController>(
        init: PsiController(),
        builder: (psiCtl)=>
            SizedBox(
              height: 108,
              child: PageView.builder(
                  clipBehavior: Clip.none,
                  itemCount: psiCtl.carOwnerInfo.carList!.length,
                  controller: psiCtl.pageDetailController,
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int index) =>
                      Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  ทะเบียน", 12,
                                  const Color(0xFF777777), FontWeight.w300),
                              AppWidget.normalTextS(
                                  context,
                                  "${psiCtl.carOwnerInfo.carList![indexList].reg != "" ? psiCtl.carOwnerInfo.carList![indexList].reg : "ไม่ทราบข้อมูล"}  ",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  รุ่นรถ", 12,
                                  const Color(0xFF777777), FontWeight.w300),
                              AppWidget.normalTextS(
                                  context,
                                  "${psiCtl.carOwnerInfo.carList![indexList].carModelSa != "" ? psiCtl.carOwnerInfo.carList![indexList].carModelSa : "ไม่ทราบข้อมูล"}  ",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  เกรด", 12,
                                  const Color(0xFF777777), FontWeight.w300),
                              AppWidget.normalTextS(
                                  context,
                                  "${psiCtl.carOwnerInfo.carList![indexList].carModel != "" ? psiCtl.carOwnerInfo.carList![indexList].carModel : "ไม่ทราบข้อมูล"}  ",
                                  12,
                                  const Color(0xFF895F00),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  เลขประจำตัวรถ", 12,
                                  const Color(0xFF777777), FontWeight.w300),
                              AppWidget.normalTextS(
                                  context,
                                  "${psiCtl.carOwnerInfo.carList![indexList].carVIN != "" ? psiCtl.carOwnerInfo.carList![indexList].carVIN : "ไม่ทราบข้อมูล"}  ",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w300),
                            ],
                          ),
                          const SizedBox(
                            height: 4,
                          ),
                        ],
                      )),
            ));
  }

  /// เช็กระยะ
  buildListViewMiles(int indexListMiles) {
    return GetBuilder<PsiController>(
        init: PsiController(),
        builder: (psiCtl)=>SizedBox(
          height: 228,
          child: PageView.builder(
              clipBehavior: Clip.none,
              itemCount: psiCtl.carOwnerInfo.carList!.length,
              controller: psiCtl.pageDetailController2,
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext context, int index) =>
                  Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/image/psiIcon/wrench.svg',
                                width: 20,
                                height: 20,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                "เช็กระยะ",
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF282828),
                                ),
                              ),
                              const Spacer(),
                              InkWell(
                                onTap: () {
                                  psiCtl.carRegSelect.value = psiCtl.carOwnerInfo.carList![index].reg.toString();
                                  Get.to(() => const AppointmentPage());
                                  // TODO: Add your action here
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        const Color(0xFFE8E6E2),
                                        const Color(0xFFD9D8D5),
                                      ],
                                    ),
                                    // color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/image/psiIcon/Edit-box2.svg',
                                        width: 20,
                                        height: 20,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "นัดคิว",
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFf282828),
                                          fontWeight: FontWeight.w600,
                                          shadows: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.2),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.error_outline_rounded,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      " ครบกำหนด ${AppService.numberFormatNon0(int.parse(psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileage!.replaceAll(',', '')) + 10000)} กม. ครั้งถัดไป",
                                      12,
                                      const Color(0xFFFA4862),
                                      FontWeight.w400),
                                ],
                              ),
                              AppWidget.boldText(
                                  context,
                                  psiCtl
                                      .carOwnerInfo
                                      .carList![indexListMiles]
                                      .miles![index]
                                      .mileageDate !=
                                      ""
                                      ? " ${psiCtl.dateEngToThaiMiniPlus6M("${psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileageDate}")}"
                                      : "ไม่ทราบข้อมูล",
                                  12,
                                  const Color(0xFFFA4862),
                                  FontWeight.w500),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  AppWidget.normalText(context, "เช็กครั้งล่าสุด: ",
                                      12, const Color(0xFF777777), FontWeight.w300),
                                  AppWidget.normalText(
                                      context,
                                      psiCtl
                                          .carOwnerInfo
                                          .carList![indexListMiles]
                                          .miles![index]
                                          .mileageDate !=
                                          ""
                                          ? AppService.dateEngToThaiMini(
                                          "${psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileageDate}")
                                          : "ไม่ทราบข้อมูล",
                                      12,
                                      const Color(0xFF777777),
                                      FontWeight.w400),
                                ],
                              ),
                              AppWidget.normalText(
                                  context,
                                  psiCtl.carOwnerInfo
                                      .carList![indexListMiles].miles![index].mileage !=
                                      ""
                                      ? "${psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileage} กม."
                                      : "ไม่ทราบข้อมูล",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w400),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              AppWidget.normalText(context, "ระยะทาง", 12,
                                  const Color(0xFF777777), FontWeight.w300),
                              AppWidget.normalText(
                                  context,
                                  psiCtl.carOwnerInfo
                                      .carList![indexListMiles].miles![index].mileage !=
                                      ""
                                      ? "${psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileage} กม."
                                      : "ไม่ทราบข้อมูล",
                                  12,
                                  const Color(0xFF895F00),
                                  FontWeight.w400),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          AppWidget.normalText(context, "เงื่อนไขการเช็กระยะ", 12,
                              const Color(0xFF282828), FontWeight.w500),
                          AppWidget.normalText(
                              context,
                              "ทุก 6 เดือน หรือ ${AppService.numberFormatNon0(int.parse(psiCtl.carOwnerInfo.carList![indexListMiles].miles![index].mileage!.replaceAll(',', '')) + 10000)} กม. แล้วแต่อย่างใดอย่างหนึ่ง",
                              12,
                              const Color(0xFF895F00),
                              FontWeight.w300),
                        ],
                      ),
                    ),
                  )),
        ));
  }

  /// พ่นกันสนิม
  buildListViewRust(int indexListView){
    return GetBuilder<PsiController>(
        init: PsiController(),
        builder: (psiCtl)=>SizedBox(
          height: 152,
          child: PageView.builder(
              clipBehavior: Clip.none,
              itemCount: psiCtl.carOwnerInfo.carList!.length,
              controller: psiCtl.pageDetailController3,
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext context, int index) =>
                  Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child:
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.event_available_outlined,size: 20,),
                                  SizedBox(width: 10,),
                                  Text(
                                    "เช็กพ่นกันสนิม",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF282828),
                                    ),
                                  ),
                                ],
                              ),
                              InkWell(
                                onTap: () {
                                  psiCtl.carRegSelect.value = psiCtl.carOwnerInfo.carList![index].reg.toString();
                                  Get.to(() => const AppointmentPage());
                                  // TODO: Add your action here
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        const Color(0xFFE8E6E2),
                                        const Color(0xFFD9D8D5),
                                      ],
                                    ),
                                    // color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/image/psiIcon/Edit-box2.svg',
                                        width: 20,
                                        height: 20,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "นัดคิว",
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFf282828),
                                          fontWeight: FontWeight.w600,
                                          shadows: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.2),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.error_outline_rounded,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      " ครบกำหนดครั้งถัดไป",
                                      12,
                                      const Color(0xFFFA4862),
                                      FontWeight.w400),
                                ],
                              ),
                              AppWidget.boldText(context,
                                  psiCtl.carOwnerInfo.carList != null &&
                                      psiCtl.carOwnerInfo.carList!.length > indexListView &&
                                      psiCtl.carOwnerInfo.carList![indexListView].rustproof != null &&
                                      psiCtl.carOwnerInfo.carList![indexListView].rustproof!.isNotEmpty &&
                                      psiCtl.carOwnerInfo.carList![indexListView].rustproof!.length > index
                                      ? psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate != null
                                      ? " ${psiCtl.dateEngToThaiMiniPlus1Y(psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate!)}"
                                      : "ไม่มีข้อมูลวันที่"
                                      : "ไม่ทราบข้อมูล",
                                  12,const Color(0xFFFA4862),
                                  FontWeight.w500),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "รับบริการครั้งล่าสุด", 12, const Color(0xFF777777),
                                  FontWeight.w400),
                              AppWidget.boldText(
                                context,
                                (psiCtl.carOwnerInfo.carList != null &&
                                    psiCtl.carOwnerInfo.carList!.length > indexListView &&
                                    psiCtl.carOwnerInfo.carList![indexListView].rustproof != null &&
                                    psiCtl.carOwnerInfo.carList![indexListView].rustproof!.isNotEmpty &&
                                    psiCtl.carOwnerInfo.carList![indexListView].rustproof!.length > index)
                                    ? (psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate != null &&
                                    psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate != "")
                                    ? AppService.dateEngToThaiMini(
                                    "${psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate}")
                                    : "ไม่ทราบข้อมูล"
                                    : "ไม่ทราบข้อมูล",
                                12,
                                const Color(0xFF777777),
                                FontWeight.w400,
                              )
                              // AppWidget.boldText(context,
                              //     psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate != "" ?
                              //     AppService.dateEngToThaiMini("${psiCtl.carOwnerInfo.carList![indexListView].rustproof![index].rustDate}") : "ไม่ทราบข้อมูล",
                              //     12, const Color(0xFF777777),
                              //     FontWeight.w400),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
          ),
        ));
  }

  /// ประกัน / พรบ.
  buildListViewInsurance(int indexList){
    return GetBuilder<PsiController>(
        init: PsiController(),
        builder: (psiCtl) => SizedBox(
          height: 152,
          child: PageView.builder(
              clipBehavior: Clip.none,
              itemCount: psiCtl.carOwnerInfo.carList!.length,
              controller: psiCtl.pageDetailController4,
              physics: const NeverScrollableScrollPhysics(),
              scrollDirection: Axis.horizontal,
              itemBuilder: (BuildContext context, int index) =>
                  Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.verified_user_outlined,size: 20,),
                                  SizedBox(width: 10,),
                                  Text(
                                    "ประกันภัย / พรบ.",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF282828),
                                    ),
                                  ),
                                ],
                              ),
                              InkWell(
                                onTap: () {
                                  Get.to(() => const InsurancePage());
                                  // TODO: Add your action here
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                    vertical: 8.0,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        const Color(0xFFE8E6E2),
                                        const Color(0xFFD9D8D5),
                                      ],
                                    ),
                                    // color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(
                                        'assets/image/psiIcon/Edit-box2.svg',
                                        width: 20,
                                        height: 20,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        "นัดคิว",
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFf282828),
                                          fontWeight: FontWeight.w600,
                                          shadows: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.2),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          // AppWidget.boldTextS(context, "ประกันภัย / พรบ.", 14, const Color(0xFF282828),
                          //     FontWeight.w500),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                                  AppWidget.normalText(context, " ครบกำหนดครั้งถัดไป", 12, const Color(0xFFFA4862),
                                      FontWeight.w400),
                                ],
                              ),
                              AppWidget.boldText(context,
                                  psiCtl.carOwnerInfo.carList![indexList].insurance![index].insuranceDateEnd != "" ?
                                  " ${psiCtl.dateEngToThaiMini("${psiCtl.carOwnerInfo.carList![indexList].insurance![index].insuranceDateEnd}")}" : "ไม่ทราบข้อมูล",
                                  12, const Color(0xFFFA4862),
                                  FontWeight.w500),
                            ],
                          ),
                          AppWidget.showDatDivider(context),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "รับบริการครั้งล่าสุด", 12, const Color(0xFF777777),
                                  FontWeight.w400),
                              AppWidget.normalText(context,
                                  psiCtl.carOwnerInfo.carList![indexList].insurance![index].insuranceDate != "" ?
                                  AppService.dateEngToThaiMini("${psiCtl.carOwnerInfo.carList![indexList].insurance![index].insuranceDate}") : "ไม่ทราบข้อมูล",
                                  12, const Color(0xFF777777),
                                  FontWeight.w400),
                            ],
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                        ],
                      ),
                    ),
                  )
          ),
        ));
  }

}