import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';

class PsiDetail extends StatefulWidget {
  final dataCustomer;

  PsiDetail({Key? key, required this.dataCustomer}) : super(key: key);
  @override
  State<PsiDetail> createState() => _PsiDetailState();
}

class _PsiDetailState extends State<PsiDetail> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: ColorfulSafeArea(
          color: Colors.black,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFEDEDED),
                  Color(0xFFF2F2F2),
                ],
              )
            ),
            child: Padding(
              padding:  EdgeInsets.only(top:20,left: 16, right: 16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            height: 35,
                            width: 35,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: Color(0xFFBCBCBC).withOpacity(0.5)),
                            ),
                            alignment: Alignment.center,
                            child: Icon(Icons.keyboard_arrow_left_rounded,
                            color: Color(0xFFFFB100),),
                          )
                      ),
                      Text(
                        'PDF',
                        style: TextStyle(fontSize: 21, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(
                        height: 35,
                        width: 35,
                      ),

                    ],
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  /// PDF
                  Container(
                    height: Get.height * 0.8,
                    width: Get.width,
                    // color: Colors.white,
                    child: PhotoView(
                      backgroundDecoration:
                        BoxDecoration(
                          color: Colors.transparent,
                        ),
                        imageProvider: NetworkImage(widget.dataCustomer),
                        ),
                    // PDF(
                    //   onError: (error) {
                    //     print(error);
                    //   },
                    //   onPageError: (page, error) {
                    //     print('$page: ${error.toString()}');
                    //   },
                    // ).fromUrl(widget.dataCustomer,
                    //   placeholder: (double progress) =>
                    //     Center(child: Text('$progress %')),
                    //   errorWidget: (dynamic error) =>
                    //       Center(child: Text(error.toString())),
                    ),
                    // child: PDF().fromUrl(
                    //   eSignatureController.showPDF,
                    //   placeholder: (double progress) =>
                    //       Center(child: Text('$progress %')),
                    //   errorWidget: (dynamic error) =>
                    //       Center(child: Text(error.toString())),
                    // ),
                ],
              ),
            ),
          ),
        )
    );
  }
}
