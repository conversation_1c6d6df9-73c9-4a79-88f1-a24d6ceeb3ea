import 'package:flutter/material.dart';
import 'dart:js' as js;

import 'package:get/get.dart';

class QRScannerPage extends StatefulWidget {
  const QRScannerPage({Key? key}) : super(key: key);

  @override
  _QRScannerPageState createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    js.context['flutter_qr_result'] = (String qrCode) {
      print('QR Code from JS: $qrCode');
      if (qrCode.startsWith("Error:")) {
        // แสดง Error แต่ **ไม่ปิดหน้า**
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(qrCode)),
        );
        setState(() {
          _isScanning = false;
        });
      } else {
        // ถ้า QR ถูกต้อง ปิดหน้าพร้อมส่งค่า
        Navigator.of(context).pop(qrCode);
        setState(() {
          _isScanning = false;
        });
      }
    };
    startScanning();
  }


  void startScanning() {
    setState(() {
      _isScanning = true;
    });
    js.context.callMethod('startQRScanner');
  }

  @override
  void dispose() {
    if (_isScanning) {
      try {
        js.context.callMethod('stopQRScanner');
      } catch (e) {
        print('Error stopping QR scanner: $e');
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Container(
            height: 96,
            width: Get.width,
            color: Colors.transparent,
          ),
          Container(
            height: 96,
            width: Get.width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFFFC700).withOpacity(0.8),
                  Color(0xFFFFB100),
                  Color(0xFFFF9900).withOpacity(0.8)
                ],
              ),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 16, top: 38, right: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      height: 34,
                      width: 34,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white, width: 1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.arrow_back_ios_new,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                  Text(
                    'แสกน คิวอาร์โค้ด',
                    style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        height: 1,
                        fontFamily: 'Prompt',
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          )
                        ]),
                  ),
                  Container(
                    height: 34,
                    width: 34,
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
