import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

class Referralqr extends StatefulWidget {
  const Referralqr({super.key});

  @override
  State<Referralqr> createState() => _ReferralqrState();
}

class _ReferralqrState extends State<Referralqr> {
  final ScreenshotController screenshotController = ScreenshotController();
  final TextEditingController textController = TextEditingController();
  String qrData = '1234';
  final profileController = Get.put(ProfileController());
  final GlobalKey qrKey = GlobalKey(); // ใช้จับ QR Code เท่านั้น



  Future<void> saveQrToGallery() async {
    try {
      print('กำลังบันทึก Screenshot...');

      await Future.delayed(Duration(milliseconds: 500));

      RenderRepaintBoundary boundary =
      qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List imageBytes = byteData!.buffer.asUint8List();

      // ✅ กำหนดขนาดพื้นที่ที่ต้องการครอบ
      int cropWidth = 850;
      int cropHeight = 1120;

      // ✅ คำนวณตำแหน่งที่ต้องการให้รูปอยู่กลาง
      int cropX = (image.width - cropWidth) ~/ 2;
      int cropY = (image.height - cropHeight) ~/ 5;

      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // ✅ กำหนดพื้นหลังให้เป็นโปร่งใส
      final backgroundPaint = Paint()
        ..color = Colors.transparent
        ..blendMode = BlendMode.src;
      canvas.drawRect(
        Rect.fromLTWH(0, 0, cropWidth.toDouble(), cropHeight.toDouble()),
        backgroundPaint,
      );

      final paint = Paint();
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(cropX.toDouble(), cropY.toDouble(),
            cropWidth.toDouble(), cropHeight.toDouble()),
        Rect.fromLTWH(0, 0, cropWidth.toDouble(), cropHeight.toDouble()),
        paint,
      );

      ui.Image croppedImage =
      await recorder.endRecording().toImage(cropWidth, cropHeight);

      ByteData? croppedByteData =
      await croppedImage.toByteData(format: ui.ImageByteFormat.png);
      Uint8List croppedBytes = croppedByteData!.buffer.asUint8List();

      // ✅ บันทึกภาพที่ครอบแล้ว
      final result = await ImageGallerySaver.saveImage(croppedBytes);
      print('บันทึกสำเร็จ: $result');

      if (mounted) {
        Get.snackbar(
          "สำเร็จ",
          "บันทึกภาพสำเร็จ!",
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.transparent,
          colorText: Colors.black,
        );
      }
    } catch (e) {
      print('เกิดข้อผิดพลาด: $e');
    }
  }



  void copyToClipboard() {
    if (profileController.profile.value.mrCode?.isNotEmpty ?? false) {
      Clipboard.setData(
          ClipboardData(text: profileController.profile.value.mrCode!));
      Get.snackbar(
        "สำเร็จ",
        "คัดลอกสำเร็จ!",
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.transparent, // สีเขียว
        colorText: Colors.black, // สีขาว
      );
    } else {
      Get.snackbar(
        "ผิดพลาด",
        "ไม่พบข้อมูล",
        snackPosition: SnackPosition.TOP,
        backgroundColor: const Color(0xFFF44336), // สีแดง
        colorText: const Color(0xFFFFFFFF), // สีขาว
      );
    }
  }

  void shareReferralLink(String referralCode) {
    String url = "https://www.prachakij.com/register?ref=$referralCode";
    Share.share("สมัครสมาชิกกับเราและรับสิทธิพิเศษ! ลงทะเบียนที่นี่: $url");
  }


  Future<void> shareQrAsImage() async {
    try {
      print('Generating QR image for sharing...');
      await Future.delayed(Duration(milliseconds: 500));

      // ดึงภาพ QR + ข้อความ
      RenderRepaintBoundary boundary =
      qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);

      // กำหนดขนาดพื้นที่ที่ต้องการครอบ
      int cropWidth = 850;
      int cropHeight = 1100;

      // คำนวณตำแหน่งที่ต้องการให้รูปอยู่กลาง
      int cropX = (image.width - cropWidth) ~/ 2;
      int cropY = (image.height - cropHeight) ~/ 5;

      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // ✅ กำหนดพื้นหลังให้เป็นโปร่งใสแบบเด็ดขาด
      final backgroundPaint = Paint()
        ..color = Colors.transparent
        ..blendMode = BlendMode.src;
      canvas.drawRect(
        Rect.fromLTWH(0, 0, cropWidth.toDouble(), cropHeight.toDouble()),
        backgroundPaint,
      );

      final paint = Paint();
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(cropX.toDouble(), cropY.toDouble(),
            cropWidth.toDouble(), cropHeight.toDouble()),
        Rect.fromLTWH(0, 0, cropWidth.toDouble(), cropHeight.toDouble()),
        paint,
      );

      // แปลงเป็น Uint8List
      ui.Image croppedImage = await recorder.endRecording().toImage(cropWidth, cropHeight);
      ByteData? byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);
      Uint8List newImageBytes = byteData!.buffer.asUint8List();

      // บันทึกไฟล์ชั่วคราว
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/qr_share.png';
      File imageFile = File(filePath);
      await imageFile.writeAsBytes(newImageBytes);

      // ✅ แชร์รูปภาพ
      await Share.shareXFiles([XFile(imageFile.path)], text: "นี่คือ QR Code ของฉัน!");
    } catch (e) {
      print("❌ Error sharing QR: $e");
    }
  }


  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(
        init: ProfileController(),
        builder: (profileCtl) {
          return Scaffold(
            // backgroundColor: Colors.transparent,
              body: RepaintBoundary(
                  key: qrKey, // ✅ ใช้ GlobalKey กับ Stack ทั้งหมด
                  child: Stack(
                    children: [
                      /// ✅ พื้นหลังที่ต้องการ Screenshot
                      Container(
                        width: Get.width,
                        height: Get.height,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: [0.0, 0.5, 1.0],
                            colors: [
                              const Color(0xFFFFC700).withOpacity(0.8),
                              const Color(0xFFFFB100),
                              const Color(0xFFFF9900).withOpacity(0.8),
                            ],
                          ),
                        ),child: BackdropFilter( // เพิ่ม BackdropFilter
                        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30), // เพิ่มเอฟเฟกต์เบลอ
                        child: Container(
                          // color: Colors.transparent.withOpacity(0.2),
                          color: Colors.white.withOpacity(0.2), // ทำให้พื้นหลังโปร่งใส
                        ),
                      ),
                      ),
                      /// ✅ UI ทั้งหมด
                      Column(
                        children: [
                          Padding(
                            padding:
                            EdgeInsets.only(top: 47, right: 16, left: 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(width: 34, height: 34),
                                Center(
                                  child: Text(
                                    'คิวอาร์โค้ดแนะนำ',
                                    style: TextStyle(
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF2B1710),
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () => Get.back(),
                                  child: Container(
                                    width: 34,
                                    height: 34,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.white),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(Icons.close_sharp,
                                        color: Colors.white, size: 18),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 61),
                          /// ✅ QR Code และข้อความ
                          Container(
                            height: 200,
                            width: 200,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: EdgeInsets.all(8),
                            child: QrImageView(
                              data: profileCtl.profile.value.mrCode
                                  .toString()
                                  .substring(3),
                              backgroundColor: Colors.transparent,
                            ),
                          ),
                          SizedBox(height: 14),
                          Text(
                            "MR${profileCtl.profile.value.mrCode.toString().substring(3)}",
                            style: TextStyle(
                              fontFamily: 'Prompt',
                              color: Color(0xFF1A1818),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          SizedBox(height: 14),
                          Text(
                            'ส่งคิวอาร์โค้ดนี้ให้เพื่อนของคุณ',
                            style: TextStyle(
                              fontFamily: 'Prompt',
                              color: Color(0xFF1A1818),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 10),
                          Text(
                            'เพื่อสแกนเข้าสู่ระบบแนะนำเพื่อน',
                            style: TextStyle(
                              fontFamily: 'Prompt',
                              color: Color(0xFF1A1818).withOpacity(0.75),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),

                        ],
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Padding(
                          padding: EdgeInsets.only(left: 55, right: 55,bottom: 60),
                          child: Container(
                            height: 48,
                            width: Get.width,
                            // color: Colors.red,
                            child: Row(
                              mainAxisAlignment:
                              MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () {
                                    copyToClipboard();
                                  },
                                  child: Container(
                                    height: 48,
                                    width: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Image.asset(
                                        'assets/image/MR/fileCopy.png',
                                        scale: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    saveQrToGallery();
                                  },
                                  child: Container(
                                    height: 48,
                                    width: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Image.asset(
                                        'assets/image/MR/save_imgQR.png',
                                        scale: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    shareQrAsImage();
                                  },
                                  child: Container(
                                    height: 48,
                                    width: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Image.asset(
                                        'assets/image/MR/share_imgQR.png',
                                        scale: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    shareReferralLink(profileCtl
                                        .profile.value.mrCode
                                        .toString()
                                        .substring(3));
                                  },
                                  child: Container(
                                    height: 48,
                                    width: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Image.asset(
                                        'assets/image/MR/share_linkQR.png',
                                        scale: 2,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  )));
        });
  }
}