import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import '../../component/alert.dart';
import '../../component/widget.dart';
import '../../view/car_repair_status/camera_page.dart';
import '../../view/car_repair_status/qr_payment.dart';
import '../../view/home/<USER>/my_car_detail.dart';

import '../../controller/setting_controller/car_repair_controller.dart';

import 'package:http/http.dart' as http;


class HomeCarRepairStatusPage extends StatefulWidget {
  const HomeCarRepairStatusPage({Key? key}) : super(key: key);

  @override
  State<HomeCarRepairStatusPage> createState() => _HomeCarRepairStatusPageState();
}

class _HomeCarRepairStatusPageState extends State<HomeCarRepairStatusPage> {

  final carRepairCtl = Get.put(CarRepairController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => Container(
      padding: EdgeInsets.only(
        left: Get.width < 500 ? 8 : 54,
        right: Get.width < 500 ? 8 : 54,
      ),
      margin: const EdgeInsets.only(
          bottom: 10
      ),
      child: Column(
        children: [
          // TextButton(onPressed: () async {
          //   // print(carRepairCtl.carRepairStatusStr.value);
          //   // // status.value = "รอชำระเงิน";
          //   // if(carRepairCtl.carRepairStatusStr.value == "อยู่ในระหว่างการซ่อม"){
          //   //   carRepairCtl.carRepairStatusStr.value = "รอชำระเงิน";
          //   // }else if(carRepairCtl.carRepairStatusStr.value == "รอชำระเงิน"){
          //   //   carRepairCtl.carRepairStatusStr.value = "รับแจ้งซ่อมเรียบร้อย กำลังรอเข้าซ่อม";
          //   // }else if(carRepairCtl.carRepairStatusStr.value == "รับแจ้งซ่อมเรียบร้อย กำลังรอเข้าซ่อม"){
          //   //   carRepairCtl.carRepairStatusStr.value = "ชำระเงินเรียบร้อยแล้ว รอส่งมอบรถ";
          //   // }else if(carRepairCtl.carRepairStatusStr.value == "ชำระเงินเรียบร้อยแล้ว รอส่งมอบรถ"){
          //   //   carRepairCtl.carRepairStatusStr.value = "ดำเนินการซ่อมเสร็จเรียบร้อย";
          //   // }else if(carRepairCtl.carRepairStatusStr.value == "ดำเนินการซ่อมเสร็จเรียบร้อย"){
          //   //   carRepairCtl.carRepairStatusStr.value = "อยู่ในระหว่างการซ่อม";
          //   // }
          //   // await Get.put(CarRepairController().getCarRepairStatus());
          //   await carRepairCtl.getCarRepairStatus();
          //   // carRepairCtl.getCarRepairStatus();
          //   // carRepairCtl.carRepairStatusStr.value = "รับแจ้งซ่อมเรียบร้อย";
          //   // carRepairCtl.widthProcess.value = 0.1;
          //   // carRepairCtl.percent.value = "0%";
          // }, child: const Text("เปลี่ยนสเตตัส")),
          Container(
            width: Get.width,
            // height: 400,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFF8F8F8),
                ],
              ),
              border: Border.all(
                color: const Color(0xFFE8E6E2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            padding: const EdgeInsets.only(
              left: 10,
              right: 10,
              top: 10,
              bottom: 14,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    AppWidget.boldTextS(
                        context,
                        "รถของคุณ",
                        12.5,
                        const Color(0xFF282828),
                        FontWeight.w400
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: (){
                        Get.to(() => const MyCarDetail());
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppWidget.normalText(
                              context,
                              "ดูข้อมูลเพิ่มเติม ",
                              12.5,
                              const Color(0xFF282828),
                              FontWeight.w400),
                          const Icon(
                            Icons.arrow_forward_ios_rounded,
                            size: 14,
                            color: Color(0xFFFFB100),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 8,
                ),
                const Divider(
                  height: 0.5,
                  color: Color(0xFFBCBCBC),
                ),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppWidget.boldTextS(
                            context,
                            carRepairCtl.carRepairStatus.value.carType == "" ? "ไม่ทราบข้อมูล" : carRepairCtl.carRepairStatus.value.carType,
                            14,
                            const Color(0xFF282828),
                            FontWeight.w600
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Row(
                          children: [
                            AppWidget.normalTextS(
                                context,
                                "เกรด : ",
                                12.5,
                                const Color(0xFF282828),
                                FontWeight.w300
                            ),
                            AppWidget.boldTextS(
                                context,
                                carRepairCtl.carRepairStatus.value.carModel == "" ? "ไม่ทราบข้อมูล" : carRepairCtl.carRepairStatus.value.carModel,
                                12,
                                const Color(0xFF895F00),
                                FontWeight.w800
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 3,
                        ),
                        Row(
                          children: [
                            AppWidget.normalTextS(
                                context,
                                "ทะเบียน : ",
                                12.5,
                                const Color(0xFF282828),
                                FontWeight.w300
                            ),
                            AppWidget.boldTextS(
                                context,
                                carRepairCtl.carRepairStatus.value.reg,
                                12,
                                const Color(0xFF282828),
                                FontWeight.w800
                            ),
                          ],
                        )
                      ],
                    ),
                    SizedBox(
                        width: 160,
                        height: 94,
                        child: ClipRect(
                          clipBehavior: Clip.hardEdge,
                          child: Transform.translate(
                              offset: const Offset(30, 0),
                              child: Image.network(carRepairCtl.carRepairStatus.value.carImage.toString(), fit: BoxFit.fitHeight,
                                errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                                  return SizedBox(
                                    height: 100,
                                    child: Image.asset("assets/image/car_detail/car_mockup.png", fit: BoxFit.cover,),
                                  );
                                },
                                loadingBuilder: (BuildContext context, Widget child,
                                    ImageChunkEvent? loadingProgress) {
                                  if (loadingProgress == null) {
                                    return child;
                                  }
                                  return Container(
                                      width: 126,
                                      height: 76,
                                      alignment: Alignment.center,
                                      child: Stack(
                                        children: [
                                          Container(
                                            width: 126,
                                            height: 76,
                                            alignment: Alignment.center,
                                            padding: const EdgeInsets.all(10),
                                            child: Image.asset("assets/image/car_detail/car_mockup.png",),
                                          ),
                                          Align(
                                            alignment: Alignment.center,
                                            child: SizedBox(
                                              width: 15,
                                              height: 15,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color: const Color(0xFFFFFFFF),
                                                value: loadingProgress.expectedTotalBytes != null
                                                    ? loadingProgress.cumulativeBytesLoaded /
                                                    loadingProgress.expectedTotalBytes!
                                                    : null,
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                  );
                                },)),
                        ))
                  ],
                ),
                AppWidget.normalText(
                    context,
                    "สถานะเข้าใช้บริการ",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400
                ),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Image.asset('assets/image/car_detail/alert-circle.png', width: 18, color:  carRepairCtl.carRepairStatusStr.value != "ชำระเงินเรียบร้อยแล้ว รอส่งมอบรถ" ? null : const Color(0xFF00B8B1),),
                    const SizedBox(
                      width: 8,
                    ),
                    AppWidget.boldTextS(
                        context,
                        carRepairCtl.carRepairStatusStr.value,
                        16,
                        carRepairCtl.carRepairStatusStr.value != "ชำระเงินเรียบร้อยแล้ว รอส่งมอบรถ" ? const Color(0xFFEB2227) : const Color(0xFF664701),
                        FontWeight.w500
                    ),
                    const Spacer(),
                    statusBox(),
                  ],
                ),
                const SizedBox(
                  height: 8,
                ),
                const Divider(
                  height: 0.5,
                  color: Color(0xFFBCBCBC),
                ),
                const SizedBox(
                  height: 10,
                ),
                processingBar(),
                const SizedBox(
                  height: 10,
                ),
                AppWidget.normalText(
                    context,
                    "ระยะเวลาในการให้บริการ จะเสร็จสิ้นภายใน",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400
                ),

                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    Image.asset('assets/image/car_detail/Clock-4.png', width: 18,),
                    const SizedBox(
                      width: 8,
                    ),
                    AppWidget.boldTextS(
                        context,
                        carRepairCtl.carRepairStatus.value.timeRepair == "12 : 00 AM" ? "ยังไม่ทราบเวลา" : carRepairCtl.carRepairStatus.value.timeRepair,
                        14,
                        const Color(0xFF282828),
                        FontWeight.w800
                    ),
                  ],
                ),
                const SizedBox(
                  height: 8,
                ),
                const Divider(
                  height: 0.5,
                  color: Color(0xFFBCBCBC),
                ),
                const SizedBox(
                  height: 10,
                ),
                AppWidget.normalText(
                    context,
                    "รายการงานซ่อม",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    Image.asset('assets/image/car_detail/Notebook.png', width: 18,),
                    const SizedBox(
                      width: 8,
                    ),
                    AppWidget.boldTextS(
                        context,
                        carRepairCtl.carRepairStatus.value.detailVHC ?? "",
                        14,
                        const Color(0xFF282828),
                        FontWeight.w800
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ));
  }

  Widget processingBar(){
    return Obx(() => Stack(
      alignment: Alignment.bottomLeft,
      children: [
        Container(
          width: Get.width,
          height: 22,
          decoration: BoxDecoration(
              color: const Color(0xFFEBEBEB),
              borderRadius: BorderRadius.circular(16)
          ),
        ),
        AnimatedContainer(
          duration: const Duration(milliseconds: 2200),
          width: Get.width * carRepairCtl.widthProcess.value,
          height: 22,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                const Color(0xFFFFC700).withOpacity(0.7),
                const Color(0xFFFFB100).withOpacity(0.7),
                const Color(0xFFFF9900).withOpacity(0.7),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFFB100).withOpacity(0.7),
                spreadRadius: 1.5,
                blurRadius: 4,
                offset: const Offset(0, 0),
              ),
            ],
          ),
        ),
        AnimatedContainer(
          duration: const Duration(milliseconds: 2200),
          width: Get.width * carRepairCtl.widthProcess.value + 14,
          height: 29,
          alignment: Alignment.bottomRight,
          child: Image.asset('assets/image/mascot2.png', width: 31, opacity: const AlwaysStoppedAnimation(.5),),
        ),
        AnimatedContainer(
          duration: const Duration(milliseconds: 2200),
          width: Get.width * carRepairCtl.widthProcess.value,
          height: 22,
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(
              right: 8
          ),
          child: AppWidget.boldTextS(
              context,
              carRepairCtl.percent.value,
              14,
              const Color(0xFFFFFFFF),
              FontWeight.w400
          ),
        ),
        AnimatedContainer(
          duration: const Duration(milliseconds: 2200),
          width: Get.width * carRepairCtl.widthProcess.value + 20,
          height: 29,
          alignment: Alignment.bottomRight,
          child: Image.asset('assets/image/mascot2.png', width: 31),
        ),

      ],
    ));
  }

  Widget paymentBox(){
    return Obx(() => InkWell(
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: (){
        if(carRepairCtl.carRepairStatus.value.typePay == "cash"){
          return;
        }
        if(carRepairCtl.timeToPay.value.startsWith("-")){
          AppAlert.showNewAccept(context, "หมดเวลาการทำรายการ", "กรุณาติดต่อพนักงาน\nเพื่อทำการชำระเงินอีกครั้ง", "ตกลง");
          return;
        }
        carRepairCtl.countDownTimeToPay();
        AppWidget.showDialogPage(context, const QrPaymentPage());
      },
      child: Container(
        height: 38,
        padding: const EdgeInsets.only(
          left: 14,
          right: 14,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE8E6E2),
              Color(0xFFD9D8D5),
            ],
          ),
          border: Border.all(
            color: const Color(0xFFFFB100),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/image/car_detail/qr-code.png' , width: 18,),
            const SizedBox(
              width: 8,
            ),
            AppWidget.normalTextS(
                context,
                "ชำระเงิน : ",
                14,
                const Color(0xFF282828),
                FontWeight.w400
            ),
            AppWidget.boldTextS(
                context,
                carRepairCtl.carRepairStatus.value.amount.toString(),
                14,
                const Color(0xFF402F11),
                FontWeight.w800
            ),
            AppWidget.normalTextS(
                context,
                " บาท",
                14,
                const Color(0xFF282828),
                FontWeight.w400
            ),
          ],
        ),
      ),
    ));
  }

  Widget cameraBox(){
    return InkWell(
      onTap: ()async{
        AppLoader.loader(context);
        await http.get(Uri.parse(carRepairCtl.carRepairStatus.value.camera.toString())).then((value) {
          if (kDebugMode) {
            // print(value.body);
          }
        });
        await Future.delayed(const Duration(seconds: 2));
        AppLoader.dismiss(context);
        Get.to(() => const CameraPage());
      },
      child: Container(
        height: 38,
        padding: const EdgeInsets.only(
          left: 14,
          right: 14,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE8E6E2),
              Color(0xFFD9D8D5),
            ],
          ),
          border: Border.all(
            color: const Color(0xFFFFB100),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/image/car_detail/video-01.png' , width: 18,),
            const SizedBox(
              width: 8,
            ),
            AppWidget.boldTextS(
                context,
                "ดูกล้อง",
                14,
                const Color(0xFF402F11),
                FontWeight.w600
            ),
          ],
        ),
      ),
    );
  }

  statusBox(){
    if(carRepairCtl.carRepairStatusStr.value == "รอชำระเงิน"){
      return paymentBox();
    } else if(carRepairCtl.carRepairStatusStr.value == "อยู่ในระหว่างการซ่อม"){
      if(carRepairCtl.carRepairStatus.value.lift != ""){
        return cameraBox();
      }
    } else {
      return Container(
        height: 38,
        padding: const EdgeInsets.only(
          left: 14,
          right: 14,
        ),
      );
    }
  }

}

