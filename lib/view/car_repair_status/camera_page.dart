import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/car_repair_controller.dart';
import 'package:zoom_widget/zoom_widget.dart';
import 'package:flutter/foundation.dart';

class CameraPage extends StatefulWidget {
  const CameraPage({Key? key}) : super(key: key);

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {

  final carRepairCtl = Get.put(CarRepairController());

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
    if(GetPlatform.isIOS){
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
      ]);
    }
  }

  @override
  void dispose() {
    super.dispose();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return Scaffold(
          body: orientation == Orientation.landscape
              ? _buildLandscape()
              : Container(),
        );
      },
    );
  }

  Widget _buildLandscape() {
    return Center(
      child: Stack(
        children: <Widget>[
          SizedBox(
            width: Get.width,
            height: Get.height,
            child: Zoom(
              initTotalZoomOut: true,
              child: SizedBox(
                  width: Get.width,
                  height: Get.height,
                  child: InAppWebView(
                    initialUrlRequest: URLRequest(url: Uri.parse(carRepairCtl.carRepairStatus.value.camera ?? '')),
                  )
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              color: Colors.black,
              width: 50,
              height: Get.height,
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              color: Colors.black,
              width: 50,
              height: Get.height,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 44, left: 60, right: 60),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    SystemChrome.setPreferredOrientations([
                      DeviceOrientation.portraitUp,
                    ]);
                    Get.back();
                  },
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                          bottomRight: Radius.circular(15),
                          bottomLeft: Radius.circular(15),
                        ),
                        color: Colors.white,
                        border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                    child: const Icon(
                      Icons.arrow_back_ios_new,
                      size: 18,
                      color: Color(0xFFFFB100),
                    ),
                  ),
                ),
                Text(carRepairCtl.carRepairStatus.value.lift ?? '',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    shadows: [
                      Shadow(
                        blurRadius: 10.0,
                        color: Colors.black,
                        offset: Offset(5.0, 5.0),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
