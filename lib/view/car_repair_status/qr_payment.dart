import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/car_repair_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:screenshot/screenshot.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class QrPaymentPage extends StatefulWidget {
  const QrPaymentPage({Key? key}) : super(key: key);

  @override
  State<QrPaymentPage> createState() => _QrPaymentPageState();
}

class _QrPaymentPageState extends State<QrPaymentPage> {

  final carRepairCtl = Get.put(CarRepairController());

  final screenShotCtl = ScreenshotController();

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
            width: Get.width,
            height: Get.height,
            color: Colors.black.withOpacity(0.6),
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              top: 40,
              // bottom: 40,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: Get.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(25),
                      color: Colors.white,
                    ),
                    padding: const EdgeInsets.only(
                      top: 20,
                      bottom: 20,
                    ),
                    child: Column(
                      children: [
                        AppWidget.boldText(
                            context,
                            "คิวอาร์โค้ด ชำระเงิน",
                            14,
                            const Color(0xFF241F35),
                            FontWeight.w600
                        ),
                        AppWidget.normalText(
                            context,
                            "บันทีกคิวอาร์โค้ดนี้ เพื่อนำไปใช้สำหรับโอนชำระเงิน",
                            14,
                            const Color(0xFF895F00),
                            FontWeight.w400
                        ),
                        const Divider(
                          color: Color(0xFFE0DDDD),
                          thickness: 0.5,
                        ),
                        Container(
                            padding: const EdgeInsets.only(
                              left: 22,
                              right: 22,
                            ),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    AppWidget.normalText(
                                        context,
                                        "ยอดชำระเงินทั้งหมด",
                                        14,
                                        const Color(0xFF241F35),
                                        FontWeight.w400
                                    ),
                                    const Spacer(),
                                    AppWidget.boldText(
                                        context,
                                        carRepairCtl.carRepairStatus.value.amount.toString(),
                                        14,
                                        const Color(0xFF895F00),
                                        FontWeight.w600
                                    ),
                                    AppWidget.normalText(
                                        context,
                                        " บาท",
                                        14,
                                        const Color(0xFF241F35),
                                        FontWeight.w400
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                SizedBox(
                                  width: 284,
                                  height: 284,
                                  child: Image.network(carRepairCtl.payment["url"].toString(), fit: BoxFit.fill),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                AppWidget.normalText(
                                    context,
                                    "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด",
                                    14,
                                    const Color(0xFF707070),
                                    FontWeight.w400
                                ),
                              ],
                            )
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        const Divider(
                          color: Color(0xFFE0DDDD),
                          thickness: 0.5,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Container(
                            padding: const EdgeInsets.only(
                              left: 22,
                              right: 22,
                            ),
                            child: Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    AppWidget.normalText(
                                        context,
                                        "กรุณาชำระภายใน",
                                        14,
                                        const Color(0xFF282828),
                                        FontWeight.w400
                                    ),
                                    AppWidget.boldText(
                                        context,
                                        "${carRepairCtl.hours.value.toString().padLeft(2, '0')} : ${carRepairCtl.minutes.value.toString().padLeft(2, '0')} : ${carRepairCtl.seconds.value.toString().padLeft(2, '0')}",
                                        14,
                                        const Color(0xFFEB2227),
                                        FontWeight.w800
                                    ),
                                    AppWidget.normalText(
                                        context,
                                        "หมดเวลา ${carRepairCtl.datePayment.value}, ${carRepairCtl.timePayment.value}",
                                        14,
                                        const Color(0xFF707070),
                                        FontWeight.w400
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                Column(
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        var permission = await Permission.photos.request();

                                        print(permission);
                                        final imageQR = await screenShotCtl.captureFromWidget(buildSlip());
                                        if(imageQR == null){
                                          AppAlert.showNewAccept(context, "บันทึกรูปภาพไม่สำเร็จ", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
                                          return;
                                        }
                                        await carRepairCtl.saveImage(context, imageQR);
                                      },
                                      child: Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(50),
                                          gradient: const LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Color(0xFFE8E6E2),
                                              Color(0xFFD9D8D5)
                                            ],
                                          ),
                                        ),
                                        padding: const EdgeInsets.all(8),
                                        child: Image.asset('assets/image/car_detail/download-.png'),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    AppWidget.boldText(
                                        context,
                                        "บันทึก",
                                        12,
                                        const Color(0xFF241F35),
                                        FontWeight.w500
                                    ),
                                  ],
                                ),

                              ],
                            )
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        const Divider(
                          color: Color(0xFFE0DDDD),
                          thickness: 0.5,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Container(
                          padding: const EdgeInsets.only(
                            left: 22,
                            right: 22,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(
                                  context,
                                  "ขั้นตอนการชำระเงิน",
                                  12,
                                  const Color(0xFF707070),
                                  FontWeight.w600
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "1.",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 6,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "คลิกปุ่ม บันทึก หรือ แคปหน้าจอ เพื่อบันทึกคิวอาร์โค้ดนี้",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "2.",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "เปิดแอปพลิเคชันธนาคารบนอุปกรณ์ของท่าน",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "3.",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "ชำระเงินจากเมนู สแกนจ่าย ผ่าน QRcode",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "4.",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "หลังจากชำระเงินเสร็จสิ้น กรุณากลับไปตรวจสอบสถานะ",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "4.",
                                      12,
                                      Colors.transparent,
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "การชำระเงินในแอพประชากิจฯ อีกครั้ง หากสถานะยังไม่",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "4.",
                                      12,
                                      Colors.transparent,
                                      FontWeight.w200
                                  ),
                                  const SizedBox(
                                    width: 3,
                                  ),
                                  AppWidget.normalText(
                                      context,
                                      "อัพเดทกรุณาติดต่อทีมงานได้ที่ฝ่ายการเงิน",
                                      12,
                                      const Color(0xFF707070),
                                      FontWeight.w200
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  TextButton(
                      onPressed: (){Get.back();},
                      child: const Text(
                          "กลับ",
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFFFFFFFF),
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Prompt',
                            decoration: TextDecoration.underline,
                          )
                      ))
                ],
              ),
            )
        ),
      ),
    ));
    // return buildSlip();
  }

  buildSlip(){
    return Screenshot(
        controller: screenShotCtl,
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: Get.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  color: Colors.white,
                ),
                padding: const EdgeInsets.only(
                  top: 20,
                  bottom: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.only(
                        left: 26,
                        right: 26,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.boldText(
                              context,
                              "คิวอาร์โค้ด ชำระเงิน",
                              14,
                              const Color(0xFF241F35),
                              FontWeight.w600
                          ),
                          const SizedBox(
                              height: 10
                          ),
                          Row(
                            children: [
                              AppWidget.normalText(
                                  context,
                                  "ยอดชำระเงินทั้งหมด",
                                  12,
                                  const Color(0xFF241F35),
                                  FontWeight.w400
                              ),
                              const Spacer(),
                              AppWidget.boldText(
                                  context,
                                  carRepairCtl.carRepairStatus.value.amount.toString(),
                                  14,
                                  const Color(0xFF895F00),
                                  FontWeight.w600
                              ),
                              AppWidget.normalText(
                                  context,
                                  " บาท",
                                  14,
                                  const Color(0xFF241F35),
                                  FontWeight.w400
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                    const Divider(
                      color: Color(0xFFE0DDDD),
                      thickness: 0.5,
                    ),
                    Container(
                        padding: const EdgeInsets.only(
                          left: 26,
                          right: 26,
                        ),
                        alignment: Alignment.center,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(
                              height: 10,
                            ),
                            SizedBox(
                              width: 284,
                              height: 284,
                              child: Image.network(carRepairCtl.payment["url"].toString(), fit: BoxFit.fill),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            AppWidget.normalText(
                                context,
                                "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด",
                                14,
                                const Color(0xFF707070),
                                FontWeight.w400
                            ),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    const Divider(
                      color: Color(0xFFE0DDDD),
                      thickness: 0.5,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                        left: 26,
                        right: 26,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Image.asset('assets/image/home/<USER>', width: 55, height: 40),
                          Row(
                            children: [
                              GradientText(
                                'ISUZU',
                                style: const TextStyle(
                                  fontSize: 8,
                                  fontFamily: 'Prompt-Medium',
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.4,
                                ),
                                gradientDirection: GradientDirection.ttb,
                                colors: [
                                  const Color(0xFF000000).withOpacity(0.7),
                                  const Color(0xFF000000),
                                ],
                              ),
                              GradientText(
                                ' PRACHAKIJ',
                                style: const TextStyle(
                                  fontSize: 8,
                                  fontFamily: 'Prompt',
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 0.4,
                                ),
                                gradientDirection: GradientDirection.ttb,
                                colors: const [
                                  Color(0xFF664701),
                                  Color(0xFF1D1400),
                                ],
                              ),
                              const Spacer(),
                              AppWidget.normalText(
                                  context,
                                  "กรุณาชำระภายใน",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w400
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Row(
                            children: [
                              AppWidget.normalText(
                                  context,
                                  "BY : PRACHAKIJ MOBILE",
                                  12,
                                  const Color(0xFF707070),
                                  FontWeight.w400
                              ),
                              const Spacer(),
                              AppWidget.normalText(
                                  context,
                                  "วันที่ : ${carRepairCtl.datePayment.value} ${carRepairCtl.timePayment.value}",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w600
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
