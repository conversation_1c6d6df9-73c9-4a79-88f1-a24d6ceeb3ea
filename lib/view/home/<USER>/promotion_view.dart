import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';

class PromotionViewPage extends StatefulWidget {
  final id;
  final photo;
  final name;
  final body;

  PromotionViewPage(this.id, this.photo, this.name, this.body);

  @override
  State<PromotionViewPage> createState() => _PromotionViewPageState(this.id, this.photo, this.name, this.body);
}

class _PromotionViewPageState extends State<PromotionViewPage> {
  final id;
  final photo;
  final name;
  final body;

  _PromotionViewPageState(this.id, this.photo, this.name, this.body);

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "PromotionView");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [Color(0xff505050), Color(0xff282828)],
                  stops: [0.0, 1.0],
                ),
              ),
            ),
            Column(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  width: Get.width,
                  height: 90,
                  padding: const EdgeInsets.only(
                      left: 18,
                      right: 18
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: (){
                          Navigator.pop(context);
                        },
                        child:  Container(
                          width: 50,
                          height: 50,
                          alignment: Alignment.centerLeft,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: Color(0xFFFFB100),
                            size: 16,
                          ),
                        ),
                      ),
                      AppWidget.boldTextS(
                          context,
                          "รายละเอียด",
                          14,
                          Colors.white,
                          FontWeight.w400),
                      const SizedBox(
                        width: 50,
                        height: 50,
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        CachedNetworkImage(
                          imageUrl: photo,
                          width: Get.width,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const SizedBox(
                            width: 50,
                            height: 50,
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Colors.orange,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.error,
                            color: Color(0xFFFFB100),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(
                            top: 20,
                            left: 18,
                            right: 18,
                          ),
                          child: Column(
                            children: [
                              Text(
                                name,
                                style: const TextStyle(
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: 18,
                                  color: Color(0xFFFFB100),
                                ),
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              Text(
                                body,
                                style: const TextStyle(
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: 16,
                                  color: Color(0xFFFFFFFF),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 20,)
                      ],
                    ),
                  ),
                )
              ],
            ),
          ],
        )
    );
  }
}
