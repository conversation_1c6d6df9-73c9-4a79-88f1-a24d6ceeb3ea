import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NewsPopup extends StatefulWidget {
  const NewsPopup({Key? key}) : super(key: key);

  @override
  State<NewsPopup> createState() => _NewsPopupState();
}

class _NewsPopupState extends State<NewsPopup> {
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: Get.width * 0.9,
            height: Get.width * 0.9,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(width: 1.5, color: const Color(0xff282828)),
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          Container(
              width: 79,
              height: 34,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: const LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [
                    Color(0xffF3F3F3),
                    Color(0xffFFFFFF),
                  ],
                  stops: [0.0, 1.0],
                ),
                border: Border.all(width: 1.5, color: const Color(0xffFFFFFF)),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.1),
                    offset: const Offset(0, 4),
                    blurRadius: 10,
                  ),
                ],
              ),
              child: TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text(
                  'ปิดหน้านี้',
                  style: TextStyle(
                    fontFamily: 'Prompt',
                    fontSize: 14,
                    color: Color(0xff282828),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
          ),
        ],
      ),

    );
  }
}
