import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:restart_app/restart_app.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../../controller/updatePatch.controller.dart';

class AlertUpdatePatchPage extends StatefulWidget {
  const AlertUpdatePatchPage({Key? key}) : super(key: key);

  @override
  State<AlertUpdatePatchPage> createState() => _AlertUpdatePatchPageState();
}

class _AlertUpdatePatchPageState extends State<AlertUpdatePatchPage> {
  final centerCtl = Get.put(SettingController());
  final updatePatchCtl = Get.put(UpdatePatchController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFB100).withOpacity(0.6),
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
            child: SizedBox(
              width: 1.sw,
              height: 1.sh,
            ),
          ),
          SizedBox(
            width: 1.sw,
            height: 1.sh,
            child: Column(
              // mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 0.2.sh, right: 0.05.sw),
                  height: 0.1.sh,
                ),
                Image.asset(
                  "assets/image/version_Mascot.png",
                  width: 0.3.sw,
                ),
                SizedBox(
                  height: 0.02.sh,
                ),
                Obx(() => AppWidget.boldTextS(
                    context,
                    updatePatchCtl.restartApp.value == true
                        ? "อัปเดตแพทช์สำเร็จ!"
                        : "กำลังอัปเดตแพทช์...",
                    18.sp,
                    const Color(0xFF000000),
                    FontWeight.w500)),
                SizedBox(
                  height: 0.02.sh,
                ),
                Obx(() => updatePatchCtl.restartApp.value == true
                    ? Column(
                        children: [
                          AppWidget.boldTextS(
                              context,
                              "กดปุ่มรีสตาร์ทแอปด้านล่าง",
                              14.sp,
                              const Color(0xFFFFFFFF),
                              FontWeight.w400),
                          AppWidget.boldTextS(
                              context,
                              "และเข้าใช้งานแอปใหม่อีกครั้ง",
                              14.sp,
                              const Color(0xFFFFFFFF),
                              FontWeight.w400)
                        ],
                      )
                    : Column(
                        children: [
                          AppWidget.boldTextS(
                              context,
                              "เพื่อเพิ่มประสิทธิภาพการใช้งาน",
                              14.sp,
                              const Color(0xFFFFFFFF),
                              FontWeight.w400),
                          AppWidget.boldTextS(
                              context,
                              "และรองรับสำหรบฟีเจอร์ใหม่",
                              14.sp,
                              const Color(0xFFFFFFFF),
                              FontWeight.w400)
                        ],
                      )),
                SizedBox(
                  height: 0.04.sh,
                ),
                Obx(() => Container(
                      margin: EdgeInsets.only(
                        left: 0.06.sw,
                        right: 0.06.sw,
                      ),
                      child: updatePatchCtl.restartApp.value == true
                          ? InkWell(
                              onTap: () async {
                                /// In Web Platform, Fill webOrigin only when your new origin is different than the app's origin
                                Restart.restartApp();
                              },
                              child: Container(
                                width: 1.sw,
                                height: 0.06.sh,
                                margin: EdgeInsets.only(
                                  left: 0.02.sw,
                                  right: 0.02.sw,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(15),
                                    topLeft: Radius.circular(15),
                                    bottomRight: Radius.circular(15),
                                    bottomLeft: Radius.circular(15),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: const [0.0, 1.0],
                                    colors: [
                                      const Color(0xFF000000).withOpacity(0.7),
                                      const Color(0xFF000000),
                                    ],
                                  ),
                                  border: Border.all(
                                      width: 1, color: const Color(0xFF895F00)),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withOpacity(0.2),
                                      offset: const Offset(1, 2),
                                      blurRadius: 3,
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.boldText(
                                        context,
                                        "รีสตาร์ทแอป",
                                        15.sp,
                                        const Color(0xFFFFFFFF),
                                        FontWeight.w400),
                                    // AppWidget.normalText(context, " ตอนนี้", 15.sp,
                                    //     const Color(0xFFFFB100), FontWeight.w400)
                                  ],
                                ),
                              ),
                            )
                          : Column(
                              children: [
                                LinearPercentIndicator(
                                  barRadius: const Radius.circular(10),
                                  lineHeight: 8,
                                  percent: updatePatchCtl
                                          .downloadProgressNotifier.value /
                                      100,
                                  backgroundColor:
                                      Colors.black.withOpacity(0.2),
                                  progressColor: const Color(0xFFFFB100),
                                ),
                                SizedBox(
                                  height: 0.01.sh,
                                ),
                                AppWidget.normalText(
                                    context,
                                    "${updatePatchCtl.downloadProgressNotifier.value.round()}%",
                                    14,
                                    Colors.white,
                                    FontWeight.w400),
                              ],
                            ),
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }
}
