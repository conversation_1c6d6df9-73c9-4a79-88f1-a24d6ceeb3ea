import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/responsive/responsive_layout.dart';
import 'package:mapp_prachakij_v3/controller/responsive/tablet_body.dart';
// class HomeNavigator extends StatefulWidget {
//   const HomeNavigator({Key? key}) : super(key: key);
//
//   @override
//   State<HomeNavigator> createState() => _HomeNavigatorState();
// }
//
// class _HomeNavigatorState extends State<HomeNavigator> with TickerProviderStateMixin  {
//   final SecureStorage secureStorage = SecureStorage();
//
//   @override
//   Widget build(BuildContext context) {
//     // return ResponsiveLayout(mobileBody: const MobileBodyPage(), tabletBody: const TabletBodyPage());
//
//   }
//
// }

class HomeNavigator extends StatefulWidget {
  const HomeNavigator({super.key});

  @override
  State<HomeNavigator> createState() => _HomeNavigatorState();
}

class _HomeNavigatorState extends State<HomeNavigator> {
  @override
  Widget build(BuildContext context) {
    return const MobileBodyPage();
  }
}
