import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class AlertUpdateVersionPage extends StatefulWidget {
  const AlertUpdateVersionPage({Key? key}) : super(key: key);

  @override
  State<AlertUpdateVersionPage> createState() => _AlertUpdateVersionPageState();
}

class _AlertUpdateVersionPageState extends State<AlertUpdateVersionPage> {

  final centerCtl = Get.put(SettingController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFB100).withOpacity(0.6),
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
            child: SizedBox(
              width: 1.sw,
              height: 1.sh,
            ),
          ),
          SizedBox(
            width: 1.sw,
            height: 1.sh,
            child: Column(
              // mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: (){
                    Get.back();
                  },
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Container(
                      margin: EdgeInsets.only(
                          top: 0.1.sh,
                          right: 0.05.sw
                      ),
                      width: 0.1.sw,
                      height: 0.1.sw,
                      child: Image.asset('assets/image/X_icon.png'),
                    ),
                  ),
                ),
                SizedBox(height: 0.1.sh,),
                Image.asset("assets/image/version_Mascot.png", width: 0.3.sw,),
                SizedBox(
                  height: 0.02.sh,
                ),
                AppWidget.boldTextS(
                    context,
                    "อัพเดทเวอร์ชั่น ใหม่",
                    16.sp,
                    const Color(0xFF000000),
                    FontWeight.w500),
                AppWidget.normalText(
                    context,
                    "V. ${centerCtl.versionInStore.value}",
                    16.sp,
                    const Color(0xFF000000),
                    FontWeight.w300),
                SizedBox(
                  height: 0.01.sh,
                ),
                AppWidget.normalTextS(
                    context,
                    "เวอร์ชั่นปัจจุบันของคุณ ไม่รองรับ",
                    14.sp,
                    const Color(0xFFFFFFFF),
                    FontWeight.w400),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalTextS(
                        context,
                        "ฟังก์ชั่นการใช้งาน",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400),
                    AppWidget.boldTextS(
                        context,
                        " กรุณาอัปเดต",
                        14.sp,
                        const Color(0xFFFFFFFF),
                        FontWeight.w500),
                  ],
                ),
                AppWidget.normalTextS(
                    context,
                    "เพื่อเพิ่มประสิทธิภาพการใช้งานสำหรับฟีเจอร์ใหม่",
                    14.sp,
                    const Color(0xFFFFFFFF),
                    FontWeight.w400),
                SizedBox(
                  height: 0.04.sh,
                ),
                InkWell(
                  onTap: (){
                    AppService.launchUrl(centerCtl.updateUrl.value);
                  },
                  child: Container(
                    width: 1.sw,
                    height: 0.06.sh,
                    margin: EdgeInsets.only(
                      left: 0.06.sw,
                      right: 0.06.sw,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0.0, 1.0],
                        colors: [
                          const Color(0xFF000000).withOpacity(0.7),
                          const Color(0xFF000000),
                        ],
                      ),
                      border: Border.all(width: 1, color: const Color(0xFF895F00)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          offset: const Offset(1, 2),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppWidget.boldText(
                            context,
                            "อัพเดท",
                            15.sp,
                            const Color(0xFFFFFFFF),
                            FontWeight.w400),
                        AppWidget.normalText(
                            context,
                            " ตอนนี้",
                            15.sp,
                            const Color(0xFFFFB100),
                            FontWeight.w400)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
