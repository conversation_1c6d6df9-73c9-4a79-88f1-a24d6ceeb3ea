import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_insurance_referral.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_newcar_referral.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_product_referral.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_service_referral.dart';

class historyReferralPage extends StatefulWidget {
  const historyReferralPage({Key? key}) : super(key: key);

  @override
  State<historyReferralPage> createState() => _historyReferralPageState();
}


class _historyReferralPageState extends State<historyReferralPage> with TickerProviderStateMixin {

  TabController? _historyTabController;

  int currentIndex = 0;

  void _handleTabSelection() {
    setState(() {
      currentIndex = _historyTabController!.index;
    });
  }

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "HistoryMarketingRepresentative");
    _historyTabController = TabController(vsync: this, length: 4);
    _historyTabController!.addListener(_handleTabSelection);
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
    minTextAdapt: true,
    splitScreenMode: true,
    builder: (context, child) {
      return Scaffold(
        body: Stack(
          children: <Widget>[
            Container(
              width: width,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFEDEDED),
                    Color(0xFFF2F2F2),
                  ],
                ),
              ),
            ),
        Container(
          width: MediaQuery.of(context).size.height * 1,
          height: MediaQuery.of(context).size.height * 0.15,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0, 0.3909, 1],
              colors: [
                const Color(0xFFFFB100).withOpacity(1.0),
                const Color(0xFFFFC700).withOpacity(0.7),
                const Color(0xFFFFC700).withOpacity(0),
              ],
            ),
          ),
        ),
            Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 55.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: (){
                          Navigator.pop(context);
                        },
                        child: Container(
                          margin: EdgeInsets.only(
                            left: 15.w,
                          ),
                          height: 35.h,
                          width: 35.h,
                          child: Icon(Icons.arrow_back_ios_new,
                            color: const Color(0xFF282828),
                            size: 15.w,
                          ),
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                            children: [
                              TextSpan(
                                text: 'ประวัติกิจกรรมแนะนำ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  color: const Color(0xFF282828),
                                  fontSize: 14.w,
                                ),
                              ),
                            ]
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                          right: 15.w,
                        ),
                        height: 35.h,
                        width: 35.h,
                      ),
                    ],
                  ),
                ),
                buildTabBar(),
                Expanded(
                  child: TabBarView(
                    controller: _historyTabController,
                    children: const [
                      historyServiceReferralPage(),
                      historyNewcarReferral(),
                      historyProductReferral(),
                      HistoryInsuranceReferralPage(),
                    ],
                  ),
                ),

              ],
            ),

          ],
        ),
      );
  }
    );
  }
  buildTabBar(){
    return DefaultTabController(
        length: 4,
        child: PreferredSize(
            preferredSize: const Size.fromHeight(30.0),
            child: TabBar(
              controller: _historyTabController,
              indicatorColor: Colors.transparent,
              isScrollable: true,
              labelPadding: EdgeInsets.only(
                left: 0.01.sw,
                right: 0.01.sw,
              ),
              padding: EdgeInsets.only(left: 0.05.sw,right: 0.05.sw),
              tabs: [
                Tab(
                    child: currentIndex == 0
                        ? buildSelectTab('งานขาย')
                        : buildUnSelectTab('งานขาย')
                ),
                Tab(
                    child: currentIndex == 1
                        ? buildSelectTab('งานศูนย์บริการ')
                        : buildUnSelectTab('งานศูนย์บริการ')
                ),
                Tab(
                    child: currentIndex == 2
                        ? buildSelectTab('ศูนย์ซ่อมสีและตัวถัง')
                        : buildUnSelectTab('ศูนย์ซ่อมสีและตัวถัง')
                ),
                Tab(
                    child: currentIndex == 3
                        ? buildSelectTab('อู่พีเอ็มจี เซอร์วิส')
                        : buildUnSelectTab('อู่พีเอ็มจี เซอร์วิส')
                ),
              ],
            )
        )
    );
  }

  buildSelectTab(String text){
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF664701).withOpacity(0.8),
              const Color(0xFF282828),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(1),
              offset: const Offset(1, 1),
              blurRadius: 5,
            ),
          ],
          border: Border.all(color: const Color(0xFF895F00).withOpacity(0.4))
      ),
      padding: EdgeInsets.only(
        top: 5.h,
        bottom: 5.h,
        right: 13.w,
        left: 13.w,
      ),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(text,
          style: TextStyle(
            fontFamily: 'Prompt-Medium',
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            color: const Color(0xFFFFFFFF),
          ),),
      ),
    );
  }
  
  buildUnSelectTab(String text){
    return Container(
      padding: EdgeInsets.only(
        top: 5.h,
        bottom: 5.h,
        right: 13.w,
        left: 13.w,
      ),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(text,
          style: TextStyle(
            fontFamily: 'Prompt',
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF707070).withOpacity(.5),
          ),),
      ),
    );
  }
}
