import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../controller/setting_controller/likepoint_controller/view_get_likepoint_controller.dart';

class NewsViewPage extends StatefulWidget {

  NewsViewPage();

  @override
  State<NewsViewPage> createState() =>  _NewsViewPageState();
}

class _NewsViewPageState extends State<NewsViewPage> {
  _NewsViewPageState();

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  Timer _timer = Timer.periodic(Duration.zero, (timer) {});
  int _start = 0;

  final viewGetLikePointCtl = Get.put(ViewGetLikePointController());
  final profileCtl = Get.put(ProfileController());
  final eventAndNewsCtl = Get.put(EventAndNewsController());
  final promotionCtl = Get.find<PromotionController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "NewsView");
    getDataIfToday(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId);
  }

  Future<void> getDataIfToday(runningAds) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lastVisit = prefs.getString('lastVisit') ?? ""; // เช็ควันเวลาล่าสุดที่เข้ามาดูหน้านี้
    String today = DateTime.now().toString().substring(0, 10); // วันนี้
    if (lastVisit == today && prefs.getBool('isViewed_${eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId}') == true) {
      Get.snackbar("PMSpoint", "วันนี้คุณได้รับ PMSpoint แล้ว");
      return;
    } else {
      await getData();
      await prefs.setString('lastVisit', today); // บันทึกวันเวลาล่าสุดที่เข้ามาดูหน้านี้
      await prefs.setBool('isViewed_${eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId}', true); // บันทึกว่าผู้ใช้ดู runningAds นี้แล้ว
    }
  }

  getData() async {
    if(profileCtl.token.value != null){
      await viewGetLikePointCtl.getTime();
      await viewGetLikePointCtl.checkCanGetLikePOI(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId, "share", eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].type);
      await viewGetLikePointCtl.checkCanGetLikePOI(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId, "watch", eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].type);
      if(viewGetLikePointCtl.getLikeWatchLoader.value == true){
        startTimer(viewGetLikePointCtl.timeOut.value);
      }
    }
  }

  void startTimer(timeout) {
    _start = timeout;
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
          (Timer timer2) => setState(() {
        if (_start < 1) {
          timer2.cancel();
          viewGetLikePointCtl.getPOIReward(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationId, "watch", eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].type);
        } else {
          _start -= 1;
        }
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(0.0, -1.0),
                end: Alignment(0.0, 1.0),
                colors: [Color(0xff505050), Color(0xff282828)],
                stops: [0.0, 1.0],
              ),
            ),
          ),
          Column(
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                width: Get.width,
                height: 90,
                padding: const EdgeInsets.only(
                    left: 18,
                    right: 19
                ),
                child:  Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: (){
                        Navigator.pop(context);
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        alignment: Alignment.centerLeft,
                        child: const Icon(
                          Icons.arrow_back_ios,
                          color: Color(0xFFFFB100),
                          size: 16,
                        ),
                      ),
                    ),
                    const Text('รายละเอียด',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Prompt-Medium',
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(
                      width: 50,
                      height: 50,
                    )
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      CachedNetworkImage(
                          imageUrl: '${promotionCtl.urlEvent.value}${eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationPic}',
                        width: Get.width,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => const SizedBox(
                          width: 50,
                          height: 50,
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Colors.orange,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => const Icon(
                          Icons.error,
                          color: Color(0xFFFFB100),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(
                          top: 10,
                          left: 18,
                          right: 18,
                        ),
                        child: Column(
                          children: [
                            Text(
                              eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationName.toString(),
                              style: const TextStyle(
                                fontFamily: 'Prompt-Medium',
                                fontSize: 18,
                                color: Color(0xFFFFB100),
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Text(
                              AppService.parseHtmlString(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationBody.toString()),
                              style: const TextStyle(
                                fontFamily: 'Prompt-Medium',
                                fontSize: 16,
                                color: Color(0xFFFFFFFF),
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationLink != ""
                                ? InkWell(
                                  onTap: () {
                                    AppService.launchUrl(eventAndNewsCtl.newsList.data![eventAndNewsCtl.indexNews.value].informationLink);
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      color: const Color(0xffFFA700),
                                    ),
                                    padding: const EdgeInsets.only(
                                      left: 10,
                                      right: 10,
                                      top: 5,
                                      bottom: 5,
                                    ),
                                    child: const Text(
                                      "กด Link ที่นี่",
                                      style: TextStyle(
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 16,
                                        color: Color(0xFFFFFFFF),
                                      ),
                                    ),
                                  ),
                                )
                                : Container(),
                             const SizedBox(height: 20,),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _start > 0
              ? Align(
                alignment: Alignment.bottomCenter,
                child: SizedBox(
                  width: Get.width,
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.boldText(
                          context,
                          'คุณจะได้รับ PMSpoint ภายใน',
                          15,
                          const Color(0xffFFA700),
                          FontWeight.w400),
                      const SizedBox(
                        width: 20,
                      ),
                      Container(
                        alignment: Alignment.center,
                        width: 50,
                        height: 30,
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.elliptical(9999.0, 9999.0),
                          ),
                          color: Color(0xff27e4e1),
                        ),
                        child: AppWidget.boldText(
                            context,
                            _start.toString(),
                            15,
                            const Color(0xff191f26),
                            FontWeight.w400),
                      ),
                    ],
                  ),
                ),
              )
                : Obx(() => viewGetLikePointCtl.getLikeWatchLoader.value == true
                  ? Align(
                alignment: Alignment.bottomCenter,
                child: SizedBox(
                  width: Get.width,
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.boldText(
                          context,
                          'กำลังบันทึกข้อมูล',
                          15,
                          const Color(0xffFFA700),
                          FontWeight.w400),
                      const SizedBox(
                        width: 20,
                      ),
                      Container(
                        alignment: Alignment.center,
                        width: 30,
                        height: 30,
                        child: const CircularProgressIndicator(
                          color: Color(0xff27e4e1),
                          strokeWidth: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              )
                  : const SizedBox()),
            ],
          ),
        ],
      )
    );
  }
}
