import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AppointmentPage extends StatefulWidget {
  const AppointmentPage({Key? key}) : super(key: key);

  @override
  State<AppointmentPage> createState() => _AppointmentPageState();
}

class _AppointmentPageState extends State<AppointmentPage> {

  DateTime selectedDay = DateTime.now();
  DateTime focusedDay = DateTime.now();

  DateTime currentTime = DateTime.now();

  final appointmentCtl = Get.put(AppointmentController());

  String timeNow = "";
  int timeHour = 0;
  int timeMinute = 0;
  DateTime now = DateTime.now();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    timeNow = DateFormat("HH:mm").format(DateTime.now());
    print("timeNow");
    print(timeNow);

    timeHour = int.parse(timeNow.split(":")[0]);
    timeMinute = int.parse(timeNow.split(":")[1]);
  }
  @override
  Widget build(BuildContext context) {

    return Scaffold(
        body: Obx(() => appointmentCtl.isLoading.value
            ? AppLoader.loaderWaitPage(context)
            : InkWell(
          onTap: (){
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          },
          child: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFF5F5F5),
                      Color(0xFFF5F5F5),
                    ],
                  ),
                ),
              ),
              NotificationListener<ScrollNotification>(
                onNotification: (notificationInfo) {
                  if (notificationInfo is ScrollUpdateNotification) {
                    appointmentCtl.scrollParam.value = notificationInfo.metrics.pixels;
                  }
                  return true;
                },
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 100,
                      ),
                      buildHeadContent(),
                      const SizedBox(
                          height: 10
                      ),
                      buildContent(),
                      const Padding(padding: EdgeInsets.only(bottom: 25))
                    ],
                  ),
                ),
              ),
              Obx(() => appointmentCtl.scrollParam.value >= 14
                  ? ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: SizedBox(
                      width: Get.width,
                      height: 100
                  ),
                ),
              ):
              const SizedBox(),
              ),
              buildAppBar(),
            ],
          ),
        )
        )
    );
  }

  buildAppBar() {
    return Container(
      height: 100,
      margin: EdgeInsets.only(
        top: 16,
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              Get.back();
              appointmentCtl.timeSelect.value = "";
              appointmentCtl.branchSelect.value = "";
              appointmentCtl.carSelect.value = "";
              appointmentCtl.carRegSelect.value = "";
              appointmentCtl.showSaveButton.value = false;
            },
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(15),
                    topLeft: Radius.circular(15),
                    bottomRight: Radius.circular(15),
                    bottomLeft: Radius.circular(15),
                  ),
                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
              child: const Icon(
                Icons.arrow_back_ios_new,
                size: 18,
                color: Color(0xFFFFB100),
              ),
            ),
          ),
          Row(
            children: [
              AppWidget.boldTextS(context, "จองคิว", 18, const Color(0xFF2B1710),
                  FontWeight.w500),
              AppWidget.normalTextS(context, "ศูนย์บริการ", 18, const Color(0xFF895F00),
                  FontWeight.w400),
            ],
          ),
          const SizedBox(
            width: 36,
            height: 36,
          ),
        ],
      ),
    );
  }

  buildHeadContent() {
    return Container(
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppWidget.boldText(context, "บริการนัดหมายจองคิวล่วงหน้า",
              Get.width < 500 ? 14 : 16, const Color(0xFF895F00), FontWeight.w500),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '* ',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: const Color(0xFFCA0E1E),
                        fontSize: Get.width < 500 ? 12 : 14,
                      ),
                    ),
                    TextSpan(
                      text: 'กรุณาเลือกรายการด้านล่าง เพื่อทำการนัดหมาย',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: const Color(0xFF282828),
                        fontSize: Get.width < 500 ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Image.asset(
            'assets/image/service/menu_appoint.png',
            width: 72,
          )
        ],
      ),
    );
  }

  buildContent(){
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.boldText(context, "เลือกสาขา",
              Get.width < 500 ? 14 :16, const Color(0xFF282828), FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          InkWell(
            onTap: ()async{
              // openDialogBranch.value = !openDialogBranch.value;
              await buildBranchDialog();
            },
            child: Obx(() => Container(
              width: Get.width,
              height: Get.width < 500 ? 50 : 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xFFFFFFFF),
                gradient: appointmentCtl.branchSelect.value != ""
                    ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 1.0],
                  colors: [
                    const Color(0xFF000000).withOpacity(0.7),
                    const Color(0xFF000000),
                  ],
                )
                    : null,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  appointmentCtl.branchSelect.value == ""
                      ? const Icon(Icons.location_on_outlined, color: Color(0xFFBCBCBC), size: 20,)
                      : const Icon(Icons.location_on_outlined, color: Color(0xFFFFB100), size: 20,),
                  const SizedBox(
                    width: 3,
                  ),
                  appointmentCtl.branchSelect.value == "" ?
                  AppWidget.normalText(context, "กรุณาเลือกสาขาที่ต้องการ",
                      Get.width < 500 ? 14 : 16, const Color(0xFFBCBCBC), FontWeight.w400)
                      : AppWidget.normalText(context, appointmentCtl.branchSelect.value,
                      Get.width < 500 ? 14 : 16, const Color(0xFFFFFFFF), FontWeight.w400),
                ],
              ),
            )
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          AppWidget.boldText(context, "เลือกประเภทรถ",
              Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: Get.width < 500 ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
            children: [
              buildSelectCar("รถปิกอัพ", "assets/image/service/appointment/VCross_car.png"),
              Get.width < 500 ? const SizedBox() : const SizedBox(
                width: 20,
              ),
              buildSelectCar("รถอเนกประสงค์", "assets/image/service/appointment/MuX_car.png"),
              Get.width < 500 ? const SizedBox() : const SizedBox(
                width: 20,
              ),
              buildSelectCar("รถบรรทุก", "assets/image/service/appointment/Truck_car.png"),
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          AppWidget.boldText(context, "ข้อมูลทะเบียนรถ",
              Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          InkWell(
            onTap: () async {
              await buildCarRegDialog();
            },
            child: Container(
              width: Get.width,
              height: Get.width < 500 ? 50 : 60,
              padding: const EdgeInsets.only(
                  left: 15,
                  right: 15
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: const Color(0xFFFFFFFF),
                  border: appointmentCtl.carRegSelect.value != ""
                      ? Border.all(
                      width: 1,
                      color: const Color(0xFFFFB100)
                  ) : null
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 25,
                  ),
                  Obx(() => appointmentCtl.carRegSelect.value == ""
                      ? AppWidget.normalText(context, "เลือกหมายเลขทะเบียนรถ",
                      Get.width < 500 ? 14 : 16, const Color(0xFFBCBCBC), FontWeight.w500)
                      : AppWidget.boldText(context, appointmentCtl.carRegSelect.value,
                      Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500)),
                  const Icon(Icons.arrow_drop_down_rounded, color: Color(0xFFFFC700), size: 25,),
                ],
              ),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppWidget.boldText(context, "เลือกวันที่และเวลา",
                  Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500),
              AppWidget.normalText(context, " นัดหมายที่ต้องการ",
                  Get.width < 500 ? 13 : 15, const Color(0xFF895F00), FontWeight.w400),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(15),
              ),
              color: Colors.white,
            ),
            child: TableCalendar(
              locale: 'th_TH',
              focusedDay: focusedDay,
              firstDay: Jiffy.now().add(years: -2).dateTime,
              lastDay: Jiffy.now().add(years: 5).dateTime,
              daysOfWeekVisible: true,
              weekendDays: const [
                DateTime.sunday
              ],
              calendarFormat: CalendarFormat.week,
              headerStyle: HeaderStyle(
                titleTextFormatter: (date, locale) {
                  return AppService.dateEng2Tha(
                    date.toString(),
                  );
                },
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: TextStyle(
                    fontFamily: 'Prompt-Medium',
                    color: const Color(0xFF282828),
                    fontSize:   Get.width < 500 ? 14 : 16,
                    fontWeight: FontWeight.w500
                ),
                leftChevronIcon: const Icon(
                  Icons.arrow_left_rounded,
                  color: Color(0xFFFFB100),
                  size: 25,
                ),
                rightChevronIcon: const Icon(
                  Icons.arrow_right_rounded,
                  color: Color(0xFFFFB100),
                  size: 25,
                ),
              ),
              onDaySelected: (selectedDayIn, focusedDayIn) {

                if(selectedDayIn.weekday == DateTime.sunday) {
                  return ;
                } else if(selectedDayIn.isBefore(now) && selectedDayIn.day != now.day) {
                  return ;
                } else {
                  selectedDay = selectedDayIn;
                  focusedDay = focusedDayIn;
                  appointmentCtl.timeSelect.value = "";
                  appointmentCtl.showSaveButton.value = false;
                  setState(() {});
                }

              },
              selectedDayPredicate: (DateTime date) {
                if(date.weekday == DateTime.sunday) {
                  return false;
                } else if(date.isBefore(now) && date.day != now.day) {
                  return false;
                } else {
                  return isSameDay(selectedDay, date);
                }

              },
              calendarStyle: CalendarStyle(
                cellMargin: const EdgeInsets.only(top: 10, bottom: 10, left: 7, right: 7),
                defaultTextStyle: const TextStyle(
                  color: Color(0xFFBCBCBC),
                ),

                selectedDecoration: BoxDecoration(
                    color: const Color(0xFFFFB100),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      width: 1.5,
                      color: const Color(0xFFE8E6E2),
                    ),
                    shape: BoxShape.rectangle
                ),
                disabledDecoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    shape: BoxShape.rectangle
                ),
                defaultDecoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    shape: BoxShape.rectangle),
                weekendDecoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    shape: BoxShape.rectangle),
                todayDecoration: BoxDecoration(
                    color: const Color(0xFFEBEBEB),
                    borderRadius: BorderRadius.circular(10),
                    shape: BoxShape.rectangle
                ),
                todayTextStyle: const TextStyle(
                  color: Color(0xFFBCBCBC),
                ),
                weekendTextStyle: const TextStyle(
                  color: Color(0xFFBCBCBC),
                ),
                selectedTextStyle: const TextStyle(
                  color: Colors.white,
                ),
              ),
              startingDayOfWeek: StartingDayOfWeek.sunday,
              daysOfWeekStyle: const DaysOfWeekStyle(
                weekdayStyle: TextStyle(
                  color: Color(0xFF895F00),
                ),
                weekendStyle: TextStyle(
                  color: Color(0xFFFFB7B7),
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            height: 92,
            padding: EdgeInsets.only(
              left:  Get.width < 500 ? 16 : 28,
              right:  Get.width < 500 ? 16 : 28,
            ),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(15),
              ),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    buildTimeBtn('8:30'),
                    buildTimeBtn('9:00'),
                    buildTimeBtn('10:30'),
                    buildTimeBtn('11:00'),
                  ],
                ),
                const SizedBox(
                  height: 5,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    buildTimeBtn('13:00'),
                    buildTimeBtn('13:30'),
                    buildTimeBtn('14:00'),
                    buildTimeBtn('14:30'),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          AppWidget.boldText(context, "เบอร์โทรติดต่อ",
              Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          Container(
            width: Get.width,
            height: Get.width < 500 ? 50 : 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: const Color(0xFFFFFFFF),
            ),
            alignment: Alignment.center,
            child: TextField(
              controller: appointmentCtl.phoneController,
              textAlign: TextAlign.center,
              onChanged: checkSaveData(),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  AppService.number10RegExp(),
                ),
              ],
              style: TextStyle(
                fontFamily: 'Prompt-Medium',
                fontSize:  Get.width < 500 ? 14 : 16,
                color: const Color(0xFF282828),
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: "กรอกเบอร์โทรศัพท์",
                hintStyle: TextStyle(
                  fontFamily: 'Prompt',
                  fontSize:  Get.width < 500 ? 14 : 16,
                  color: const Color(0xFF707070).withOpacity(0.6),
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          Obx(() => appointmentCtl.showSaveButton.isTrue
              ? InkWell(
            onTap: (){
              saveData();
            },
            child: Container(
              width: Get.width,
              height: Get.width < 500 ? 50 : 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 1.0],
                  colors: [
                    const Color(0xFF000000).withOpacity(0.7),
                    const Color(0xFF000000),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    offset: const Offset(0, 4),
                    blurRadius: 10.0,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppWidget.boldText(context, "ยืนยัน",
                      Get.width < 500 ? 14 : 16, const Color(0xFFFFB100), FontWeight.w500),
                  AppWidget.normalText(context, " ข้อมูล",
                      Get.width < 500 ? 14 : 16, const Color(0xFFFFFFFF), FontWeight.w500),
                ],
              ),
            ),
          )
              : const SizedBox()),
        ],
      ),
    );
  }

  buildSelectCar(text, picture){
    return Obx(() => InkWell(
      onTap: () async {
        appointmentCtl.carSelect.value = text;
        checkSaveData();
      },
      child: SizedBox(
        width: 99,
        height: 80,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                  alignment: Alignment.bottomCenter,
                  width: 99,
                  height: 58,
                  padding: const EdgeInsets.only(
                    bottom: 8,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Colors.white,
                    gradient: appointmentCtl.carSelect.value == text ? const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 0.5125, 1.0],
                      colors: [
                        Color(0xFFffc402),
                        Color(0xFFFFB100),
                        Color(0xFFFF9900),
                      ],
                    ) : null,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000).withOpacity(0.1),
                        spreadRadius: 0,
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: AnimatedSize(duration: const Duration(milliseconds: 100), child: AppWidget.normalText(context, text,
                      appointmentCtl.carSelect.value == text ? 12.5 : 12, appointmentCtl.carSelect.value == text ? const Color(0xFFFFFFFF) :const Color(0xFFBCBCBC), appointmentCtl.carSelect.value == text ? FontWeight.w600 : FontWeight.w400),)
              ),
            ),
            AnimatedSize(duration: const Duration(milliseconds: 300),child: Image.asset(picture, height: appointmentCtl.carSelect.value == text ? 65 : 60, width: appointmentCtl.carSelect.value == text ? 80 : 75,),)
          ],
        ),
      ),
    ));
  }

  buildTimeBtn(time) {
    var timeHourhere = int.parse(time.split(":")[0]);
    var timeMinutehere = int.parse(time.split(":")[1]);
    return Obx(() => appointmentCtl.timeSelect.value != time
        ? InkWell(
      onTap: (){
        print(selectedDay);
        print(now);

        if(selectedDay.isBefore(now) && selectedDay.day != now.day) {
          Fluttertoast.showToast(msg: "ไม่สามารถจองคิวในวันที่และเวลาที่ผ่านมาได้", backgroundColor: Colors.red, textColor: Colors.white, fontSize: 13.0);

        } else if(selectedDay.isBefore(now) && timeHour < timeHourhere) {
          appointmentCtl.timeSelect.value = time;
          checkSaveData();
        } else if(timeHour == timeHourhere && timeMinute < timeMinutehere) {
          appointmentCtl.timeSelect.value = time;
          checkSaveData();
        } else if(selectedDay.isBefore(now) == false){
          appointmentCtl.timeSelect.value = time;
          checkSaveData();
        }else{
          Fluttertoast.showToast(msg: "ไม่สามารถจองคิวในวันที่และเวลาที่ผ่านมาได้", backgroundColor: Colors.red, textColor: Colors.white, fontSize: 13.0);
        }
      },
      child: Container(
          alignment: Alignment.center,
          width: 51,
          height: 26,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: selectedDay.isBefore(now) ? selectedDay.day != now.day ? const Color(0x22FA4862):timeHour < timeHourhere ? Colors.transparent : timeHour == timeHourhere && timeMinute < timeMinutehere ? Colors.transparent : const Color(0x22FA4862) : Colors.transparent,
              border: Border.all(
                  width: 1.5,
                  color: selectedDay.isBefore(now) ? selectedDay.day != now.day ? Color(0x22FA4862):timeHour < timeHourhere ? const Color(0xFFE8E6E2) : timeHour == timeHourhere && timeMinute < timeMinutehere ? const Color(0xFFE8E6E2) :const Color(0x22FA4862) : const Color(0xFFE8E6E2)
              )
          ),
          child:AppWidget.normalText(
              context,
              time,
              12,
              selectedDay.isBefore(now) ? selectedDay.day != now.day ? const Color(0xFFFA4862):timeHour < timeHourhere ? const Color(0xFFBCBCBC) : timeHour == timeHourhere && timeMinute < timeMinutehere ? const Color(0xFFBCBCBC) : const Color(0xFFFA4862) : const Color(0xFFBCBCBC),
              FontWeight.w400)
      ),
    )
        : InkWell(
      onTap: (){
        appointmentCtl.timeSelect.value = time;
      },
      child: Container(
          alignment: Alignment.center,
          width: 51,
          height: 26,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0, 0.5125, 1.0],
                colors: [
                  Color(0xFFffc402),
                  Color(0xFFFFB100),
                  Color(0xFFFF9900),
                ],
              ),
              border: Border.all(
                  width: 1.5,
                  color: const Color(0xFFE8E6E2)
              )
          ),
          child: AppWidget.boldText(
              context,
              time,
              12,
              const Color(0xFFFFFFFF),
              FontWeight.w500)
      ),
    )
    );
  }

  buildBranchDialog(){
    return showModalBottomSheet(
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        isScrollControlled: false,
        context: context,
        builder: (_){
          return Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: Get.width,
              height: 550,
              padding: EdgeInsets.only(
                top: 20,
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      AppWidget.normalText(context, "สาขา",
                          Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w400),
                      AppWidget.boldText(context, "ศูนย์บริการ",
                          Get.width < 500 ? 14 : 16, const Color(0xFF895F00), FontWeight.w500),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  buildBranchContainer("สำนักงานใหญ่"),
                  buildBranchContainer("นายายอาม"),
                  buildBranchContainer("ขลุง"),
                  buildBranchContainer("สอยดาว"),
                ],
              ),
            ),
          );
        });
  }

  buildBranchContainer(text){
    return Obx(() => InkWell(
      onTap: (){
        Future.delayed(const Duration(milliseconds: 500), () {
          Get.back();
        });
        Future.delayed(const Duration(milliseconds: 800), () {
          appointmentCtl.branchSelect.value = text;
          checkSaveData();
        });
      },
      child: Container(
        alignment: Alignment.center,
        width: Get.width,
        height: 50,
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            width: 1,
            color: appointmentCtl.branchSelect.value == text ? const Color(0xFFFFB100) : const Color(0xFFEDEDED),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, -4),
            ),
          ],
        ),
        child: AppWidget.boldText(context, text,
            Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w400),
      ),));
  }

  buildCarRegDialog(){
    return showModalBottomSheet(
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        isScrollControlled: true,
        context: context,
        builder: (_) => const DialogCarReg()).then((value) {
      appointmentCtl.addCar.value = false;
    });
  }

  checkSaveData(){
    if(appointmentCtl.branchSelect.value == ""){
      return;
    }
    if(appointmentCtl.carRegSelect.value == ""){
      return;
    }
    if(appointmentCtl.timeSelect.value == ""){
      return;
    }
    appointmentCtl.showSaveButton.value = true;
  }

  saveData() async {
    AppLoader.loader(context);
    bool isAfter5PM = currentTime.hour >= 17;
    var status = await appointmentCtl.popupBeforeCallAPI(context, selectedDay.toString());
    print("status");
    print(status);
    if(status == 200){
      AppLoader.dismiss(context);
      if(isAfter5PM){
        var res = await AppAlert.showNewAccept(context, "ขอบคุณที่ใช้บริการ", "เราได้รับข้อมูลการจองคิวของคุณ\nเรียบร้อย กรุณารอการติดต่อกลับ\nเพื่อยืนยันอีกครั้ง ในวันถัดไปภายใน\nไม่เกิน 9:00 น. เนื่องจากคุณทำรายการ\nนอกเวลาทำการ ขออภัยในความไม่สะดวก", "ตกลง");
        if(res == true){
          appointmentCtl.timeSelect.value = "";
          appointmentCtl.branchSelect.value = "";
          appointmentCtl.carSelect.value = "";
          appointmentCtl.carRegSelect.value = "";
          appointmentCtl.showSaveButton.value = false;
          Get.back();
        }
        return;
      }
      var res = await AppAlert.showNewAccept(context, "ขอบคุณที่ใช้บริการ", "เราได้รับข้อมูลการจองคิวของคุณ\nเรียบร้อย กรุณารอการติดต่อกลับ\nเพื่อยืนยันอีกครั้ง", "ตกลง");
      if(res == true){
        appointmentCtl.timeSelect.value = "";
        appointmentCtl.branchSelect.value = "";
        appointmentCtl.carSelect.value = "";
        appointmentCtl.carRegSelect.value = "";
        appointmentCtl.showSaveButton.value = false;
        Get.back();
      }
    } if(!status) {
      AppLoader.dismiss(context);

    }else {
      AppLoader.dismiss(context);
      AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
    }
  }
}

class DialogCarReg extends StatefulWidget {
  const DialogCarReg({Key? key}) : super(key: key);

  @override
  State<DialogCarReg> createState() => _DialogCarRegState();
}

class _DialogCarRegState extends State<DialogCarReg> {

  final appointmentCtl = Get.put(AppointmentController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            GestureDetector(
              onTap: (){
                Get.back();
              },
              child: Container(width: Get.width,height: Get.height, color: Colors.transparent,),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Obx(() => InkWell(
                onTap: (){
                  FocusScope.of(context).requestFocus(FocusNode());
                },
                child: AnimatedContainer(
                  width: Get.width,
                  height: appointmentCtl.addCar.value ? 550 : 550,
                  padding: EdgeInsets.only(
                    top: 20,
                    left: Get.width < 500 ? 18 : 54,
                    right: Get.width < 500 ? 18 : 54,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000).withOpacity(0.05),
                        spreadRadius: 0,
                        blurRadius: 10,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  duration: const Duration(milliseconds: 500),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              AppWidget.normalText(context, "ข้อมูล",
                                  Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w400),
                              AppWidget.boldText(context, "ทะเบียนรถ",
                                  Get.width < 500 ? 14 : 16, const Color(0xFF895F00), FontWeight.w500),
                            ],
                          ),
                          Obx(() => !appointmentCtl.addCar.value
                              ? Row(
                            children: [
                              !appointmentCtl.editCar.value
                                  ? InkWell(
                                onTap:()async{
                                  appointmentCtl.addCar.value = true;
                                  await appointmentCtl.getProvince();
                                },
                                child: Container(
                                  width: 83,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: [0, 1.0],
                                      colors: [
                                        Color(0xFF664701),
                                        Color(0xFF1D1400),
                                      ],
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset("assets/image/service/appointment/Add_ring_duotone.svg"),
                                      AppWidget.boldText(context, " เพิ่มใหม่",
                                          12, const Color(0xFFFFFFFF), FontWeight.w500),
                                    ],
                                  ),
                                ),
                              )
                                  : const SizedBox(
                                width: 83,
                                height: 36,
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              InkWell(
                                  onTap: (){
                                    appointmentCtl.editCar.value = !appointmentCtl.editCar.value;
                                  },
                                  child: Obx(() => Image.asset("assets/image/MR/Edit_duotone.png", width: 25, height: 25,opacity: appointmentCtl.editCar.value ? const AlwaysStoppedAnimation(.2) : const AlwaysStoppedAnimation(1),
                                  )))
                            ],
                          )
                              : Row(
                            children: [
                              InkWell(
                                onTap: (){
                                  checkSaveCar();
                                },
                                child: Container(
                                  width: 83,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: [0, 1.0],
                                      colors: [
                                        Color(0xFF664701),
                                        Color(0xFF1D1400),
                                      ],
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset("assets/image/service/appointment/save.svg"),
                                      AppWidget.boldText(context, " บันทึก",
                                          12, const Color(0xFFFFFFFF), FontWeight.w500),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          )),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Obx(() => !appointmentCtl.addCar.value
                          ? buildListCarReg()
                          : Column(
                        children: [
                          buildTextField(
                              TextField(
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    AppService.numberAZThai3RegExp(),
                                  ),
                                ],
                                controller: appointmentCtl.carCharacterController,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Prompt',
                                  fontSize:  Get.width < 500 ? 14 : 16,
                                  color: const Color(0xFF282828),
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "กรอกตัวเลข/พยัญชนะ",
                                  hintStyle: TextStyle(
                                    fontFamily: 'Prompt',
                                    fontSize:  Get.width < 500 ? 14 : 16,
                                    color: const Color(0xFFBCBCBC),
                                  ),
                                ),
                              )),
                          const SizedBox(height: 10,),
                          buildTextField(
                              TextField(
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    AppService.number6RegExp(),
                                  ),
                                ],
                                keyboardType: TextInputType.number,
                                controller: appointmentCtl.carNumberController,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontFamily: 'Prompt',
                                  fontSize:  Get.width < 500 ? 14 : 16,
                                  color: const Color(0xFF282828),
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "กรอกเลขทะเบียน",
                                  hintStyle: TextStyle(
                                    fontFamily: 'Prompt',
                                    fontSize:  Get.width < 500 ? 14 : 16,
                                    color: const Color(0xFFBCBCBC),
                                  ),
                                ),
                              )),
                          const SizedBox(height: 10,),
                          dropdownCounty(context, appointmentCtl.openProvince, appointmentCtl.province, appointmentCtl.provinceSelect, "กรอกจังหวัด")
                        ],
                      )),
                    ],
                  ),
                ),
              )),
            ),
          ],
        )
    );
  }

  buildCarRegContainer(text, id){
    return Obx(() => InkWell(
        onTap: (){
          if(appointmentCtl.editCar.value){
            return;
          }
          appointmentCtl.carRegSelect.value = text;
          checkSaveData();
          Future.delayed(const Duration(milliseconds: 500), () {
            Get.back();
          });
        },
        child: !appointmentCtl.editCar.value
            ? Container(
          alignment: Alignment.center,
          width: Get.width,
          height: 50,
          margin: const EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              width: 1,
              color: appointmentCtl.carRegSelect.value == text ? const Color(0xFFFFB100) : const Color(0xFFEDEDED),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.05),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          child: AppWidget.boldText(context, text,
              Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w400),
        )
            : Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              alignment: Alignment.center,
              width: Get.width * 0.75,
              height: 50,
              margin: const EdgeInsets.only(bottom: 10),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  width: 1,
                  color: appointmentCtl.carSelect.value == text ? const Color(0xFFFFB100) : const Color(0xFFEDEDED),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: AppWidget.boldText(context, text,
                  Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w400),
            ),
            InkWell(
              onTap: () async {
                var res = await AppAlert.showNewConfirm(context, "ลบข้อมูลทะเบียน", "คุณต้องการจะลบข้อมูล\n$text\nใช่หรือไม่?", "ลบ", "ยกเลิก");
                if(res){
                  await appointmentCtl.removeCarReg(context, id);

                  setState(() {
                  });
                }
              },
              child: Image.asset("assets/image/service/appointment/Remove_light.png", width: 24, height: 24,),
            )
          ],
        )));
  }

  buildListCarReg() {
    return Expanded(
      child: ListView.builder(
        itemCount: appointmentCtl.carRegList.data!.length,
        itemBuilder: (context, index) {
          return buildCarRegContainer(
            "${appointmentCtl.carRegList.data![index].carLicense}",
            appointmentCtl.carRegList.data![index].running,
          );
        },
      ),
    );
  }

  buildTextField(widgetTextField){
    return Container(
      width: Get.width,
      height: 50,
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1,
          color: const Color(0xFFEDEDED),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      alignment: Alignment.center,
      child: widgetTextField,
    );
  }

  dropdownCounty(context, button, items, RxString value, showText) {
    return GestureDetector(
      onTap: () {
        button.value = true;
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (value.value != "") {
              selectTempIndex = items.indexOf(value.value);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          button.value = false;
                          Get.back();
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize:  Get.width < 500 ? 14 : 16,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกรายการ',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize:  Get.width < 500 ? 14 : 16,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          value.value = items[selectTempIndex];
                          button.value = false;
                          Get.back();
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize:  Get.width < 500 ? 14 : 16,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 300,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            fontSize:  Get.width < 500 ? 14 : 16,
                            color: const Color(0xFF282828),
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        ).then((value) {
          button.value = false;
        });
      },
      child: Container(
        width: Get.width,
        height: 50,
        padding: EdgeInsets.only(
            left: Get.width < 500 ? 18 : 54,
            right: Get.width < 500 ? 18 : 54,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            width: 1,
            color: const Color(0xFFEDEDED),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, -4),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            const SizedBox(
              width: 25,
            ),
            Obx(() => AppWidget.normalText(
                context,
                appointmentCtl.provinceSelect.value == "" ? showText : appointmentCtl.provinceSelect.value,
                Get.width < 500 ? 14 : 16,
                appointmentCtl.provinceSelect.value == "" ? const Color(0xFFBCBCBC) : const Color(0xFF282828),
                FontWeight.w400)),
            !button.value
                ? const Icon(Icons.arrow_drop_down_rounded, color: Color(0xFFFFC700), size: 25,)
                : const Icon(Icons.arrow_drop_up_rounded , color: Color(0xFFFFC700), size: 25,)

          ],
        ),
      ),
    );
  }

  checkSaveCar(){
    if(appointmentCtl.carSelect.value != "รถบรรทุก" &&
        appointmentCtl.carCharacterController.text == "" ||
        appointmentCtl.carNumberController.text == "" ||
        appointmentCtl.provinceSelect.value == ""){
      return;
    }
    if(appointmentCtl.carSelect.value == "รถบรรทุก" &&
        appointmentCtl.carNumberController.text == "" ||
        appointmentCtl.provinceSelect.value == ""){
      return;
    }
    appointmentCtl.createCarReg(context);
  }

  checkSaveData(){
    if(appointmentCtl.branchSelect.value == ""){
      return;
    }
    if(appointmentCtl.carRegSelect.value == ""){
      return;
    }
    if(appointmentCtl.timeSelect.value == ""){
      return;
    }
    appointmentCtl.showSaveButton.value = true;
  }
}




