import 'dart:async';
import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/county_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/sc_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_plus_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_pro_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_standard_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/recommend_success_MR.dart';

class RecommendMRPage extends StatefulWidget {
  const RecommendMRPage({Key? key}) : super(key: key);

  @override
  State<RecommendMRPage> createState() => _RecommendMRPageState();
}

class _RecommendMRPageState extends State<RecommendMRPage> {
  final TextEditingController _referralFullName = TextEditingController();
  final TextEditingController _referralPhone = TextEditingController();
  final TextEditingController _referralNote = TextEditingController();
  final TextEditingController _referralCarReg = TextEditingController();
  final TextEditingController _referralLocation = TextEditingController();

  RxString codePOI = "".obs;
  RxString rank = "".obs;
  RxDouble likePoint = 0.00.obs;

  List<String> referralList = [
    'เข้าใช้บริการ',
    'ออกรถใหม่',
    'ซื้ออะไหล่ ประดับยนต์ แม็คยาง',
    'งานศูนย์ซ่อมสีและตัวถัง',
    'ทะเบียนประกันภัย',
    'อู่พีเอ็มจี เซอร์วิส',
    'จัดหาสถานที่ตรวจเช็ครถ'
  ];
  String referralSelect = "";
  RxBool openReferral = false.obs;

  List<String> listCar = [
    'Mu-X',
    'X Series',
    'V-Cross',
    '4 Door',
    '2 Door',
    'Spark',
  ];

  RxString carSelect = "".obs;
  RxBool openCar = false.obs;

  String? bankNameMR;
  String? bookBankNameMR;
  String? bookBankNoMR;

  RxBool openProduct = false.obs;
  RxBool openAmphur = false.obs;
  RxBool openTumbol = false.obs;

  bool selectHeadOffice = false;
  bool selectNaYaiAm = false;
  bool selectKlung = false;
  bool selectSoiDao = false;

  int? selectSaleHeadOffice;
  int? selectSaleNaYaiAm;
  int? selectSaleKlung;
  int? selectSaleSoiDao;

  String? branch;
  String salesPerson = "";
  String? rewardType;
  String? amountLikepoint;
  String? amountFiat;

  String? rankMR;

  final formatter = NumberFormat('#,##0');

  RxBool openButton = false.obs;
  RxBool openText = false.obs;

  RxString productSelect = "".obs;
  RxString moreProductSelect = "".obs;
  RxInt pmgIndex = 0.obs;

  RxDouble scrollParam = 0.00.obs;

  final profileCtl = Get.put(ProfileController());
  final recommendMRCtl = Get.put(RecommendMRController());
  final scCtl = Get.put(SCController());
  final countyCtl = Get.put(CountyController());
  

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    analytics.setCurrentScreen(screenName: "RecommendMarketingRepresentative");
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Stack(
          children: <Widget>[
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFEDEDED),
                    Color(0xFFF2F2F2),
                  ],
                ),
              ),
            ),
            Container(
              width: Get.width,
              height: 125,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFFFFB100).withOpacity(1),
                    const Color(0xFFFFC700).withOpacity(0.7),
                    const Color(0xFFFFC700).withOpacity(0.4),
                    const Color(0xFFFFC700).withOpacity(0),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 40,
              right: -10,
              child: Image.asset(
                'assets/image/MR/LadyWhite.png',
                opacity: const AlwaysStoppedAnimation(.6),
                width: 137,
                height: 127,
              ),
            ),
            NotificationListener<ScrollNotification>(
              onNotification: (notificationInfo) {
                if (notificationInfo is ScrollUpdateNotification) {
                  scrollParam.value = notificationInfo.metrics.pixels;
                }
                return true;
              },
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: 120,
                    ),
                    buildContent(),
                    referralSelect == "ออกรถใหม่"
                        ? const SizedBox(
                      height: 50,
                    )
                        : const SizedBox(),
                    Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom)),
                  ],
                ),
              ),
            ),
            Obx(() => scrollParam.value >= 14
                ? ClipRRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: SizedBox(
                  width: Get.width,
                  height: 100,
                ),
              ),
            ):
            const SizedBox(),
            ),
            buildTopContent(),

          ],
        ),
      ),
    );
  }

  buildTopContent(){
    return Container(
      margin: EdgeInsets.only(left: Get.width < 600 ? 18 : 54,top: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          InkWell(
            onTap: () async {
              FocusNode focusNode = FocusNode();
              FocusScope.of(context).requestFocus(focusNode);
              var res = await showAlert();
              if(res){
                Timer(const Duration(milliseconds: 600), () {
                  if (profileCtl.rankMR.rankCurrent == "STANDARD") {
                    Get.to(() => const DetailStandardMRPage());
                  } else if (profileCtl.rankMR.rankCurrent == "PLUS") {
                    Get.to(() => const DetailPlusMRPage());
                  } else if (profileCtl.rankMR.rankCurrent == "PRO") {
                    Get.to(() => const DetailProMRPage());
                  }
                });
              }
              countyCtl.selectTumbol.value = "";
              countyCtl.selectAmphur.value = "";
              countyCtl.tumbols.value = [];
            },
            child: Container(
              height: 50,
              width: 50,
              alignment: Alignment.centerLeft,
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Color(0xFF282828),
                size: 16,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 27, left: Get.width < 500 ? 0 : 20,),
            height: 50,
            child: RichText(
              text: const TextSpan(children: [
                TextSpan(
                  text: 'กิจกรรมแนะนำลูกค้า',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt-Medium',
                    color: Color(0xFF282828),
                    fontSize: 14,
                  ),
                ),
                TextSpan(
                  text:
                  '\nกรุณาใส่รายละเอียดข้อมูลให้ครบถ้วน',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF664701),
                    fontSize: 12,
                  ),
                ),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  buildContent(){
    int value = likePoint.value.toInt(); // Convert to int
    return Container(
      height: referralSelect != "ออกรถใหม่" ? Get.height * 1 : null,
      margin: EdgeInsets.only(
        right: Get.width < 600 ? 18 : 54,
        left: Get.width < 600 ? 18 : 54,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: const TextSpan(children: [
              TextSpan(
                text: 'รายการแนะนำ',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF282828),
                  fontSize: 14,
                ),
              ),
            ]),
          ),
          Container(
              child: selectRecommend(
                  context, referralList)),
          const SizedBox(
            height: 15,
          ),
          referralSelect != ""
          ? RichText(
            text: const TextSpan(children: [
              TextSpan(
                text: 'ชื่อ - นามสกุล',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF282828),
                  fontSize: 14,
                ),
              ),
            ]),
          )
          : const SizedBox(),
          referralSelect != ""
          ? TextField(
            onChanged: showButton(),
            controller: _referralFullName,
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                AppService.thaiAZ(),
              ),
              FilteringTextInputFormatter.deny(AppService.deny()),
            ],
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: Color(0xFF664701),
              fontSize: 14,
            ),
            decoration: InputDecoration(
              hintText: 'พิมพ์ชื่อ-นามสกุล',
              hintStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: const Color(0xFF707070).withOpacity(0.5),
                fontSize: 14,
              ),
              enabledBorder: UnderlineInputBorder(
                borderSide:
                BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: BorderSide(color: Color(0xFF895F00)),
              ),
            ),
          )
          : const SizedBox(),
          const SizedBox(
            height: 15,
          ),
          referralSelect != ""
          ? RichText(
            text: const TextSpan(children: [
              TextSpan(
                text: 'เบอร์โทรศัพท์',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF282828),
                  fontSize: 14,
                ),
              ),
            ]),
          )
          : const SizedBox(),
          referralSelect != ""
          ? TextField(
            controller: _referralPhone,
            onChanged: showButton(),
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                AppService.number10RegExp(),
              ),
            ],
            keyboardType: TextInputType.number,
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: Color(0xFF664701),
              fontSize: 14,
            ),
            decoration: InputDecoration(
              hintText: 'กรอกหมายเลขโทรศัพท์',
              hintStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: const Color(0xFF707070).withOpacity(0.5),
                fontSize: 14,
              ),
              enabledBorder: UnderlineInputBorder(
                borderSide:
                BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
              ),
              focusedBorder: const UnderlineInputBorder(
                borderSide: BorderSide(color: Color(0xFF895F00)),
              ),
            ),
          )
          : const SizedBox(),
          const SizedBox(
            height: 15,
          ),
          referralSelect != ""
          ? showDetailRecommend(referralSelect)
          : const SizedBox(),
          Obx(() => openText.value
              ? referralSelect == "ออกรถใหม่"
              ? RichText(
            text: const TextSpan(children: [
              TextSpan(
                text: 'รางวัลที่คุณจะได้รับ :',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF282828),
                  fontSize: 14,
                ),
              ),
            ]),
          )
          : Padding(
            padding: EdgeInsets.zero,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('PMSpoint ที่คุณจะได้รับคือ :',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF282828),
                    fontSize: 14,
                  ),),
                const SizedBox(
                  height: 5,
                ),
                Text('${formatter.format(likePoint.value)} LIKE',
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF895F00),
                    fontSize: 14,
                  ),)
              ],
            ),
          )
              : const SizedBox()),
          const SizedBox(
            height: 5,
          ),
          Obx(() => openText.value && referralSelect == "ออกรถใหม่"
              ? RichText(
            text: const TextSpan(children: [
              TextSpan(
                text: 'เงินสด 3,000 บาท',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF895F00),
                  fontSize: 14,
                ),
              ),
            ]),
          )
              : const SizedBox()),
          const SizedBox(
            height: 12
          ),
          Obx(() => openText.value && referralSelect == "ออกรถใหม่"
              ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('โอนเข้าบัญชี :',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF282828),
                    fontSize: 14,
                  ),),
              const SizedBox(
                height: 5,
              ),
              Text('${profileCtl.profile.value.bankNameMR} ${profileCtl.profile.value.bookBankNoMR.toString()} ${profileCtl.profile.value.bookBankNameMR}',
                style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF895F00),
                  fontSize: 14,
                ),),
            ],
          )
              : const SizedBox()),
          referralSelect != "ออกรถใหม่" && referralSelect != "อู่พีเอ็มจี เซอร์วิส"
          ? const SizedBox(
              height: 20
          )
          :  const SizedBox(
            height: 30
            ),
          referralSelect != ""
          ? referralSelect != "ออกรถใหม่"
              ? Align(
                  alignment: Alignment.bottomCenter,
                  child: InkWell(
                    onTap: () {
                      if(_referralPhone.text.length != 10){
                        Get.snackbar(
                          'หมายเลขโทรศัพท์ไม่ถูกต้อง',
                          'กรุณาตรวจสอบหมายเลขโทรศัพท์',
                          snackPosition: SnackPosition.TOP,
                          duration: const Duration(seconds: 5),
                        );
                        return;
                      }
                      saveData();
                    },
                    child: Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        width: Get.width,
                        height: 52,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            openButton.value ? BoxShadow(
                              color: Colors.black45.withOpacity(0.25),
                              offset: const Offset(0, 3),
                              blurRadius: 5,
                            ) : BoxShadow(
                              color: Colors.black45.withOpacity(0.25),
                            )
                          ],
                          color: openButton.value ? const Color(0xFF282828) : const Color(0xFF282828).withOpacity(0),
                        ),
                        child: Center(
                          child: Text(
                            'ยืนยันข้อมูล',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: openButton.value ? const Color(0xFFFFFFFF) : const Color(0xFFFFFFFF).withOpacity(0.25),
                              fontSize: 14,
                            ),
                          ),
                        )),
                  )
              )
              : Align(
              alignment: Alignment.bottomCenter,
              child: InkWell(
                onTap: () {
                  if(_referralPhone.text.length != 10){
                    Get.snackbar(
                      'หมายเลขโทรศัพท์ไม่ถูกต้อง',
                      'กรุณาตรวจสอบหมายเลขโทรศัพท์',
                      snackPosition: SnackPosition.TOP,
                      duration: const Duration(seconds: 5),
                    );
                    return;
                  }
                  saveData();
                },
                child: Container(
                    margin: openButton.value ? const EdgeInsets.only(top: 0) : const EdgeInsets.only(top: 56),
                    width: Get.width,
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        openButton.value ? BoxShadow(
                          color: Colors.black45.withOpacity(0.25),
                          offset: const Offset(0, 3),
                          blurRadius: 5,
                        ) : BoxShadow(
                          color: Colors.black45.withOpacity(0.25),
                        )
                      ],
                      color: openButton.value ? const Color(0xFF282828) : const Color(0xFF282828).withOpacity(0),
                    ),
                    child: Center(
                      child: Text(
                        'ยืนยันข้อมูล',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt-Medium',
                          color: openButton.value ? const Color(0xFFFFFFFF) : const Color(0xFFFFFFFF).withOpacity(0.25),
                          fontSize: 14,
                        ),
                      ),
                    )),
              )
          )
         : const SizedBox()
        ],
      ),
    );
  }

  ///โชว์ข้อมูล Recommend
  showDetailRecommend(referralSelect) {
    switch (referralSelect) {
      case 'ออกรถใหม่':
        return buildRecommendNewCar();
      case 'เข้าใช้บริการ':
        return buildRecommendFixCar();
      case 'ซื้ออะไหล่ ประดับยนต์ แม็คยาง':
        return buildRecommendBuyProduct();
      case 'งานศูนย์ซ่อมสีและตัวถัง':
        return buildRecommendPMG();
      case 'ทะเบียนประกันภัย':
        return buildRecommendInsurance();
      case 'อู่พีเอ็มจี เซอร์วิส':
        return buildRecommendPMGService();
      case 'จัดหาสถานที่ตรวจเช็ครถ':
        return buildRecommendCheckCar();
    }
  }
  ///โชว์ข้อมูล Recommend

  ///โชว์ข้อมูล RecommendDetail
  buildRecommendFixCar() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ทะเบียนรถ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Colors.black,
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' ตย. กบ1235 จันทบุรี',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        TextField(
          controller: _referralCarReg,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              AppService.thaiNumber30RegExp(),
            ),
            FilteringTextInputFormatter.deny(AppService.deny()),
          ],
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF664701),
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: 'กรอกหมายเลขทะเบียนรถ',
            hintStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: const Color(0xFF707070).withOpacity(0.5),
              fontSize: 14,
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide:
              BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF895F00)),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ระบุเพิ่มเติม',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' รายละเอียดงานซ่อม',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        TextField(
          onChanged: showButton(),
          controller: _referralNote,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              AppService.thaiAZRegExp(),
            ),
          ],
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF664701),
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: 'เช่น ตรวจเช็คระยะ, พ่นกันสนิม เป็นต้น',
            hintStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: const Color(0xFF707070).withOpacity(0.5),
              fontSize: 14,
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide:
              BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF895F00)),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
      ],
    );
  }

  buildRecommendNewCar() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'รุ่นรถ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Colors.black,
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
          child: dropdown(context, listCar, carSelect, "เลือกรุ่นรถที่สนใจ"),
        ),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ระบุเพิ่มเติม',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' รายละเอียดความต้องการที่สนใจ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        TextField(
          controller: _referralNote,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              AppService.thaiAZRegExp2(),
            ),
          ],
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF664701),
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: 'เช่น แคปตอนเดียว ตัวต่ำ ดาวน์เริ่มต้นเท่าไหร่',
            hintStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: const Color(0xFF707070).withOpacity(0.5),
              fontSize: 14,
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide:
                  BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF895F00)),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ที่ปรึกษาการขาย',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' กรุณาเลือกสาขาที่ต้องการ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        const SizedBox(
          height: 12,
        ),
        Row(
          children: [
            InkWell(
              onTap: () {
                setState(() {
                  selectHeadOffice = true;
                  selectSoiDao = false;
                  selectKlung = false;
                  selectNaYaiAm = false;
                  branch = "สำนักงานใหญ่";
                });
              },
              child: selectHeadOffice != true
                  ? Container(
                      width: 96,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2,),

                decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 0.5,
                            color: const Color(0xFF895F00),
                          ),
                          color: Colors.transparent),
                      child: const Text(
                        'สำนักงานใหญ่',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF664701),
                          fontSize: 12,
                        ),
                      ),
                    )
                  : Container(
                      width: 96,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2,),

                decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          width: 0.5,
                          color: const Color(0xFF282828),
                        ),
                        color: const Color(0xFF282828),
                      ),
                      child: const Text(
                        'สำนักงานใหญ่',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt-Medium',
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                        ),
                      ),
                    ),
            ),
            const SizedBox(
              width: 6,
            ),
            InkWell(
              onTap: () {
                setState(() {
                  selectHeadOffice = false;
                  selectSoiDao = false;
                  selectKlung = false;
                  selectNaYaiAm = true;
                  branch = "นายายอาม";
                });
              },
              child: selectNaYaiAm != true
                  ? Container(
                      width: 72,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2,),
                decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 0.5,
                            color: const Color(0xFF895F00),
                          ),
                          color: Colors.transparent),
                      child: const Text(
                        'นายายอาม',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF664701),
                          fontSize: 12,
                        ),
                      ),
                    )
                  : Container(
                      width: 72,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2,),

                decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          width: 0.5,
                          color: const Color(0xFF282828),
                        ),
                        color: const Color(0xFF282828),
                      ),
                      child: const Text(
                        'นายายอาม',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt-Medium',
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                        ),
                      ),
                    ),
            ),
            const SizedBox(
              width: 6,
            ),
            InkWell(
              onTap: () {
                setState(() {
                  selectHeadOffice = false;
                  selectSoiDao = true;
                  selectKlung = false;
                  selectNaYaiAm = false;
                  branch = "สอยดาว";
                });
              },
              child: selectSoiDao != true
                  ? Container(
                      width: 62,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2),

                decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 0.5,
                            color: const Color(0xFF895F00),
                          ),
                          color: Colors.transparent),
                      child: const Text(
                        'สอยดาว',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF664701),
                          fontSize: 12,
                        ),
                      ),
                    )
                  : Container(
                      width: 62,
                height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          width: 0.5,
                          color: const Color(0xFF282828),
                        ),
                        color: const Color(0xFF282828),
                      ),
                      child: const Text(
                        'สอยดาว',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt-Medium',
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                        ),
                      ),
                    ),
            ),
            const SizedBox(
              width: 6,
            ),
            InkWell(
              onTap: () {
                setState(() {
                  selectHeadOffice = false;
                  selectSoiDao = false;
                  selectKlung = true;
                  selectNaYaiAm = false;
                  branch = "ขลุง";
                });
              },
              child: selectKlung != true
                  ? Container(
                      alignment: Alignment.center,
                      width: 48,
                      height: 30,
                padding: const EdgeInsets.only(bottom: 2),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            width: 0.5,
                            color: const Color(0xFF895F00),
                          ),
                          color: Colors.transparent),
                      child: const Text(
                        'ขลุง',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF664701),
                          fontSize: 12,
                        ),
                      ),
                    )
                  : Container(
                      width: 48,
                      height: 30,
                alignment: Alignment.center,
                padding: const EdgeInsets.only(bottom: 2),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          width: 0.5,
                          color: const Color(0xFF282828),
                        ),
                        color: const Color(0xFF282828),
                      ),
                      child: const Text(
                        'ขลุง',
                        style: TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt-Medium',
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                        ),
                      ),
                    ),
            ),
          ],
        ),
        const SizedBox(
          height: 15,
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: selectBrach(
              selectHeadOffice, selectNaYaiAm, selectKlung, selectSoiDao),
        ),
        Divider(
          thickness: 1,
          indent: 0,
          endIndent: 0,
          color: const Color(0xFF895F00).withOpacity(0.5),
        ),
      ],
    );
  }

  buildRecommendBuyProduct() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
            child: dropdown(context, recommendMRCtl.resAccessory.productList,
                productSelect, "เลือกผลิตภัณฑ์ที่ต้องการ")),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ระบุเพิ่มเติม',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' รายละเอียดผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        TextField(
          onChanged: showButton(),
          controller: _referralNote,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              AppService.thaiAZRegExp(),
            ),
          ],
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF664701),
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText: 'เช่น สนใจซื้อไฟหน้ารุ่นยกสูง, สนใจชุดแต่ง เป็นต้น',
            hintStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: const Color(0xFF707070).withOpacity(0.5),
              fontSize: 14,
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide:
                  BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF895F00)),
            ),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  buildRecommendPMG() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
          child: dropdown(context, recommendMRCtl.resPMG.productList,
              productSelect, "เลือกผลิตภัณฑ์ที่ต้องการ"),
        ),
        const SizedBox(
          height: 15,
        ),
        Obx(() => productSelect.value != ""
          ? RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ระบุเพิ่มเติม',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' รายละเอียดผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        )
          : const SizedBox(
        ),),
        Obx(() {
          if (productSelect.value == "งานซ่อมทำสี") {
            return dropdown(context, recommendMRCtl.resPMG.colorList,
                moreProductSelect, "เลือกรายการผลิตภัณฑ์ที่ต้องการ");
          } else if (productSelect.value == "เคลือบแก้ว") {
            return dropdown(context, recommendMRCtl.resPMG.glassCoatingList,
                moreProductSelect, "เลือกรายการผลิตภัณฑ์ที่ต้องการ");
          } else if (productSelect.value == "ผลิตภัณฑ์เสริม") {
            return dropdown(context, recommendMRCtl.resPMG.accessoryProductList,
                moreProductSelect, "เลือกรายการผลิตภัณฑ์ที่ต้องการ");
          } else {
            return const SizedBox();
          }
        }),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  buildRecommendInsurance() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
          child: dropdown(context, recommendMRCtl.resInsurance.productList,
              productSelect, "เลือกผลิตภัณฑ์ที่ต้องการ"),
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }

  buildRecommendPMGService() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ผลิตภัณฑ์',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
          child: dropdown(context, recommendMRCtl.resPMGService.productList,
              productSelect, "เลือกผลิตภัณฑ์ที่ต้องการ"),
        ),
        const SizedBox(
          height: 15,
        ),
        productSelect.value != ""
            ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(() => productSelect.value == "แนะนำเข้าใช้บริการ"
                ? RichText(
              text: const TextSpan(children: [
                TextSpan(
                  text: 'ทะเบียนรถ',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Colors.black,
                    fontSize: 14,
                  ),
                ),
                TextSpan(
                  text: ' ตย. กบ1235 จันทบุรี',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF895F00),
                    fontSize: 14,
                  ),
                ),
              ]),
            )
                : const SizedBox()),
            Obx(() => productSelect.value == "แนะนำเข้าใช้บริการ"
                ? TextField(
              controller: _referralCarReg,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  AppService.thaiNumber30RegExp(),
                ),
                FilteringTextInputFormatter.deny(AppService.deny()),
              ],
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF664701),
                fontSize: 14,
              ),
              decoration: InputDecoration(
                hintText: 'กรอกหมายเลขทะเบียนรถ',
                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF707070).withOpacity(0.5),
                  fontSize: 14,
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(
                      color:
                      const Color(0xFF895F00).withOpacity(0.5)),
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Color(0xFF895F00)),
                ),
              ),
            )
                : const SizedBox()),
            Obx(() => productSelect.value == "แนะนำเข้าใช้บริการ"
                ? const SizedBox(
              height: 15,
            )
                : const SizedBox()),
            RichText(
              text: const TextSpan(children: [
                TextSpan(
                  text: 'ระบุเพิ่มเติม',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF282828),
                    fontSize: 14,
                  ),
                ),
                TextSpan(
                  text: ' รายละเอียดความต้องการที่สนใจ',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF895F00),
                    fontSize: 14,
                  ),
                ),
              ]),
            ),
            TextField(
              onChanged: showButton(),
              controller: _referralNote,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  AppService.thaiAZRegExp2(),
                ),
              ],
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF664701),
                fontSize: 14,
              ),
              decoration: InputDecoration(
                hintText: productSelect.value == "แนะนำเข้าใช้บริการ"
                    ? 'เช่น ตรวจเช็ครถ, เปลี่ยนน้ำมันเครื่อง เป็นต้น'
                    : 'เช่น สนใจซื้อไฟหน้าดีแม็คซ์ปี65, ที่กรองอากาศ เป็นต้น',
                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF707070).withOpacity(0.5),
                  fontSize: 14,
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(
                      color: const Color(0xFF895F00).withOpacity(0.5)),
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(color: Color(0xFF895F00)),
                ),
              ),
            ),
            const SizedBox(
              height: 12,
            ),
          ],
        )
            : const SizedBox()
      ],
    );
  }

  buildRecommendCheckCar() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'สถานที่แนะนำ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        TextField(
          controller: _referralLocation,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              AppService.thaiAZ100RegExp(),
            ),
          ],
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF664701),
            fontSize: 14,
          ),
          decoration: InputDecoration(
            hintText:
                'กรอกรายละเอียด เช่น ตลาดเจริญสุข ร้านกาแฟดอยจันท์ เป็นต้น',
            hintStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontFamily: 'Prompt',
              color: const Color(0xFF707070).withOpacity(0.5),
              fontSize: 14,
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide:
                  BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF895F00)),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'อำเภอ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' สถานที่แนะนำ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
          child: dropdownCounty(context, openAmphur, countyCtl.amphurs, countyCtl.selectAmphur, "เลือกอำเภอ")
        ),
        const SizedBox(
          height: 15,
        ),
        RichText(
          text: const TextSpan(children: [
            TextSpan(
              text: 'ตำบล',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF282828),
                fontSize: 14,
              ),
            ),
            TextSpan(
              text: ' สถานที่แนะนำ',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt',
                color: Color(0xFF895F00),
                fontSize: 14,
              ),
            ),
          ]),
        ),
        Container(
            child: dropdownTumbol(context, openTumbol, countyCtl.tumbols, countyCtl.selectTumbol, "เลือกตำบล")
        ),
        const SizedBox(
          height: 12,
        ),
      ],
    );
  }
  ///โชว์ข้อมูล RecommendDetail

  ///แสดงข้อมูล SC
  buildSCHeadOffice(context) {
    try {
      List<Widget> list = [];
      for (int i = 0; i < scCtl.resSCHeadOffice.data!.length; i++) {
        list.add(InkWell(
          onTap: () {
            setState(() {
              selectSaleHeadOffice = i;
              selectSaleNaYaiAm = null;
              selectSaleSoiDao = null;
              selectSaleKlung = null;
              salesPerson = scCtl.resSCHeadOffice.data![i].saleName!;
              showButton();

            });
          },
          child: Container(
            margin: const EdgeInsets.only(right: 5, bottom: 15),
            height: 85,
            width: 64,
            child: Stack(
              children: [
                Center(
                  child: SizedBox(
                    width: 62,
                    height: 85,
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: const EdgeInsets.only(top: 5),
                        width: 60,
                        height: 85,
                        decoration: BoxDecoration(
                          gradient: selectSaleHeadOffice != i
                              ? const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFEDEDED),
                              Color(0xFFF2F2F2),
                            ],
                          )
                              :
                          const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFFFB100),
                              Color(0xFFFFB100),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.05),
                              offset: const Offset(0, 4),
                              blurRadius: 10.0,
                            ),
                          ],
                          borderRadius: const BorderRadius.all(Radius.circular(25)),
                        ),
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 5),
                            child: Text(
                              scCtl.resSCHeadOffice.data![i].saleNickname
                                  .toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt-Medium',
                                color: selectSaleHeadOffice != i
                                    ? const Color(0xFF895F00)
                                    : const Color(0xFFFFFFFF),
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    width: 62.5,
                    height: 62.5,
                    decoration: BoxDecoration(
                        gradient: selectSaleHeadOffice != i
                            ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            Color(0xFFE8E6E2),
                            Color(0xFFD9D8D5),
                          ],
                        )
                            : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            const Color(0xFFFFC700),
                            const Color(0xFFFFB100).withOpacity(0.8),
                            const Color(0xFFFF9900).withOpacity(0.8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.1),
                            offset: const Offset(0, 1),
                            blurRadius: 1.0,
                          ),
                        ],
                        borderRadius: BorderRadius.circular(50)),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CircleAvatar(
                        backgroundColor: Colors.transparent,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(50),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFA9A9A9),
                            ),
                            child: scCtl.resSCHeadOffice.data![i].salePicture != null
                              ?
                            Image.network(
                              scCtl.resSCHeadOffice.data![i].salePicture.toString(),
                              fit: BoxFit.cover,
                              width: 57,
                              height: 57,
                            )
                                : Image.asset('assets/image/mascot2.png'),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
      }
      return Row(
        children: list,
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
  buildSCNaYaiAm(context) {
    try {
      List<Widget> list = [];
      for (int i = 0; i < scCtl.resSCNaYaiAm.data!.length; i++) {
        list.add(InkWell(
          onTap: () {
            setState(() {
              selectSaleNaYaiAm = i;
              selectSaleHeadOffice = null;
              selectSaleSoiDao = null;
              selectSaleKlung = null;
              salesPerson = scCtl.resSCNaYaiAm.data![i].saleName!;
              showButton();

            });
          },
          child: Container(
            margin: const EdgeInsets.only(right: 5, bottom: 15),
            height: 85,
            width: 64,
            child: Stack(
              children: [
                Center(
                  child: SizedBox(
                    width: 62,
                    height: 85,
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: const EdgeInsets.only(top: 5),
                        width: 60,
                        height: 85,
                        decoration: BoxDecoration(
                          gradient: selectSaleNaYaiAm != i
                              ? const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFEDEDED),
                              Color(0xFFF2F2F2),
                            ],
                          )
                              :
                          const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFFFB100),
                              Color(0xFFFFB100),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.05),
                              offset: const Offset(0, 4),
                              blurRadius: 10.0,
                            ),
                          ],
                          borderRadius: const BorderRadius.all(Radius.circular(25)),
                        ),
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 5),
                            child: Text(
                              scCtl.resSCNaYaiAm.data![i].saleNickname
                                  .toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt-Medium',
                                color: selectSaleNaYaiAm != i
                                    ? const Color(0xFF895F00)
                                    : const Color(0xFFFFFFFF),
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    width: 62.5,
                    height: 62.5,
                    decoration: BoxDecoration(
                        gradient: selectSaleNaYaiAm != i
                            ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            Color(0xFFE8E6E2),
                            Color(0xFFD9D8D5),
                          ],
                        )
                            : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            const Color(0xFFFFC700),
                            const Color(0xFFFFB100).withOpacity(0.8),
                            const Color(0xFFFF9900).withOpacity(0.8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.1),
                            offset: const Offset(0, 1),
                            blurRadius: 1.0,
                          ),
                        ],
                        borderRadius: BorderRadius.circular(50)),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CircleAvatar(
                        backgroundColor: Colors.transparent,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(50),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFA9A9A9),
                            ),
                            child: scCtl.resSCNaYaiAm.data![i].salePicture != ""
                            ? Image.network(
                              scCtl.resSCNaYaiAm.data![i].salePicture
                                  .toString(),
                              fit: BoxFit.cover,
                              width: 57,
                              height: 57,
                            )
                                : Image.asset('assets/image/mascot2.png',
                              fit: BoxFit.cover,
                              width: 57,
                              height: 57,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
      }
      return Row(
        children: list,
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
  buildSCKlung(context) {
    try {
      List<Widget> list = [];
      for (int i = 0; i < scCtl.resSCKlung.data!.length; i++) {
        list.add(InkWell(
          onTap: () {
            setState(() {
              selectSaleKlung = i;
              selectSaleNaYaiAm = null;
              selectSaleSoiDao = null;
              selectSaleHeadOffice = null;
              salesPerson = scCtl.resSCKlung.data![i].saleName!;
              showButton();

            });
          },
          child: Container(
            margin: const EdgeInsets.only(right: 5, bottom: 15),
            height: 85,
            width: 64,
            child: Stack(
              children: [
                Center(
                  child: SizedBox(
                    width: 62,
                    height: 85,
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: const EdgeInsets.only(top: 5),
                        width: 60,
                        height: 85,
                        decoration: BoxDecoration(
                          gradient: selectSaleKlung != i
                              ? const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFEDEDED),
                              Color(0xFFF2F2F2),
                            ],
                          )
                              :
                          const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFFFB100),
                              Color(0xFFFFB100),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.05),
                              offset: const Offset(0, 4),
                              blurRadius: 10.0,
                            ),
                          ],
                          borderRadius: const BorderRadius.all(Radius.circular(25)),
                        ),
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 5),
                            child: Text(
                              scCtl.resSCKlung.data![i].saleNickname.toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt-Medium',
                                color: selectSaleKlung != i
                                    ? const Color(0xFF895F00)
                                    : const Color(0xFFFFFFFF),
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    width: 62.5,
                    height: 62.5,
                    decoration: BoxDecoration(
                        gradient: selectSaleKlung != i
                            ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            Color(0xFFE8E6E2),
                            Color(0xFFD9D8D5),
                          ],
                        )
                            : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            const Color(0xFFFFC700),
                            const Color(0xFFFFB100).withOpacity(0.8),
                            const Color(0xFFFF9900).withOpacity(0.8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.1),
                            offset: const Offset(0, 1),
                            blurRadius: 1.0,
                          ),
                        ],
                        borderRadius: BorderRadius.circular(50)),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CircleAvatar(
                        backgroundColor: Colors.transparent,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(50),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFA9A9A9),
                            ),
                            child: Image.network(
                              scCtl.resSCKlung.data![i].salePicture.toString(),
                              fit: BoxFit.cover,
                              width: 57,
                              height: 57,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
      }
      return Row(
        children: list,
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
  buildSCSoiDao(context) {
    try {
      List<Widget> list = [];
      for (int i = 0; i < scCtl.resSCSoiDao.data!.length; i++) {
        list.add(InkWell(
          onTap: () {
            setState(() {
              selectSaleSoiDao = i;
              selectSaleNaYaiAm = null;
              selectSaleHeadOffice = null;
              selectSaleKlung = null;
              salesPerson = scCtl.resSCSoiDao.data![i].saleName!;
              showButton();

            });
          },
          child: Container(
            margin: const EdgeInsets.only(right: 5, bottom: 15),
            height: 85,
            width: 64,
            child: Stack(
              children: [
                Center(
                  child: SizedBox(
                    width: 62,
                    height: 85,
                    child: Align(
                      alignment: Alignment.center,
                      child: Container(
                        margin: const EdgeInsets.only(top: 5),
                        width: 60,
                        height: 85,
                        decoration: BoxDecoration(
                          gradient: selectSaleSoiDao != i
                              ? const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFEDEDED),
                              Color(0xFFF2F2F2),
                            ],
                          )
                              :
                          const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: <Color>[
                              Color(0xFFFFB100),
                              Color(0xFFFFB100),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.05),
                              offset: const Offset(0, 4),
                              blurRadius: 10.0,
                            ),
                          ],
                          borderRadius: const BorderRadius.all(Radius.circular(25)),
                        ),
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 5),
                            child: Text(
                              scCtl.resSCSoiDao.data![i].saleNickname
                                  .toString(),
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt-Medium',
                                color: selectSaleSoiDao != i
                                    ? const Color(0xFF895F00)
                                    : const Color(0xFFFFFFFF),
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: Container(
                    width: 62.5,
                    height: 62.5,
                    decoration: BoxDecoration(
                        gradient: selectSaleSoiDao != i
                            ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            Color(0xFFE8E6E2),
                            Color(0xFFD9D8D5),
                          ],
                        )
                            : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: <Color>[
                            const Color(0xFFFFC700),
                            const Color(0xFFFFB100).withOpacity(0.8),
                            const Color(0xFFFF9900).withOpacity(0.8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF000000).withOpacity(0.1),
                            offset: const Offset(0, 1),
                            blurRadius: 1.0,
                          ),
                        ],
                        borderRadius: BorderRadius.circular(50)),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CircleAvatar(
                        backgroundColor: Colors.transparent,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(50),
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFA9A9A9),
                            ),
                            child: Image.network(
                              scCtl.resSCSoiDao.data![i].salePicture.toString(),
                              fit: BoxFit.cover,
                              width: 57,
                              height: 57,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
      }
      return Row(
        children: list,
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
  ///แสดงข้อมูล SC

  ///เลือกสาขา sale
  selectBrach(selectHeadOffice, selectNaYaiAm, selectKlung, selectSoiDao) {
    if (selectHeadOffice == true) {
      return buildSCHeadOffice(context);
    } else if (selectKlung == true) {
      return buildSCKlung(context);
    } else if (selectSoiDao == true) {
      return buildSCSoiDao(context);
    } else if (selectNaYaiAm == true) {
      return buildSCNaYaiAm(context);
    } else {
      return Container();
    }
  }
  ///เลือกสาขา sale

  ///ดรอปดาวน์
  selectRecommend(context, items) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        openReferral.value = true;
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (referralSelect != "") {
              selectTempIndex = items.indexOf(referralSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          openReferral.value = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'รายการแนะนำ',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          openReferral.value = false;
                          openButton.value = false;
                          openText.value = false;
                          productSelect.value = "";
                          moreProductSelect.value = "";
                          carSelect.value = "";
                          countyCtl.selectAmphur.value = "";
                          countyCtl.selectTumbol.value = "";
                          setState(() {
                            referralSelect = items[selectTempIndex];
                            _referralCarReg.text = "";
                            _referralNote.text = "";
                            _referralLocation.text = "";
                            selectSaleHeadOffice = null;
                            selectSaleNaYaiAm = null;
                            selectSaleKlung = null;
                            selectSaleSoiDao = null;
                            selectKlung = false;
                            selectHeadOffice = false;
                            selectNaYaiAm = false;
                            selectSoiDao = false;
                            salesPerson = "";
                          });
                          setPOI();
                          Get.back();
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(
                      items.length,
                          (index) {
                        return Center(
                          child: Text(
                            items[index],
                            style: TextStyle(
                              fontFamily: 'Prompt',
                              fontSize: 14,
                              color: const Color(0xFF282828),
                              letterSpacing: 0.1,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 1.0,
                                  color:
                                  const Color(0xFF000000).withOpacity(0.15),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                )
              ],
            );
          },
        ).then((value) {
          openReferral.value = false;
        });
      },
      child:  Container(
        // margin: EdgeInsets.only(left: Get.width < 600 ? 18 : 54, right: 18),
        width: Get.width,
        padding: const EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            referralSelect == ""
                ? SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'เลือกรายการ',
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 14,
                    ),
                  ),
                  Obx(() => openReferral.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            )
                : SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: referralSelect,
                        style: const TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF664701),
                          fontSize: 14,
                        ),
                      ),
                    ]),
                  ),
                  Obx(() => openReferral.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  dropdown(context, items, RxString value, showText) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        openProduct.value = true;
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (value.value != "") {
              selectTempIndex = items.indexOf(value.value);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          openProduct.value = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'เลือกรายการ',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          moreProductSelect.value = "";
                          value.value = items[selectTempIndex];
                          setPOI();
                          showButton();
                          openProduct.value = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: const Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.1,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            fontSize: 14,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.1,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        ).then((value) {
          openProduct.value = false;
        });
      },
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            value.value == ""
                ? SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showText,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 14,
                    ),
                  ),
                  Obx(() => openProduct.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            )
                : SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 280,
                    child: RichText(
                      text: TextSpan(children: [
                        TextSpan(
                          text: value.value,
                          style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: Color(0xFF664701),
                            fontSize: 14,
                          ),
                        ),
                      ]),
                    ),
                  ),
                  Obx(() => openProduct.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  dropdownCounty(context, button, items, RxString value, showText) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        button.value = true;
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (value.value != "") {
              selectTempIndex = items.indexOf(value.value);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          button.value = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกรายการ',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          value.value = items[selectTempIndex];
                          countyCtl.getTumbol();
                          countyCtl.selectTumbol.value = "";
                          button.value = false;
                          showButton();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            fontSize: 14,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        ).then((value) {
          button.value = false;
        });
      },
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Obx(() => value.value == ""
                ? SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showText,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 14,
                    ),
                  ),
                  Obx(() => button.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            )
                : SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 280,
                    child: RichText(
                      text: TextSpan(children: [
                        TextSpan(
                          text: value.value,
                          style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: Color(0xFF664701),
                            fontSize: 14,
                          ),
                        ),
                      ]),
                    ),
                  ),
                  Obx(() => button.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            ),)
          ],
        ),
      ),
    );
  }
  dropdownTumbol(context, button, items, RxString value, showText) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        button.value = true;
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (value.value != "") {
              selectTempIndex = items.indexOf(value.value);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          button.value = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกรายการ',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          value.value = items[selectTempIndex];
                          button.value = false;
                          showButton();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: const Color(0xFF282828),
                            fontSize: 14,
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            fontSize: 14,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.4,
                            shadows: <Shadow>[
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 1.0,
                                color:
                                const Color(0xFF000000).withOpacity(0.15),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        ).then((value) {
          button.value = false;
        });
      },
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Obx(() => value.value == ""
                ? SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showText,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 14,
                    ),
                  ),
                  Obx(() => button.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            )
                : SizedBox(
              width: Get.width < 600 ? Get.width  * 0.9 :  Get.width  * 0.88,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 280,
                    child: RichText(
                      text: TextSpan(children: [
                        TextSpan(
                          text: value.value,
                          style: const TextStyle(
                            overflow: TextOverflow.ellipsis,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: Color(0xFF664701),
                            fontSize: 14,
                          ),
                        ),
                      ]),
                    ),
                  ),
                  Obx(() => button.value == false
                      ? const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Color(0xFF895F00),
                  )
                      : const Icon(
                    Icons.keyboard_arrow_up,
                    size: 16,
                    color: Color(0xFF895F00),
                  ))
                ],
              ),
            ),)
          ],
        ),
      ),
    );
  }
  ///ดรอปดาวน์

  ///โชว์ปุ่มกดดำเนินการต่อ
  showButton() {
      switch (referralSelect) {
        case "เข้าใช้บริการ":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              _referralCarReg.text != "" &&
              _referralNote.text != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
        case "ออกรถใหม่":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              carSelect.value != "" &&
              _referralNote.text != "" &&
              salesPerson != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
        case "ซื้ออะไหล่ ประดับยนต์ แม็คยาง":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              productSelect.value != "" &&
              _referralNote.text != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
        case "งานศูนย์ซ่อมสีและตัวถัง":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              productSelect.value != "" &&
              moreProductSelect.value != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
        case "ทะเบียนประกันภัย":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              productSelect.value != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
        case "อู่พีเอ็มจี เซอร์วิส":
          if(productSelect.value == "แนะนำเข้าใช้บริการ"){
            if (_referralFullName.text != "" &&
                _referralPhone.text != "" &&
                productSelect.value != "" &&
                _referralCarReg.text != "" &&
                _referralNote.text != "") {
              openButton.value = true;
              openText.value = true;
            }
          }
          if(productSelect.value == "ซื้ออะไหล่ / ประดับยนต์"){
            if (_referralFullName.text != "" &&
                _referralPhone.text != "" &&
                productSelect.value != "" &&
                _referralNote.text != "") {
              openButton.value = true;
              openText.value = true;
            }
          }
          break;
        case "จัดหาสถานที่ตรวจเช็ครถ":
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              _referralLocation.text != "" &&
              countyCtl.selectAmphur.value != "" &&
              countyCtl.selectTumbol.value != "") {
            openButton.value = true;
            openText.value = true;
          }
          break;
      }
  }
  showAlert() async {
    switch (referralSelect) {
      case "เข้าใช้บริการ":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            _referralCarReg.text != "" &&
            _referralNote.text != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
      case "ออกรถใหม่":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            carSelect.value != "" &&
            _referralNote.text != "" &&
            salesPerson != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
      case "ซื้ออะไหล่ ประดับยนต์ แม็คยาง":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            productSelect.value != "" &&
            _referralNote.text != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
      case "งานศูนย์ซ่อมสีและตัวถัง":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            productSelect.value != "" &&
            moreProductSelect.value != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
      case "ทะเบียนประกันภัย":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            productSelect.value != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
      case "อู่พีเอ็มจี เซอร์วิส":
        if(productSelect.value == "แนะนำเข้าใช้บริการ"){
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              productSelect.value != "" &&
              _referralCarReg.text != "" &&
              _referralNote.text != "") {
            var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
            return response;
          }
        }
        if(productSelect.value == "ซื้ออะไหล่ / ประดับยนต์"){
          if (_referralFullName.text != "" &&
              _referralPhone.text != "" &&
              productSelect.value != "" &&
              _referralNote.text != "") {
            openButton.value = true;
            openText.value = true;
          }
        }
        break;
      case "จัดหาสถานที่ตรวจเช็ครถ":
        if (_referralFullName.text != "" &&
            _referralPhone.text != "" &&
            _referralLocation.text != "" &&
            countyCtl.selectAmphur.value != "" &&
            countyCtl.selectTumbol.value != "") {
          var response = await AppAlert.showNewConfirm(context, "ยกเลิกทำรายการ", "คุณต้องการที่จะยกเลิกการทำรายการ\nใช่หรือไม่?", "ยกเลิก", "ดำเนินการต่อ");
          return response;
        }
        break;
    }
    return true;
  }
  ///โชว์ปุ่มกดดำเนินการต่อ

  setPOI(){
    try{
      rank.value = profileCtl.rankMR.rankCurrent!;
      if(referralSelect == "เข้าใช้บริการ"){
          codePOI.value = recommendMRCtl.poiList[1].running.toString();
          double pointValue = double.parse(recommendMRCtl.poiList[1].point.toString());
          if (rank.value == "STANDARD") {
            likePoint.value = pointValue;
          } else if (rank.value == "PLUS") {
            likePoint.value = (pointValue * 1.25);
          } else if (rank.value == "PRO") {
            likePoint.value = (pointValue * 1.5);
          }
      }

      if(referralSelect == "ออกรถใหม่"){
        codePOI.value = recommendMRCtl.poiList[0].running.toString();
        print(codePOI.value);
        double pointValue = double.parse(recommendMRCtl.poiList[0].point.toString());
        print(pointValue);
        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

      if(referralSelect == "ซื้ออะไหล่ ประดับยนต์ แม็คยาง"){
        codePOI.value = recommendMRCtl.poiList[2].running.toString();
        double pointValue = double.parse(recommendMRCtl.poiList[2].point.toString());
        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

      if(referralSelect == "งานศูนย์ซ่อมสีและตัวถัง" && productSelect.value == "งานซ่อมทำสี"){
        if(moreProductSelect.value == "งานเคลม"){
          codePOI.value = recommendMRCtl.poiList[15].running.toString();
          double pointValue = double.parse(recommendMRCtl.poiList[15].point.toString());
          if (rank.value == "STANDARD") {
            likePoint.value = pointValue;
          } else if (rank.value == "PLUS") {
            likePoint.value = (pointValue * 1.25);
          } else if (rank.value == "PRO") {
            likePoint.value = (pointValue * 1.5);
          }
        }
      }

      if(referralSelect == "งานศูนย์ซ่อมสีและตัวถัง" && productSelect.value == "เคลือบแก้ว"){
        double pointValue = 0.00;
        if(moreProductSelect.value == "ระบบพ่น รับประกัน 1 ปี"){
          codePOI.value = recommendMRCtl.poiList[3].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[3].point.toString());
        }
        if(moreProductSelect.value == "ระบบพ่น รับประกัน 3 ปี"){
          codePOI.value = recommendMRCtl.poiList[4].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[4].point.toString());

        }
        if(moreProductSelect.value == "ระบบพ่น Diamond"){
          codePOI.value = recommendMRCtl.poiList[5].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[5].point.toString());
        }
        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

      if(referralSelect == "งานศูนย์ซ่อมสีและตัวถัง" && productSelect.value == "ผลิตภัณฑ์เสริม"){
        if(moreProductSelect.value == "ขัดสีรอบคัน"){
          codePOI.value = recommendMRCtl.poiList[6].running.toString();
          double pointValue = double.parse(recommendMRCtl.poiList[6].point.toString());
          if (rank.value == "STANDARD") {
            likePoint.value = pointValue;
          } else if (rank.value == "PLUS") {
            likePoint.value = (pointValue * 1.25);
          } else if (rank.value == "PRO") {
            likePoint.value = (pointValue * 1.5);
          }
        }
      }

      if(referralSelect == "ทะเบียนประกันภัย"){
        double pointValue = 0.00;
        if(productSelect.value == "ประกันภัย ประเภท 1"){
          codePOI.value = recommendMRCtl.poiList[7].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[7].point.toString());
        }

        if(productSelect.value == "ประกันภัย ประเภท 2+และ 3+"){
          codePOI.value = recommendMRCtl.poiList[8].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[8].point.toString());
        }

        if(productSelect.value == "ประกัน ประเภท 2 ธรรมดา และ 3 ธรรมดา"){
          codePOI.value = recommendMRCtl.poiList[9].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[9].point.toString());
        }

        if(productSelect.value == "ต่อพรบ."){
          codePOI.value = recommendMRCtl.poiList[10].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[10].point.toString());
        }

        if(productSelect.value == "ต่อทะเบียน"){
          codePOI.value = recommendMRCtl.poiList[11].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[11].point.toString());
        }

        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

      if(referralSelect == "อู่พีเอ็มจี เซอร์วิส"){
        double pointValue = 0.00;
        if(productSelect.value == "แนะนำเข้าใช้บริการ"){
          codePOI.value = recommendMRCtl.poiList[12].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[12].point.toString());
        }

        if(productSelect.value == "ซื้ออะไหล่ / ประดับยนต์"){
          codePOI.value = recommendMRCtl.poiList[13].running.toString();
          pointValue = double.parse(recommendMRCtl.poiList[13].point.toString());
        }

        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

      if(referralSelect == "จัดหาสถานที่ตรวจเช็ครถ"){
        codePOI.value = recommendMRCtl.poiList[14].running.toString();
        double pointValue = double.parse(recommendMRCtl.poiList[14].point.toString());
        if (rank.value == "STANDARD") {
          likePoint.value = pointValue;
        } else if (rank.value == "PLUS") {
          likePoint.value = (pointValue * 1.25);
        } else if (rank.value == "PRO") {
          likePoint.value = (pointValue * 1.5);
        }
      }

    }catch(e){
      if (kDebugMode) {
        print("error setPOI => $e");
      }
    }
  }

  saveData() async {
    try{
      Map data = {};
      AppLoader.loader(context);
      if(referralSelect == "เข้าใช้บริการ"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'carRegis': _referralCarReg.text,
          'noteReferral': _referralNote.text,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "ออกรถใหม่"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'modelCar': carSelect.value,
          'noteReferral': _referralNote.text,
          'branchName': branch,
          'salesPerson': salesPerson,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'amountFiat': "3000",
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "ซื้ออะไหล่ ประดับยนต์ แม็คยาง"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'productReferral': productSelect.value,
          'noteReferral': _referralNote.text,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "งานศูนย์ซ่อมสีและตัวถัง"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'productReferral': productSelect.value,
          'noteReferral': moreProductSelect.value,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "ทะเบียนประกันภัย"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'productReferral': productSelect.value,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "อู่พีเอ็มจี เซอร์วิส"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'carRegis': productSelect.value == "ซื้ออะไหล่ / ประดับยนต์" ? null : _referralCarReg.text,
          'productReferral': productSelect.value,
          'noteReferral': _referralNote.text,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }

      if(referralSelect == "จัดหาสถานที่ตรวจเช็ครถ"){
        data = {
          'referral': referralSelect,
          'mrReferral': profileCtl.profile.value.mrCode,
          'fullName': _referralFullName.text,
          'phone': _referralPhone.text,
          'location': _referralLocation.text,
          'noteReferral': _referralLocation.text,
          'tumbol': countyCtl.selectTumbol.value,
          'aumphur': countyCtl.selectAmphur.value,
          'rewardType': "likepoint",
          'amountLikepoint': likePoint.value,
          'statusReferral': "pending",
          'fullNameReferral': profileCtl.profile.value.fullNameMR,
          'poi': codePOI.value,
          'rank': profileCtl.rankMR.rankCurrent,
        };
      }
      final response = await AppApi.post(AppUrl.createReferralMR, data);
      if(response['status'] == 200){
        Get.off(recommendSuccessNewMRPage(data));
      }
      if(response['status'] == 402){
        setState(() {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(
              context,
              "ไม่สามารถทำรายการได้",
              "เนื่องจากข้อมูลแนะนำของคุณมีการทำรายการ\nแนะนำกิจกรรมนี้ไปแล้ว\nคุณสามารถทำรายการได้ใหม่อีกครั้ง ในวันถัดไป\nขออภัยในความไม่สะดวก",
              "ตกลง");
        });
      }
      if(response['status'] == 404){
        setState(() {
          AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
          AppLoader.dismiss(context);
        });
      }
    }catch(e){
      if (kDebugMode) {
        print("error save data ==> $e");
      }
      setState(() {
        AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
        AppLoader.dismiss(context);
      });
    }
  }
}
