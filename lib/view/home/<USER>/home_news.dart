import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/event_register/detail_event_register.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/event_register/list_event.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/feelwell/feelwell_list.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/news/news_list_view.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/news/news_view.dart';

class HomeNews extends StatefulWidget {
  const HomeNews({Key? key}) : super(key: key);

  @override
  State<HomeNews> createState() => _HomeNewsState();
}

class _HomeNewsState extends State<HomeNews> {
  final ScrollController scrollController = ScrollController();
  int? _currentNews;
  int? _currentEvent;

  final dataProfileCtl = Get.put(ProfileController());
  final eventAndNewsCtl = Get.put(EventAndNewsController());
  final pageCtl = Get.put(PageSelectController());
  final promotionCtl = Get.find<PromotionController>();

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "HomeNews");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Obx(() {
        if (eventAndNewsCtl.isLoading.value) {
          return AppLoader.loaderWaitPage(context);
        } else {
          return NotificationListener(
            onNotification: (notification) {
              if (notification is ScrollUpdateNotification &&
                  notification.metrics.axis == Axis.vertical) {
                pageCtl.scrollParam.value = notification.metrics.pixels;
              }
              return true;
            },
            child: SingleChildScrollView(
              controller: scrollController,
              child: Column(
                children: [
                  const SizedBox(
                    height: 130,
                  ),
                  eventAndNewsCtl.eventList.data!.isNotEmpty ?
                  Container(
                    margin: const EdgeInsets.only(
                        left: 18,
                        right: 18),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            AppWidget.boldTextS(context, "กิจกรรม", 13,
                                const Color(0xFF282828), FontWeight.w400),
                            AppWidget.normalTextS(context, " ร่วมสนุก..", 13,
                                const Color(0xFF282828), FontWeight.w400),
                            AppWidget.normalTextS(context, "กับอีซูซุประชากิจฯ", 13,
                                const Color(0xFF664701), FontWeight.w400),
                          ],
                        ),
                        InkWell(
                          onTap: () async {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ListEventPage(),
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              AppWidget.normalTextS(context, "ดูทั้งหมด ", 13,
                                  const Color(0xFF91908E), FontWeight.w400),
                              const Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 13,
                                color: Color(0xFFFF9300),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ) : const SizedBox(),
                  SizedBox(
                      height: eventAndNewsCtl.eventList.data!.isNotEmpty ? 10 : 0
                  ),
                  eventAndNewsCtl.eventList.data!.isNotEmpty ?
                  carouselRatio11EventRegis(eventAndNewsCtl.eventList.data!) : const SizedBox(),
                  SizedBox(
                      height: eventAndNewsCtl.eventList.data!.isNotEmpty ? 20 : 0
                  ),
                  Container(
                    margin: const EdgeInsets.only(left: 18, right: 18),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            AppWidget.boldTextS(context, "ข่าวสารทั่วไป", 13,
                                const Color(0xFF282828), FontWeight.w400),
                          ],
                        ),
                        InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const NewsListViewPage(),
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              AppWidget.normalTextS(context, "ดูทั้งหมด ", 13,
                                  const Color(0xFF91908E), FontWeight.w400),
                              const Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 13,
                                color: Color(0xFFFF9300),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                      height: 10
                  ),
                  carouselRatio11News(eventAndNewsCtl.newsList.data!),
                  const SizedBox(
                      height: 20
                  ),
                  Container(
                    margin: const EdgeInsets.only(left: 18, right: 18),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            AppWidget.boldTextS(
                                context, "คอนเทนต์", 14, Colors.black, FontWeight.w400),
                            AppWidget.normalTextS(context, " สาระความรู้", 14,
                                const Color(0xFF664701), FontWeight.w400),
                          ],
                        ),
                        InkWell(
                          onTap: () async {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => FeelWellListPage("all", "all"),
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              AppWidget.normalTextS(context, "ดูทั้งหมด ", 14,
                                  const Color(0xFF91908E), FontWeight.w400),
                              const Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 13,
                                color: Color(0xFFFF9300),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                      height: 10
                  ),
                  carouselRatio169(eventAndNewsCtl.feelWellList.data!),
                  const Padding(padding: EdgeInsets.only(bottom: 90))
                ],
              ),
            ),
          );
        }
      }),
    );
  }

  Widget carouselRatio11EventRegis(List<dynamic> listData){
    return CarouselSlider.builder(
      itemCount: listData.length,
      options: CarouselOptions(
        height: Get.width * 0.9,
        enlargeCenterPage: true,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: false,  // เลื่อนสไลด์ได้ไกล
        viewportFraction: 1,  // ส่วนที่เปิดเผยของรายการภาพในแต่ละสไลด์
        aspectRatio: 1 / 1,  // อัตราส่วนกว้างต่อสูงของภาพในแต่ละสไลด์
      ),
      itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width: Get.width * 0.9,
                height: Get.width * 0.9,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF282828),
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: Image.network(
                    '${promotionCtl.urlEvent.value}${listData[itemIndex].informationPic}',
                    fit: BoxFit.fill,
                    errorBuilder: (BuildContext context,
                        Object exception,
                        StackTrace? stackTrace) {
                      return Container(
                        padding: const EdgeInsets.all(10),
                        child: const Icon(
                          Icons.error,
                          size: 50,
                          color: Color(0xFFFFB100),
                      ),);
                    },
                    loadingBuilder: (BuildContext context,
                        Widget child,
                        ImageChunkEvent? loadingProgress) {
                      if (loadingProgress == null) {
                        return child;
                      }
                      return Align(
                        alignment: Alignment.center,
                        child: SizedBox(
                          width: 50,
                          height: 50,
                          child:
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            color:
                            const Color(0xFFFFFFFF),
                            value: loadingProgress
                                .expectedTotalBytes !=
                                null
                                ? loadingProgress
                                .cumulativeBytesLoaded /
                                loadingProgress
                                    .expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 106,
                  height: 34,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0x00FFC700).withOpacity(0.9),
                        const Color(0x00FFB100).withOpacity(0.9),
                        const Color(0xFFFF9900).withOpacity(0.9),
                      ],
                    ),
                    border: Border.all(
                      color: const Color(0xFF282828),
                      width: 2,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: (){
                      Map data = {
                        "id_activity" : eventAndNewsCtl.eventList.data![itemIndex].idActivity,
                        "information_pic" : eventAndNewsCtl.eventList.data![itemIndex].informationPic,
                        'information_name' : eventAndNewsCtl.eventList.data![itemIndex].nameActivity,
                        "information_body" : AppService.parseHtmlString(eventAndNewsCtl.eventList.data![itemIndex].informationBody.toString()),
                        "fdate_regis_activity" : eventAndNewsCtl.eventList.data![itemIndex].fdateRegisActivity,
                        "edate_regis_activity" :eventAndNewsCtl.eventList.data![itemIndex].edateRegisActivity,
                        "fdate_activity" : eventAndNewsCtl.eventList.data![itemIndex].fdateActivity,
                        "edate_activity" : eventAndNewsCtl.eventList.data![itemIndex].edateActivity,
                        "LP_activity" : eventAndNewsCtl.eventList.data![itemIndex].lpActivity
                      };
                      Navigator.push(context,
                          MaterialPageRoute(
                            builder: (context) => DetailEventRegisterPage(data),
                          )
                      );
                    },
                    child: AppWidget.boldText(
                        context,
                        "ลงทะเบียน",
                        14,
                        const Color(0xFFFFFFFF),
                        FontWeight.w500),
                  )
                ),
              )
            ],
          ),
    );
  }

  Widget carouselRatio11News(List<dynamic> listData){
    return CarouselSlider.builder(
      itemCount: listData.length,
      options: CarouselOptions(
        height: Get.width * 0.9,
        enlargeCenterPage: true,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: false,  // เลื่อนสไลด์ได้ไกล
        viewportFraction: 1,  // ส่วนที่เปิดเผยของรายการภาพในแต่ละสไลด์
        aspectRatio: 1 / 1,  // อัตราส่วนกว้างต่อสูงของภาพในแต่ละสไลด์
      ),
      itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width:  Get.width * 0.9,
                height:  Get.width * 0.9,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF282828),
                    width: 2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: Image.network(
                    '${promotionCtl.urlEvent.value}${listData[itemIndex].informationPic}',
                    fit: BoxFit.fill,
                    errorBuilder: (BuildContext context,
                        Object exception,
                        StackTrace? stackTrace) {
                      return Container(
                        padding: const EdgeInsets.all(10),
                        child: const Icon(
                          Icons.error,
                          size: 50,
                          color: Color(0xFFFFB100),
                        ),);
                    },
                    loadingBuilder: (BuildContext context,
                        Widget child,
                        ImageChunkEvent? loadingProgress) {
                      if (loadingProgress == null) {
                        return child;
                      }
                      return Align(
                        alignment: Alignment.center,
                        child: SizedBox(
                          width: 50,
                          height: 50,
                          child:
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            color:
                            const Color(0xFFFFFFFF),
                            value: loadingProgress
                                .expectedTotalBytes !=
                                null
                                ? loadingProgress
                                .cumulativeBytesLoaded /
                                loadingProgress
                                    .expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 106,
                  height: 34,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0x00FFC700).withOpacity(0.9),
                        const Color(0x00FFB100).withOpacity(0.9),
                        const Color(0xFFFF9900).withOpacity(0.9),
                      ],
                    ),
                    border: Border.all(
                      color: const Color(0xFF282828),
                      width: 2,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: (){
                      eventAndNewsCtl.indexNews.value = itemIndex;
                      Get.to(() => NewsViewPage());
                    },
                    child: AppWidget.boldText(
                        context,
                        "ดูเพิ่มเติม",
                        14,
                        const Color(0xFFFFFFFF),
                        FontWeight.w500),
                  )
                ),
              )
            ],
          ),
    );
  }

  Widget carouselRatio169(List<dynamic> listData){
    return CarouselSlider.builder(
      itemCount: listData.length,
      options: CarouselOptions(
        height: 190,
        enlargeCenterPage: true,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        enableInfiniteScroll: true,  // เลื่อนสไลด์ได้ไกล
        viewportFraction: 1,  // ส่วนที่เปิดเผยของรายการภาพในแต่ละสไลด์
        aspectRatio: 16 / 9,  // อัตราส่วนกว้างต่อสูงของภาพในแต่ละสไลด์
      ),
      itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) =>
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width: Get.width * 0.9,
                height: 190,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF282828),
                    width: 1.5,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: Image.network(
                    '${listData[itemIndex].primaryImg}',
                    fit: BoxFit.cover,
                    errorBuilder: (BuildContext context,
                        Object exception,
                        StackTrace? stackTrace) {
                      return Container(
                        padding: const EdgeInsets.all(10),
                        child: const Icon(
                          Icons.error,
                          size: 50,
                          color: Color(0xFFFFB100),
                        ),);
                    },
                    loadingBuilder: (BuildContext context,
                        Widget child,
                        ImageChunkEvent? loadingProgress) {
                      if (loadingProgress == null) {
                        return child;
                      }
                      return Align(
                        alignment: Alignment.center,
                        child: SizedBox(
                          width: 50,
                          height: 50,
                          child:
                          CircularProgressIndicator(
                            strokeWidth: 2,
                            color:
                            const Color(0xFFFFFFFF),
                            value: loadingProgress
                                .expectedTotalBytes !=
                                null
                                ? loadingProgress
                                .cumulativeBytesLoaded /
                                loadingProgress
                                    .expectedTotalBytes!
                                : null,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: InkWell(
                  onTap: (){
                    Navigator.push(context,
                      MaterialPageRoute(
                        builder: (context) => FeelWellListPage(
                            eventAndNewsCtl.feelWellList.data![itemIndex].running, eventAndNewsCtl.feelWellList.data![itemIndex].typeFeelWellTv
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: 106,
                    height: 34,
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0x00FFC700).withOpacity(0.9),
                          const Color(0x00FFB100).withOpacity(0.9),
                          const Color(0xFFFF9900).withOpacity(0.9),
                        ],
                      ),
                      border: Border.all(
                        color: const Color(0xFF282828),
                        width: 2,
                      ),
                    ),
                    alignment: Alignment.center,
                    child: AppWidget.boldText(
                        context,
                        "ดูเพิ่มเติม",
                        14,
                        const Color(0xFFFFFFFF),
                        FontWeight.w500),
                  ),
                ),
              )
            ],
          ),
    );
  }
}
