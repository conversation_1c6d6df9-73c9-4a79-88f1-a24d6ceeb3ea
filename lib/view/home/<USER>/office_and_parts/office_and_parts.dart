import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/office_and_parts_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class OfficeAndPartsPage extends StatefulWidget {
  const OfficeAndPartsPage({Key? key}) : super(key: key);

  @override
  State<OfficeAndPartsPage> createState() => _OfficeAndPartsPageState();
}

class _OfficeAndPartsPageState extends State<OfficeAndPartsPage> {

  final officeAndPartsCtl = Get.put(OfficeAndPartsController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "TalkWithOfficeParts");
    getOAP();
  }

  getOAP() async {
    if(officeAndPartsCtl.memberOfficePartsList.data!.isEmpty){
      await officeAndPartsCtl.getMemberOfficeParts();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Obx((){
        if(officeAndPartsCtl.isLoading.value){
          return AppLoader.loaderWaitPage(context);
        } else {
          return Stack(
            children: [
              Container(
                width: 1.sw,
                height: 1.sh,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: const [0.2, 0.8],
                    colors: [
                      const Color(0xFFF4F4F4).withOpacity(0.85),
                      const Color(0xFFFFFFFF).withOpacity(0.85),
                    ],
                  ),
                ),
              ),
              Container(
                width: 1.sw,
                height: 1.sh,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: const [0, 0.5],
                    colors: [
                      const Color(0xFF282828),
                      const Color(0xFFFFB100).withOpacity(0.20),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomLeft,
                child: Container(
                  margin: EdgeInsets.only(bottom: 0.01.sh),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 0.05.sw,
                      ),
                      AppWidget.boldTextS(
                          context,
                          "ISUZU",
                          12.sp,
                          Colors.white,
                          FontWeight.w700),
                      AppWidget.boldTextS(
                          context,
                          " PRACHAKIJ",
                          12.sp,
                          Colors.white,
                          FontWeight.w400),
                    ],
                  ),
                ),
              ),
              Container(
                width: 1.sw,
                height: 0.95.sh,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    // topLeft: Radius.circular(0.03.sh),
                    // topRight: Radius.circular(0.03.sh),
                    bottomLeft: Radius.circular(0.03.sh),
                    bottomRight: Radius.circular(0.03.sh),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: const [0, 0.5],
                    colors: [
                      const Color(0xFFF4F4F4).withOpacity(0.85),
                      const Color(0xFFFFFFFF).withOpacity(0.85),
                    ],
                  ),
                ),
              ),
              Column(
                children: [
                  buildTopContainer(),
                  buildListView(),
                ],
              ),
            ],
          );
        }
      }),
    );
  }



  buildTopContainer(){
    return Stack(
      children: [
        Container(
          width: 1.sw,
          height: 0.17.sh,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              // topLeft: Radius.circular(0.03.sh),
              // topRight: Radius.circular(0.03.sh),
              bottomLeft: Radius.circular(0.03.sh),
              // bottomRight: Radius.circular(0.03.sh),
            ),
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              stops: const [0, 0.5],
              colors: [
                const Color(0xFF333333).withOpacity(0.9),
                const Color(0xFF282828),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.1),
                offset: const Offset(0, 5),
                blurRadius: 3.0,
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(
              top: 0.08.sh,
              right: 0.05.sw
          ),
          child: Align(
            alignment: Alignment.topRight,
            child: Image.asset(
              'assets/image/service/menu_service.png',
              width: 0.25.sw,
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(
            top: 0.08.sh,
            left: 0.05.sw,
            right: 0.05.sw,
          ),
          // height: 0.1.sh,
          // color: Colors.red,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: (){
                  Navigator.pop(context);
                },
                child: SizedBox(
                  width: 0.04.sh,
                  height: 0.04.sh,
                  child: Icon(Icons.arrow_back_ios_new,
                    color: const Color(0xFFFFB100),
                    size: 0.02.sh,
                  ),
                ),
              ),
              SizedBox(
                width: 0.02.sw,
              ),
              Container(
                margin: EdgeInsets.only(
                  top: 0.004.sh,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.boldTextS(
                        context,
                        "ศูนย์บริการและอะไหล่",
                        18.sp,
                        Colors.white,
                        FontWeight.w600),
                    AppWidget.normalTextS(
                        context,
                        "กรุณาเลือกทีมงานที่ปรึกษา",
                        14.sp,
                        const Color(0xFFFFB100),
                        FontWeight.w400),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  buildListView(){
    return SizedBox(
      width: 1.sw,
      height: 0.8.sh,
      // color: Colors.red,
      child: ListView.builder(
          itemCount: officeAndPartsCtl.memberOfficePartsList.data!.length,
          itemBuilder: (BuildContext context, int index){
            return Column(
              children: [
                buildListMember(context,
                    officeAndPartsCtl.memberOfficePartsList.data![index].picture,
                    officeAndPartsCtl.memberOfficePartsList.data![index].name,
                    officeAndPartsCtl.memberOfficePartsList.data![index].team,
                    officeAndPartsCtl.memberOfficePartsList.data![index].phone,
                    officeAndPartsCtl.memberOfficePartsList.data![index].lineId),
                officeAndPartsCtl.memberOfficePartsList.data!.length == (index + 1)
                    ? SizedBox(
                  height: 0.02.sh,
                )
                    : const Divider(
                  thickness: 0.1,
                  indent: 1,
                  endIndent: 1,
                  color: Color(0xFF707070),
                ),
              ],
            );
          }
      ),
    );
  }
  buildListMember(context, picture, name, team, phone, lineId){
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 0.05.sw,
        ),
        Container(
          width: 0.18.sw,
          height: 0.18.sw,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(width: 1, color: const Color(0xFFFFB100),
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.1),
                offset: const Offset(0, 3),
                blurRadius: 3.0,
              ),
            ],
          ),
          child: CachedNetworkImage(
            imageUrl: picture,
            fit: BoxFit.cover,
            placeholder: (context, url) => SizedBox(
              width: 0.2.sw,
              height: 0.2.sw,
              child: const Center(
                child: CircularProgressIndicator(color: Colors.orange),
              ),
            ),
            errorWidget: (context, url, error) => const Center(
              child: Icon(
                Icons.error,
                color: Color(0xFFFFB100),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 0.05.sw,
        ),
        SizedBox(
          width: 0.3.sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.boldTextS(
                  context,
                  name,
                  16.sp,
                  const Color(0xFF000000),
                  FontWeight.w400),
              team == "SA ONLINE"
                  ? Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 0.03.sw,
                  vertical: 0.005.sh,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(0.03.sh),
                    topRight: Radius.circular(0.03.sh),
                    bottomLeft: Radius.circular(0.03.sh),
                    bottomRight: Radius.circular(0.03.sh),
                  ),
                  color: const Color(0xFF282828),
                ),
                child: AppWidget.normalTextS(
                  context,
                  team,
                  14.sp,
                  const Color(0xFFFFFFFF),
                  FontWeight.w400,
                ),
              )
                  : Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 0.03.sw,
                  vertical: 0.005.sh,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(0.03.sh),
                    topRight: Radius.circular(0.03.sh),
                    bottomLeft: Radius.circular(0.03.sh),
                    bottomRight: Radius.circular(0.03.sh),
                  ),
                  color: const Color(0xFFFFB100),
                ),
                child: AppWidget.normalTextS(
                  context,
                  team,
                  12.sp,
                  const Color(0xFF000000),
                  FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          width: 0.05.sw,
        ),
        SizedBox(
          width: 0.05.sh,
          height: 0.05.sh,
          child: Material(
            shape: const CircleBorder(),
            clipBehavior: Clip.hardEdge,
            child: InkWell(
              splashColor: const Color(0xFFFFB100),
              onTap: (){
                AppService.callPhone(phone);
              },
              child: Image.asset('assets/image/service/insurance/call_btn.png'),
            ),
          ),
        ),
        SizedBox(
          width: 0.05.sw,
        ),
        SizedBox(
          width: 0.05.sh,
          height: 0.05.sh,
          child: Material(
            shape: const CircleBorder(),
            clipBehavior: Clip.hardEdge,
            child: InkWell(
              splashColor: const Color(0xFFFFB100),
              onTap: (){
                AppService.launchUrl("http://line.me/ti/p/~" + lineId);
              },
              child: Image.asset('assets/image/service/insurance/message_btn.png'),
            ),
          ),
        ),
      ],
    );
  }

}
