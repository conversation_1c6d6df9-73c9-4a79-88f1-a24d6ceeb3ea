import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/lifetime_appointment_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/my_car_detail.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MyCarCard extends StatefulWidget {
  const MyCarCard({Key? key}) : super(key: key);

  @override
  State<MyCarCard> createState() => _MyCarCardState();
}

class _MyCarCardState extends State<MyCarCard> {
  final lifetimeAppointmentCtl = Get.put(LifetimeAppointmentController());
  final appointmentCtl = Get.put(AppointmentController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => lifetimeAppointmentCtl.isLoading.value
        ? const SizedBox()
        : Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 226,
          child: PageView.builder(
              itemCount: lifetimeAppointmentCtl
                  .lifetimeAppointment.carList!.length,
              clipBehavior: Clip.none,
              physics: const BouncingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              controller: lifetimeAppointmentCtl.pageController,
              itemBuilder: (BuildContext context, index) => Container(
                width: Get.width,
                height: 226,
                padding: EdgeInsets.only(
                  left: Get.width < 500 ? 8 : 54,
                  right: Get.width < 500 ? 8 : 54,
                ),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x0C000000),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ],
                ),
                child: Container(
                  width: Get.width,
                  height: 226,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFFFFFFF),
                        Color(0xFFF8F8F8),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                        const Color(0xFF000000).withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      )
                    ],
                    borderRadius: BorderRadius.circular(15),
                  ),
                  padding: const EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 14,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          AppWidget.boldTextS(
                              context,
                              "รถของคุณ",
                              12.5,
                              const Color(0xFF282828),
                              FontWeight.w400),
                          const Spacer(),
                          InkWell(
                            onTap: () {
                              lifetimeAppointmentCtl
                                  .carSelectIndex.value = index;
                              Get.to(() => const MyCarDetail());
                            },
                            child: Row(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                AppWidget.normalText(
                                    context,
                                    "ดูข้อมูลเพิ่มเติม ",
                                    12.5,
                                    const Color(0xFF282828),
                                    FontWeight.w400),
                                const Icon(
                                  Icons.arrow_forward_ios_rounded,
                                  size: 14,
                                  color: Color(0xFFFFB100),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      const Divider(
                        height: 0.5,
                        color: Color(0xFFBCBCBC),
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      Row(
                        children: [
                          Column(
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 4,
                              ),
                              AppWidget.boldTextS(
                                  context,
                                  "รุ่นรถ : ${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModelSa != "" ? lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModelSa : "ไม่ทราบข้อมูล"}",
                                  14,
                                  const Color(0xFF282828),
                                  FontWeight.w600),
                              const SizedBox(
                                height: 4,
                              ),
                              RichText(
                                text: TextSpan(children: [
                                  TextSpan(
                                      text: "เกรด : ",
                                      style: TextStyle(
                                        color:
                                        const Color(0xFF282828),
                                        fontSize: 13,
                                        fontFamily: 'Prompt',
                                        fontWeight: FontWeight.w300,
                                        shadows: <Shadow>[
                                          Shadow(
                                            offset:
                                            const Offset(0, 1),
                                            blurRadius: 5.0,
                                            color: const Color(
                                                0xFF000000)
                                                .withOpacity(0.1),
                                          ),
                                        ],
                                      )),
                                  TextSpan(
                                      text: lifetimeAppointmentCtl
                                          .lifetimeAppointment
                                          .carList![index]
                                          .carModel !=
                                          ""
                                          ? lifetimeAppointmentCtl
                                          .lifetimeAppointment
                                          .carList![index]
                                          .carModel
                                          : "ไม่ทราบข้อมูล",
                                      style: TextStyle(
                                        color:
                                        const Color(0xFF895F00),
                                        fontSize: 12,
                                        fontFamily: 'Prompt-Medium',
                                        fontWeight: FontWeight.w600,
                                        shadows: <Shadow>[
                                          Shadow(
                                            offset:
                                            const Offset(0, 1),
                                            blurRadius: 5.0,
                                            color: const Color(
                                                0xFF000000)
                                                .withOpacity(0.1),
                                          ),
                                        ],
                                      )),
                                ]),
                              ),
                              const SizedBox(
                                height: 4,
                              ),
                              RichText(
                                text: TextSpan(children: [
                                  TextSpan(
                                      text: "ทะเบียน : ",
                                      style: TextStyle(
                                        color:
                                        const Color(0xFF282828),
                                        fontSize: 13,
                                        fontFamily: 'Prompt',
                                        fontWeight: FontWeight.w300,
                                        shadows: <Shadow>[
                                          Shadow(
                                            offset:
                                            const Offset(0, 1),
                                            blurRadius: 5.0,
                                            color: const Color(
                                                0xFF000000)
                                                .withOpacity(0.1),
                                          ),
                                        ],
                                      )),
                                  TextSpan(
                                      text: lifetimeAppointmentCtl
                                          .lifetimeAppointment
                                          .carList![index]
                                          .reg,
                                      style: TextStyle(
                                        color:
                                        const Color(0xFF282828),
                                        fontSize: 14,
                                        fontFamily: 'Prompt-Medium',
                                        fontWeight: FontWeight.w600,
                                        shadows: <Shadow>[
                                          Shadow(
                                            offset:
                                            const Offset(0, 1),
                                            blurRadius: 5.0,
                                            color: const Color(
                                                0xFF000000)
                                                .withOpacity(0.1),
                                          ),
                                        ],
                                      )),
                                ]),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Image.network(
                            lifetimeAppointmentCtl.lifetimeAppointment
                                .carList![index].carImage
                                .toString(),
                            width: 126,
                            height: 76,
                            errorBuilder: (BuildContext context,
                                Object exception,
                                StackTrace? stackTrace) {
                              return Container(
                                width: 126,
                                height: 76,
                                padding: const EdgeInsets.all(10),
                                child: Image.asset(
                                  "assets/image/car_detail/car_mockup.png",
                                ),
                              );
                            },
                            loadingBuilder: (BuildContext context,
                                Widget child,
                                ImageChunkEvent? loadingProgress) {
                              if (loadingProgress == null) {
                                return child;
                              }
                              return Container(
                                  width: 126,
                                  height: 76,
                                  alignment: Alignment.center,
                                  child: Stack(
                                    children: [
                                      Container(
                                        width: 126,
                                        height: 76,
                                        alignment: Alignment.center,
                                        padding:
                                        const EdgeInsets.all(10),
                                        child: Image.asset(
                                          "assets/image/car_detail/car_mockup.png",
                                        ),
                                      ),
                                      Align(
                                        alignment: Alignment.center,
                                        child: SizedBox(
                                          width: 15,
                                          height: 15,
                                          child:
                                          CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: const Color(
                                                0xFFFFFFFF),
                                            value: loadingProgress
                                                .expectedTotalBytes !=
                                                null
                                                ? loadingProgress
                                                .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                                : null,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ));
                            },
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      const Divider(
                        height: 0.5,
                        color: Color(0xFFBCBCBC),
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      AppWidget.normalText(
                          context,
                          "สถานะครบกำหนดงานบริการ",
                          12.5,
                          const Color(0xFF282828),
                          FontWeight.w400),

                      const SizedBox(
                        height: 8,
                      ),

                      /// สถานะครบกำหนดงานบริการ
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment:
                        MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            height: 36,
                            // color: Colors.red,
                            child: Row(
                              crossAxisAlignment:
                              CrossAxisAlignment.start,
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                Container(
                                    height: 15,
                                    width: 15,
                                    child: Center(
                                        child: Icon(
                                          Icons.error_outline_rounded,
                                          color: Colors.red,
                                          size: 18,
                                        ))),
                                SizedBox(
                                  width: 6,
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "${lifetimeAppointmentCtl.calculateNearestProduct(
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .miles!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .rustproof!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .insurance!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .pmg!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}"
                                            : null,)}",
                                      style: TextStyle(
                                        fontFamily: 'Prompt',
                                        color: const Color(0xFF282828),
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      "${lifetimeAppointmentCtl.calculateNearestDate(
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .miles!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .rustproof!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .insurance!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}"
                                            : null,
                                        lifetimeAppointmentCtl
                                            .lifetimeAppointment
                                            .carList![index]
                                            .pmg!
                                            .isNotEmpty
                                            ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}"
                                            : null,
                                      )}",
                                      style: TextStyle(
                                        fontFamily: 'Prompt',
                                        color: const Color(0xFFEB2227),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    // AppWidget.boldTextS(
                                    //     context,
                                    //     "${lifetimeAppointmentCtl.calculateNearestProduct(
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .miles!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .rustproof!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .insurance!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .pmg!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}"
                                    //           : null,
                                    //     )}",
                                    //     14,
                                    //     const Color(0xFF282828),
                                    //     FontWeight.w600),
                                    // AppWidget.normalTextS(
                                    //     context,
                                    //     "${lifetimeAppointmentCtl.calculateNearestDate(
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .miles!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .rustproof!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .insurance!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}"
                                    //           : null,
                                    //       lifetimeAppointmentCtl
                                    //               .lifetimeAppointment
                                    //               .carList![index]
                                    //               .pmg!
                                    //               .isNotEmpty
                                    //           ? "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}"
                                    //           : null,
                                    //     )}",
                                    //     14.5,
                                    //     const Color(0xFFFA4862),
                                    //     FontWeight.w400),
                                  ],
                                )
                              ],
                            ),
                          ),
                          // SizedBox(width: 8,),
                          Container(
                            width: 146,
                            height: 36,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Color(0xFFE8E6E2),
                                  Color(0xFFD9D8D5),
                                ],
                              ),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.string(
                                    '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5305)"> <path d="M17.5 10V14.5C17.5 16.15 16.15 17.5 14.5 17.5H5.49998C3.84998 17.5 2.5 16.15 2.5 14.5V5.50004C2.5 3.85004 3.84998 2.50003 5.49998 2.50003H10" stroke="#664701" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M15.4167 7.64171L12 11.0584C11.85 11.2084 11.675 11.2917 11.4667 11.3084L9.42499 11.5C8.89165 11.55 8.44166 11.0917 8.49166 10.5584L8.67497 8.57504C8.69163 8.3667 8.77501 8.18336 8.92501 8.04169L12.375 4.59171L15.4167 7.64171V7.64171Z" stroke="#664701" stroke-linejoin="round"/> <path d="M17.25 5.81673L15.4167 7.64173L12.375 4.59173L14.2 2.76672C14.5333 2.43339 15.0834 2.43339 15.4167 2.76672L17.25 4.59173C17.575 4.92506 17.575 5.48339 17.25 5.81673V5.81673Z" stroke="#664701" stroke-linejoin="round"/></g><defs> <clipPath id="clip0_3260_5305"> <rect width="20" height="20" fill="white"/></clipPath></defs></svg>'),
                                const SizedBox(
                                  width: 6,
                                ),
                                Text('นัดหมายจองคิว',style: TextStyle(
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF282828),
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        offset: const Offset(0, 1),
                                        blurRadius: 2.0,
                                        color: const Color(0xFF000000).withOpacity(0.2),
                                      ),
                                    ]
                                ),)
                                // AppWidget.normalTextS(
                                //     context,
                                //     "นัดคิว",
                                //     14,
                                //     const Color(0xFF000000),
                                //     FontWeight.w400),
                              ],
                            ),
                          )
                        ],
                      )
                      // Row(
                      //   // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Row(
                      //       mainAxisAlignment: MainAxisAlignment.center,
                      //       children: [
                      //         SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                      //         const SizedBox(
                      //           width: 8,
                      //         ),
                      //         Column(
                      //           crossAxisAlignment: CrossAxisAlignment.start,
                      //           children: [
                      //             AppWidget.boldTextS(
                      //                 context,
                      //                 "${lifetimeAppointmentCtl.calculateNearestProduct(
                      //                   lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles!.isNotEmpty ?
                      //                   "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}" : null,
                      //                   lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof!.isNotEmpty ?
                      //                   "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}" : null,
                      //                   lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance!.isNotEmpty ?
                      //                   "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}" : null,
                      //                   lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg!.isNotEmpty ?
                      //                   "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}" : null,
                      //                 )}",
                      //                 14,
                      //                 const Color(0xFF282828),
                      //                 FontWeight.w600),
                      //             AppWidget.normalTextS(
                      //                 context,
                      //               "${lifetimeAppointmentCtl.calculateNearestDate(
                      //                               lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles!.isNotEmpty ?
                      //                               "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}" : null,
                      //                               lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof!.isNotEmpty ?
                      //                               "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}" : null,
                      //                               lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance!.isNotEmpty ?
                      //                               "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}" : null,
                      //                               lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg!.isNotEmpty ?
                      //                               "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}" : null,
                      //                             )}",
                      //                 14.5,
                      //                 const Color(0xFFFA4862),
                      //                 FontWeight.w400),
                      //           ],
                      //         )
                      //       ],
                      //     ),
                      //     SizedBox(
                      //       width: 8,
                      //     ),
                      //     // const Spacer(),
                      //     InkWell(
                      //       onTap: (){
                      //         appointmentCtl.carRegSelect.value = lifetimeAppointmentCtl.lifetimeAppointment.carList![index].reg.toString();
                      //         Get.to(() => const AppointmentPage());
                      //       },
                      //       child: Container(
                      //           width: 146,
                      //           height: 38,
                      //           decoration: BoxDecoration(
                      //             borderRadius: BorderRadius.circular(10),
                      //             gradient: const LinearGradient(
                      //               begin: Alignment.topCenter,
                      //               end: Alignment.bottomCenter,
                      //               colors: [
                      //                 Color(0xFFE8E6E2),
                      //                 Color(0xFFD9D8D5),
                      //               ],
                      //             ),
                      //             border: Border.all(
                      //               color: const Color(0xFFFFB100),
                      //             ),
                      //           ),
                      //           child: Row(
                      //             crossAxisAlignment: CrossAxisAlignment.center,
                      //             mainAxisAlignment: MainAxisAlignment.center,
                      //             children: [
                      //               SvgPicture.string('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5305)"> <path d="M17.5 10V14.5C17.5 16.15 16.15 17.5 14.5 17.5H5.49998C3.84998 17.5 2.5 16.15 2.5 14.5V5.50004C2.5 3.85004 3.84998 2.50003 5.49998 2.50003H10" stroke="#664701" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M15.4167 7.64171L12 11.0584C11.85 11.2084 11.675 11.2917 11.4667 11.3084L9.42499 11.5C8.89165 11.55 8.44166 11.0917 8.49166 10.5584L8.67497 8.57504C8.69163 8.3667 8.77501 8.18336 8.92501 8.04169L12.375 4.59171L15.4167 7.64171V7.64171Z" stroke="#664701" stroke-linejoin="round"/> <path d="M17.25 5.81673L15.4167 7.64173L12.375 4.59173L14.2 2.76672C14.5333 2.43339 15.0834 2.43339 15.4167 2.76672L17.25 4.59173C17.575 4.92506 17.575 5.48339 17.25 5.81673V5.81673Z" stroke="#664701" stroke-linejoin="round"/></g><defs> <clipPath id="clip0_3260_5305"> <rect width="20" height="20" fill="white"/></clipPath></defs></svg>'),
                      //               const SizedBox(
                      //                 width: 6,
                      //               ),
                      //               AppWidget.normalTextS(
                      //                   context,
                      //                   "นัดหมายจองคิว",
                      //                   14,
                      //                   const Color(0xFF000000),
                      //                   FontWeight.w400),
                      //             ],
                      //           )
                      //       ),
                      //     )
                      //   ],
                      // ),
                    ],
                  ),
                ),
              )),
        ),
        const SizedBox(
          height: 4,
        ),
        lifetimeAppointmentCtl.lifetimeAppointment.carList!.length == 1
            ? Container()
            : Center(
          child: SmoothPageIndicator(
              count: lifetimeAppointmentCtl
                  .lifetimeAppointment.carList!.length,
              effect: const SwapEffect(
                dotColor: Color(0xFFD9D9D9),
                activeDotColor: Color(0xFFFFB100),
                dotHeight: 6,
                dotWidth: 6,
              ),
              controller: lifetimeAppointmentCtl.pageController),
        )
      ],
    ));
  }
}