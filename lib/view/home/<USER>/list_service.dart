import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/accessory/accessory.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/broken_car/broken_car.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/home_service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/insurance/insurance.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/tab_pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/talksc/talk_SC.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/sa/sa.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';

class ListServicePage extends StatefulWidget {
  const ListServicePage({Key? key}) : super(key: key);

  @override
  State<ListServicePage> createState() => _ListServicePageState();
}

class _ListServicePageState extends State<ListServicePage> {
  final SecureStorage secureStorage = SecureStorage();
  final ScrollController scrollController = ScrollController();

  String? phoneCrashCar;
  String? phoneCallCenter;

  bool statusCrashCar = false;
  bool statusAppointment = false;
  bool statusHomeService = false;
  bool statusLicense = false;
  bool statusColors = false;
  bool statusServicePart = false;
  bool statusAccessory = false;
  bool statusTalkSale = false;
  bool statusSA = false;
  bool statusMR = false;

  final dataProfileCtl = Get.put(ProfileController());
  final pageCtl = Get.put(PageSelectController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Home");
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8),
      child: Container(
        decoration: ShapeDecoration(
          gradient: LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Colors.white, Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          shadows: [
            BoxShadow(
              color: Color(0x0C000000),
              blurRadius: 10,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(
              height: 20,
            ),
            AppWidget.boldTextS(context, "งานบริการ", 14.5,
                const Color(0xFF282828), FontWeight.w500),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.only(left: 40, right: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                        Get.to(() => const AppointmentPage());
                    },
                    child: buildRowMenu(
                      "menu_appoint.png",
                      "จองคิว",
                      "ศูนย์บริการ",
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => const BrokenCarPage());
                    },
                    child: buildRowMenu(
                      "menu_emer.png",
                      "รถเสีย",
                      "แจ้งปัญหา",
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => const HomeServicePage());
                    },
                    child: buildRowMenu(
                      "menu_home_repair.png",
                      "บริการซ่อม",
                      "ถึงบ้าน",
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.only(left: 40, right: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                      onTap: () {
                        // Get.to(() => const AccessoryPage());
                        Get.to(() => const TabPMG());
                      },
                      child: buildRowMenu(
                        "menu_accessories.png",
                        "อะไหล่",
                        "ประดับยนต์",
                      )),
                  InkWell(
                      onTap: () {
                        Get.to(() => const SAPage());
                      },
                      child: buildRowMenu(
                        "menu_sa.png",
                        "ปรึกษางาน",
                        "ศูนย์บริการ",
                      )),
                  InkWell(
                      onTap: () {
                        Get.to(() => const PMGPage());
                      },
                      child: buildRowMenu(
                        "menu_fix.png",
                        "ศูนย์ซ่อมสี",
                        "และตัวถัง",
                      )),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Container(
              padding: const EdgeInsets.only(left: 40, right: 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                      onTap: () {
                        Get.to(() => const TalkSCPage());
                      },
                      child: buildRowMenu(
                        "menu_sale.png",
                        "สนใจซื้อรถ",
                        "ติดต่อเซลส์",
                      )),
                  InkWell(
                      onTap: () {
                        Get.to(() => const InsurancePage());
                      },
                      child: buildRowMenu(
                        "menu_license.png",
                        "ทะเบียน",
                        "ประกัน/พรบ.",
                      )),
                  const SizedBox(
                    width: 59,
                    height: 48,
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 26,
            ),
          ],
        ),
      ),
    );
  }

  buildRowMenu(picture, tile, subtitle) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
            width: 59,
            height: 48,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(15)),
              color: Colors.white,
              border: Border.fromBorderSide(
                BorderSide(
                  color: Color(0xFFE8E6E2),
                  width: 1,
                ),
              ),
            ),
            padding: const EdgeInsets.all(8),
            child: Image.asset(
              "assets/image/service/$picture",
            )),
        const SizedBox(
          height: 4,
        ),
        AppWidget.boldText(
            context, tile, 12, const Color(0xFF282828), FontWeight.w600),
        AppWidget.normalText(
            context, subtitle, 12, const Color(0xFF282828), FontWeight.w400)
      ],
    );
  }
}
