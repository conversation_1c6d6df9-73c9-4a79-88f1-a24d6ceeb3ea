import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/show_detail_regis_MR.dart';

import '../../../component/alert.dart';
import '../../../component/api.dart';
import '../../../component/loader.dart';
import '../../../component/url.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class RegisBookbankMRPage extends StatefulWidget {
  const RegisBookbankMRPage({Key? key}) : super(key: key);

  @override
  State<RegisBookbankMRPage> createState() => _RegisBookbankMRPageState();
}

class _RegisBookbankMRPageState extends State<RegisBookbankMRPage> {
  @override
  final TextEditingController _bookbankName = TextEditingController();
  final TextEditingController _bookbankID = TextEditingController();

  List<String> ListBank = [
    'ธนาคารกสิกรไทย',
    'ธนาคารกรุงเทพ',
    'ธนาคารกรุงไทย',
    'ธนาคารกรุงศรีอยุธยา',
    'ธนาคารไทยพาณิชย์',
    'ธนาคารทหารไทยธนาชาต',
    'ธนาคารออมสิน',
    'ธนาคารธ.ก.ส.',
    'ธนาคารซีไอเอ็มบี',
    'ธนาคารยูโอบี',
    'ธนาคารเกียรตินาคินภัทร',
    'ธนาคาซิตี้แบงก์',
    'ธนาคารอาคารสงเคราะห์',
    'ธนาคารทิสโก้',
  ];
  String? bankSelect;
  bool openBank = false;

  bool openButton = false;

  final profileCtl = Get.put(ProfileController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Scaffold(
              body: Stack(
                children: <Widget>[
                  Container(
                    width: _width,
                    height: _height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: _width,
                    height: 125.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xFFFFB100).withOpacity(1),
                          const Color(0xFFFFC700).withOpacity(0.7),
                          const Color(0xFFFFC700).withOpacity(0.4),
                          const Color(0xFFFFC700).withOpacity(0),
                        ],
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      Expanded(
                        child: Container(
                          margin: EdgeInsets.only(
                            left: 23.w,
                            top: 12.h,
                          ),
                          width: _width,
                          child: Stack(
                            children: [
                              Column(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 12.h),
                                    width: 400.w,
                                    height: 80.h,
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.start,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              Navigator.pushReplacement(context,
                                                  MaterialPageRoute(builder: (context) => const ShowDetailRegisMR()));
                                            },
                                            child: SizedBox(
                                              height: 30.h,
                                              child: Icon(
                                                Icons.arrow_back_ios_new,
                                                color: const Color(0xFF282828),
                                                size: 15.w,
                                              ),
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                                top: 27.h, left: 16.w),
                                            width: 185.w,
                                            height: 50.h,
                                            child: RichText(
                                              text: TextSpan(children: [
                                                TextSpan(
                                                  text: 'ข้อมูลบัญชีธนาคาร',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: 'Prompt-Medium',
                                                    color: const Color(0xFF282828),
                                                    fontSize: 14.w,
                                                  ),
                                                ),
                                                TextSpan(
                                                  text:
                                                  '\nกรุณาใส่รายละเอียดข้อมูลให้ครบถ้วน',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w400,
                                                    fontFamily: 'Prompt',
                                                    color: const Color(0xFF664701),
                                                    fontSize: 11.w,
                                                  ),
                                                ),
                                              ]),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      top: 26.h,
                                      right: 24.w,
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'ข้อมูลบัญชีธนาคาร',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Colors.black,
                                            fontSize: 12.w,
                                          ),
                                        ),
                                        Container(
                                            child: selectBank(context, ListBank)),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'เลขบัญชีธนาคาร',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              controller: _bookbankID,
                                              keyboardType:
                                              TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter.allow(
                                                  AppService.number15RegExp(),
                                                ),
                                              ],
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText:
                                                'กรอกหมายเลขบัญชีธนาคาร',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070)
                                                      .withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder:
                                                UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: const Color(0xFF895F00)
                                                          .withOpacity(0.5)),
                                                ),
                                                focusedBorder:
                                                const UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'ชื่อบัญชีธนาคาร',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              onChanged: (value) {
                                                setState(() {
                                                  showButton(bankSelect);
                                                });
                                              },
                                              controller: _bookbankName,
                                              inputFormatters: [
                                                FilteringTextInputFormatter.allow(
                                                  AppService.thaiAZ100RegExp(),
                                                ),
                                                FilteringTextInputFormatter.deny(AppService.deny()),
                                              ],
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText: 'กรอกชื่อบัญชีธนาคาร',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070)
                                                      .withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder:
                                                UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: const Color(0xFF895F00)
                                                          .withOpacity(0.5)),
                                                ),
                                                focusedBorder:
                                                const UnderlineInputBorder(
                                                  borderSide: BorderSide(
                                                      color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              Positioned(
                                top: 30.h,
                                right: -10.w,
                                child: Image.asset(
                                  'assets/image/MR/LadyWhite.png',
                                  opacity: const AlwaysStoppedAnimation(.6),
                                  width: 137.w,
                                  height: 127.h,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      openButton != false
                          ? Padding(
                        padding: EdgeInsets.only(bottom: 30.h),
                        child: InkWell(
                          onTap: () {
                            savedata();
                          },
                          child: Container(
                              margin: EdgeInsets.only(top: 30.h),
                              width: 323.w,
                              height: 40.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.h),
                                boxShadow: const [
                                  BoxShadow(
                                    color: Colors.black45,
                                    offset: Offset(1, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                                color: const Color(0xFF282828),
                              ),
                              child: Center(
                                child: Text(
                                  'ดำเนินการต่อ',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Colors.white,
                                    fontSize: 14.h,
                                  ),
                                ),
                              )),
                        ),
                      )
                          : Container(),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }

  selectBank(context, items) {
    return GestureDetector(
      onTap: () {
        setState(() {
          openBank = true;
        });
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (bankSelect != null) {
              selectTempIndex = items.indexOf(bankSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          setState(() {
                            openBank = false;
                          });
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {},
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกธนาคาร',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          openBank = false;
                          setState(() {
                            bankSelect = items[selectTempIndex];
                            showButton(bankSelect);
                          });
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 150,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            fontSize: 13.w,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.4,
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: Container(
        width: 375.w,
        padding: EdgeInsets.symmetric(
          vertical: 10.h,
        ),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFF895F00).withOpacity(0.5),
              width: 1.0,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            bankSelect == null
                ? SizedBox(
              width: 310.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'เลือกธนาคาร',
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 12.w,
                    ),
                  ),
                  openBank == false
                      ? Icon(
                    Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(
                    Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            )
                : SizedBox(
              width: 310.w,
              // height: 50.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    bankSelect!,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF664701),
                      fontSize: 12.w,
                    ),
                  ),
                  openBank == false
                      ? Icon(
                    Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(
                    Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

//เลือกธนาคาร

//โชว์ปุ่ม
  showButton(bankSelect) {
    if (_bookbankID.text != "" &&
        _bookbankName.text != "") {
      setState(() {
        openButton = true;
      });
    }
  }
//โชว์ปุ่ม

//savedata
  savedata() async {
    try{
      if(
      bankSelect != "" &&
          _bookbankID.text != "" &&
          _bookbankName.text != ""
      ){
        profileCtl.profile.value.bankNameMR = bankSelect;
        profileCtl.profile.value.bookBankNameMR = _bookbankName.text;
        profileCtl.profile.value.bookBankNoMR = _bookbankID.text;
        AppLoader.loader(context);
        Map data = {
          'MrCode': profileCtl.profile.value.mrCode,
          "bankName" : bankSelect,
          "bookBankNo" : _bookbankID.text,
          "bookBankName" : _bookbankName.text,
        };
        final response = await AppApi.post(AppUrl.updateInformationBookbank, data);
        if (response['status'] == 200) {
          AppLoader.dismiss(context);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ShowDetailRegisMR(),
            ),
          );
        } else {
          AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
          AppLoader.dismiss(context);
        }
      } else {
        print('กรอกข้อมูลไม่ครบ');
      }
    }catch(e){
      print(e);
    }
  }
//savedata
}
