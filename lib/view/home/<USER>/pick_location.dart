import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';


class PickLocationPage extends StatefulWidget {
  const PickLocationPage({Key? key}) : super(key: key);

  @override
  State<PickLocationPage> createState() => _PickLocationPageState();
}

class _PickLocationPageState extends State<PickLocationPage> {
  final SecureStorage secureStorage = SecureStorage();

  TextEditingController editController = TextEditingController();

  String? phone;

  String mapsKey = "AIzaSyAmIZ39-kRoLGdQ7uqdyctMQGQgizSGyLs";
  LatLng? _currentPosition;
  final Completer<GoogleMapController> mapController = Completer();
  final Set<Marker> markers = Set();
  List<NearbyPlace> nearbyPlaces = [];
  bool hasSearchTerm = false;
  bool hasSearchEntry = false;
  Timer? debouncer;
  String previousSearchTerm = '';
  String sessionToken = Uuid().generateV4();



  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "PickLocation");
    getData();
  }

  @override
  void dispose() {
    editController.removeListener(onSearchInputChange);
    editController.dispose();
    super.dispose();
  }

  getData() async {
    await _getCurrentLocation();
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);

          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 80),
              width: Get.width,
              height: Get.height,
              color: Colors.white,
            ),
            Column(
              children: [
                Container(
                  width: Get.width,
                  height: 60,
                  margin: const EdgeInsets.only(top: 60),
                  padding: EdgeInsets.only(
                    // bottom: 10,
                    top: 10,
                    left: Get.width < 500 ? 18 : 54,
                    right: Get.width < 500 ? 18 : 54,
                  ),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(
                        15,
                      ),
                      topRight: Radius.circular(
                        15,
                      ),
                    ),
                    color: Color(0xFFF9F9F9),
                  ),
                  child: Row(
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: SizedBox(
                            height: 50,
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.arrow_back_ios,
                                  color: Color(0xFFFFB100),
                                  size: 16,
                                ),
                                AppWidget.normalText(
                                    context,
                                    "กลับ",
                                    13,
                                    Colors.black,
                                    FontWeight.w400)
                              ],
                            ),
                          )
                      ),
                      Container(
                        // width: 50,
                          height: 50,
                          alignment: Alignment.center,
                          child: AppWidget.boldText(
                              context,
                              "ตำแหน่งที่ตั้ง",
                              14,
                              Colors.black,
                              FontWeight.w400)
                      ),
                      const SizedBox(
                        width: 50,
                        height: 50,
                      )
                    ],
                  ),
                ),
                buildContent()
              ],
            ),
          ],
        ),
      ),
    );
  }

  buildContent(){
    return Column(
      children: [
        SizedBox(
          width: Get.width,
          height: Get.height * 0.8,
          child: GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _currentPosition!,
              zoom: 15,
            ),
            myLocationButtonEnabled: true,
            myLocationEnabled: true,
            zoomControlsEnabled: false,
            compassEnabled: true,
            onMapCreated: onMapCreated,
            onTap: (latLng) {
              moveToLocation('nearby', '', latLng, null);
            },
            markers: markers,
          ),
        ),
        // SizedBox(
        //   width: 1.sw,
        //   height: 0.4.sh,
        //   child: Column(
        //     children: [
        //       Container(
        //         margin: EdgeInsets.only(
        //           top: 0.02.sh,
        //           left: 0.02.sw,
        //           right: 0.02.sw,
        //         ),
        //         padding: EdgeInsets.only(
        //             left: 0.02.sw,
        //             right: 0.02.sw,
        //         ),
        //         height: 0.06.sh,
        //         decoration: BoxDecoration(
        //             borderRadius: BorderRadius.circular(16),
        //             color: const Color(0xFF282828)
        //         ),
        //         child: Row(
        //           children: <Widget>[
        //             const Icon(
        //               Icons.search,
        //               color: Colors.white,
        //             ),
        //             const SizedBox(width: 8),
        //             Expanded(
        //               child: TextField(
        //                 style: const TextStyle(
        //                   fontFamily: 'Prompt',
        //                   color: Colors.white,
        //                   fontWeight: FontWeight.w400,
        //                   letterSpacing: 0.2,
        //                 ),
        //                 decoration: const InputDecoration(
        //                     hintText: "Search",
        //                     hintStyle: TextStyle(
        //                       fontFamily: 'Prompt',
        //                       color: Colors.white,
        //                       fontWeight: FontWeight.w400,
        //                       letterSpacing: 0.2,
        //                     ),
        //                     border: InputBorder.none),
        //                 controller: editController,
        //                 onChanged: (value) {
        //                   setState(() {
        //                     hasSearchEntry =
        //                         value.isNotEmpty;
        //                   });
        //                 },
        //               ),
        //             ),
        //             const SizedBox(width: 8),
        //             if (hasSearchEntry)
        //               GestureDetector(
        //                 child: const Icon(Icons.clear, color: Colors.white,),
        //                 onTap: () {
        //                   editController.clear();
        //                   setState(() {
        //                     hasSearchEntry = false;
        //                   });
        //                 },
        //               ),
        //           ],
        //         ),
        //       ),
        //       Expanded(
        //         child: ListView(
        //           padding: EdgeInsets.zero,
        //           children: nearbyPlaces
        //               .map(
        //                 (it) => Material(
        //               color: Colors.white,
        //               clipBehavior: Clip.antiAlias,
        //               child: InkWell(
        //                 splashColor: const Color(0xFFFFB100),
        //                 onTap: () async {
        //                   moveToLocation(
        //                     it.type,
        //                     it.name,
        //                     it.latLng!,
        //                     it.placeId,
        //                   );
        //
        //                   if (it.type == "nearby") {
        //                     Future.delayed(
        //                         const Duration(
        //                             milliseconds: 150),
        //                             () async {
        //                           var result = await AppAlert
        //                               .showConfirm(
        //                               context,
        //                               'ยืนยันตำแหน่ง',
        //                               'ต้องการเลือกตำแหน่งนี้',
        //                               'ตกลง');
        //                           if (result == true) {
        //                             setLocation(context, it.name, it.latLng!);
        //                           }
        //                         });
        //                   }
        //                 },
        //                 child: Container(
        //                   margin: EdgeInsets.only(
        //                       left: 0.04.sw,
        //                     right: 0.04.sw,
        //                   ),
        //                   child: Column(
        //                     children: <Widget>[
        //                       Row(
        //                         children: <Widget>[
        //                           Image.network(
        //                             it.icon!,
        //                             width: 0.06.sw,
        //                           ),
        //                           SizedBox(
        //                             width: 0.03.sw,
        //                           ),
        //                           Expanded(
        //                             child: Column(
        //                               crossAxisAlignment: CrossAxisAlignment.start,
        //                               children: <Widget>[
        //                                 SizedBox(
        //                                   height:
        //                                   0.02.sh,
        //                                 ),
        //                                 Text(
        //                                   it.name != null
        //                                   ? it.name!.length > 32
        //                                       ? '${it.name!.substring(0, 32)}...'
        //                                       : it.name!
        //                                   : "",
        //                                   style: TextStyle(
        //                                     fontSize:
        //                                     20.sp,
        //                                     color: const Color(0xFF282828),
        //                                     letterSpacing:
        //                                     0.4,
        //                                     shadows: <
        //                                         Shadow>[
        //                                       Shadow(
        //                                         offset: const Offset(0, 1),
        //                                         blurRadius: 1.0,
        //                                         color: const Color(0xFF000000).withOpacity(0.1),
        //                                       ),
        //                                     ],
        //                                   ),
        //                                 ),
        //                                 Text(
        //                                   it.vicinity != null
        //                                     ? it.vicinity!.length > 44 ? '${it.vicinity!.substring(0, 44)}...' : it.vicinity!
        //                                     : "",
        //                                   style: TextStyle(
        //                                     fontSize: 16.sp,
        //                                     color: const Color(0xFF707070),
        //                                     letterSpacing: 0.4,
        //                                     shadows: <Shadow>[
        //                                       Shadow(
        //                                         offset: const Offset(0, 1),
        //                                         blurRadius: 1.0,
        //                                         color: const Color(0xFF000000).withOpacity(0.05),
        //                                       ),
        //                                     ],
        //                                   ),
        //                                 ),
        //                               ],
        //                             ),
        //                           )
        //                         ],
        //                       ),
        //                       SizedBox(
        //                         height: 0.02.sh,
        //                       ),
        //                       Container(
        //                         width: 1.sw,
        //                         height: 1.2,
        //                         color: const Color(0xFF707070).withOpacity(0.06),
        //                       ),
        //                     ],
        //                   ),
        //                 ),
        //               ),
        //             ),
        //           ).toList(),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  _getCurrentLocation() async {
    try {
      try {
        Position position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high);
        print(position.toString());
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          print(_currentPosition);
        });
      } on PlatformException catch (e) {
        if (e.code == 'PERMISSION_DENIED') {
          AppService.sendError('Permission denied to getCurrentLocation',
              'Pick Location In Home Service');
          print('Permission denied');

          Navigator.pop(context);
        } else {
          AppService.sendError('PlatformException to getCurrentLocation',
              'Pick Location In Home Service');

          Navigator.pop(context);
        }
      }
    } catch (e) {
      AppService.sendError('PlatformException to getCurrentLocation',
          'Pick Location In Home Service');
      Navigator.pop(context);
    }
  }

  void onMapCreated(GoogleMapController controller) {
    mapController.complete(controller);
    moveToLocation('nearby', '', _currentPosition!, null);
  }

  void moveToLocation(type, name, LatLng latLng, placeId) async {
    print(_currentPosition);
    mapController.future.then((controller) {
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: latLng,zoom: 14),
        ),
      );
      print(_currentPosition);
    });

    setMarker(name, latLng);

    reverseGeocodeLatLng(latLng);

    getNearbyPlaces(type, latLng, placeId);
  }

  void setMarker(name, LatLng latLng) {
    print('setMarker');
    setState(() {
      markers.clear();
      markers.add(
        Marker(
            markerId: const MarkerId("selected-location"),
            position: latLng,
            onTap: () async {
              var result = await AppAlert.showConfirm(
                  context, 'ยืนยันตำแหน่ง', 'ต้องการเลือกตำแหน่งนี้', 'ตกลง');

              if (result == true) {
                setLocation(context, name, latLng);
              }
            }),
      );
    });
  }

  static setLocation(context, name, LatLng latLong) {
    if (name == '' || name == null) {
      name = 'ตำแหน่งที่เลือก';
    }
    String latLng =
        "${latLong.latitude},${latLong.longitude}";

    List<String> data = [name, latLng];

    Navigator.of(context).pop(data);
  }

  void reverseGeocodeLatLng(LatLng latLng) async {
    try {
      Map<String, String> queryParams = {
        'latlng': '${latLng.latitude},${latLng.longitude}',
        'key': mapsKey,
      };
      print(queryParams);

      final uri = Uri.https(
          'maps.googleapis.com', '/maps/api/place/geocode/json', queryParams);

      final response = await http.get(uri);

      if (response.statusCode != 200) {
        throw Error();
      }

      final responseJson = jsonDecode(response.body);

      if (responseJson['results'] == null) {
        throw Error();
      }

      final result = responseJson['results'][0];

    } catch (e) {
      print(e);
    }
  }

  void getNearbyPlaces(type, latLng, placeId) async {

    if (type == 'nearby') {
      try {
        Map<String, String> queryParams = {
          'location': '${latLng.latitude},${latLng.longitude}',
          'key': mapsKey,
          'radius': "150",
        };

        final uri = Uri.https('maps.googleapis.com',
            '/maps/api/place/nearbysearch/json', queryParams);
        final response = await http.get(uri);

        if (response.statusCode != 200) {
          throw Error();
        }

        final responseJson = jsonDecode(response.body);

        if (responseJson['results'] == null) {
          throw Error();
        }

        nearbyPlaces.clear();

        for (Map<String, dynamic> item in responseJson['results']) {
          final nearbyPlace = NearbyPlace()
            ..name = item['name']
            ..vicinity = item['vicinity']
            ..icon = item['icon']
            ..latLng = LatLng(item['geometry']['location']['lat'],
                item['geometry']['location']['lng'])
            ..type = 'nearby';

          nearbyPlaces.add(nearbyPlace);
        }

        setState(() {
          hasSearchTerm = false;
        });
      } catch (e) {
        if (kDebugMode) {
          print(e);
        }
      }
    } else if (type == 'search') {
      decodeAndSelectPlace(placeId);
    }
  }

  void decodeAndSelectPlace(String placeId) async {
    try {
      Map<String, String> queryParams = {
        'placeid': placeId,
        'key': mapsKey,
      };

      final uri = Uri.https(
          'maps.googleapis.com', '/maps/api/place/details/json', queryParams);

      final response = await http.get(uri);
      if (response.statusCode != 200) {
        throw Error();
      }

      final responseJson = jsonDecode(response.body);

      if (responseJson['result'] == null) {
        throw Error();
      }

      final location = responseJson['result']['geometry']['location'];
      moveToLocation(
          'nearby', '', LatLng(location['lat'], location['lng']), null);
    } catch (e) {
      print(e);
    }
  }

  void onSearchInputChange() {
    if (editController.text.isEmpty) {
      debouncer?.cancel();
      searchPlace(editController.text);

      return;
    }

    if (debouncer?.isActive ?? false) {
      debouncer!.cancel();
    }

    debouncer = Timer(const Duration(milliseconds: 500), () {
      searchPlace(editController.text);
    });
  }

  void searchPlace(String place) {
    if (place == previousSearchTerm) {
      return;
    }

    previousSearchTerm = place;

    if (context == null) {
      return;
    }

    setState(() {
      hasSearchTerm = place.isNotEmpty;
    });

    if (place.isEmpty) {
      return;
    }

    autoCompleteSearch(place);
  }

  void autoCompleteSearch(String place) async {
    try {
      place = place.replaceAll(" ", "+");

      Map<String, String> queryParams = {
        'input': '${place}',
        'key': mapsKey,
        'sessiontoken': sessionToken,
      };

      final uri = Uri.https('maps.googleapis.com',
          '/maps/api/place/autocomplete/json', queryParams);
      final response = await http.get(uri);

      if (response.statusCode != 200) {
        throw Error();
      }

      final responseJson = jsonDecode(response.body);

      if (responseJson['predictions'] == null) {
        throw Error();
      }

      List<dynamic> predictions = responseJson['predictions'];

      nearbyPlaces.clear();

      if (predictions.isEmpty) {
        final searchPlace = NearbyPlace()
          ..placeId = ''
          ..name = 'No Result Found.'
          ..vicinity = ''
          ..icon = "https://icon-icons.com/icons2/1509/PNG/32/search_104498.png"
          ..type = '';

        nearbyPlaces.add(searchPlace);
      } else {
        for (Map<String, dynamic> item in predictions) {
          final searchPlace = NearbyPlace()
            ..placeId = item['place_id']
            ..name = item['description']
            ..vicinity = ''
            ..icon =
                "https://icon-icons.com/icons2/1509/PNG/32/search_104498.png"
            ..type = 'search';

          nearbyPlaces.add(searchPlace);
        }

        setState(() {
          hasSearchTerm = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
}

class NearbyPlace {
  String? name;
  String? icon;
  LatLng? latLng;
  String? vicinity;
  String? placeId;
  String? type;
}


class Uuid {
  final Random _random = Random();

  String generateV4() {
    int special = 8 + _random.nextInt(4);

    return '${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}-'
        '${_bitsDigits(16, 4)}-'
        '4${_bitsDigits(12, 3)}-'
        '${_printDigits(special, 1)}${_bitsDigits(12, 3)}-'
        '${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}${_bitsDigits(16, 4)}';
  }

  String _bitsDigits(int bitCount, int digitCount) =>
      _printDigits(_generateBits(bitCount), digitCount);

  int _generateBits(int bitCount) => _random.nextInt(1 << bitCount);

  String _printDigits(int value, int count) =>
      value.toRadixString(16).padLeft(count, '0');
}
