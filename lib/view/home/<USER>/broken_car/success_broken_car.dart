import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/broken_car_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';

class SuccessBrokenCarPage extends StatefulWidget {
  const SuccessBrokenCarPage({Key? key}) : super(key: key);

  @override
  State<SuccessBrokenCarPage> createState() => _SuccessBrokenCarPageState();
}

class _SuccessBrokenCarPageState extends State<SuccessBrokenCarPage> {

  final brokenCarCtl = Get.put(BrokenCarController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFB100).withOpacity(0.6),
      body: Stack(
        children: [
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
            child: SizedBox(
              width: Get.width,
              height: Get.height,
            ),
          ),
          SizedBox(
            width: Get.width,
            height: Get.height,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset("assets/image/service/borkenCarMascot.png", width: 178,),
                const SizedBox(
                  height: 10,
                ),
                AppWidget.boldTextS(
                    context,
                    "ขอบคุณที่ใช้บริการ",
                    14,
                    const Color(0xFFFFFFFF),
                    FontWeight.w500),
                const SizedBox(
                  height: 10,
                ),
                AppWidget.normalTextS(
                    context,
                    "ทีมงานได้รับข้อมูลแจ้งปัญหาของคุณ เป็นที่เรียบร้อย",
                    12,
                    const Color(0xFFFFFFFF),
                    FontWeight.w400),
                AppWidget.normalTextS(
                    context,
                    "หากกรณีสายไม่ว่าง ทีมงานจะทำการติดต่อกลับ",
                    12,
                    const Color(0xFFFFFFFF),
                    FontWeight.w400),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppWidget.normalTextS(
                        context,
                        "หาคุณทันที",
                        12,
                        const Color(0xFFFFFFFF),
                        FontWeight.w400),
                    AppWidget.boldTextS(
                        context,
                        " ภายใน 15-30 นาที",
                        14,
                        const Color(0xFFFFFFFF),
                        FontWeight.w500),
                  ],
                ),
                const SizedBox(
                  height: 30
                ),
                InkWell(
                  onTap: (){
                    AppService.callPhone(brokenCarCtl.technicianPhone.value);
                  },
                  child: Container(
                    width: Get.width,
                    height: 53,
                    margin: const EdgeInsets.only(
                      left: 20,
                      right: 20,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(15),
                        topLeft: Radius.circular(15),
                        bottomRight: Radius.circular(15),
                        bottomLeft: Radius.circular(15),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0.0, 1.0],
                        colors: [
                          const Color(0xFF000000).withOpacity(0.7),
                          const Color(0xFF000000),
                        ],
                      ),
                      border: Border.all(width: 1, color: const Color(0xFF895F00)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          offset: const Offset(1, 2),
                          blurRadius: 3,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppWidget.normalText(
                            context,
                            "โทรหาช่าง",
                            15,
                            const Color(0xFFFFFFFF),
                            FontWeight.w400)
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                TextButton(
                    onPressed: (){
                      Get.offAll(() => const HomeNavigator());
                    },
                    child: AppWidget.boldText(
                        context,
                        "ปิดหน้านี้",
                        15,
                        const Color(0xFF000000),
                        FontWeight.w500))
              ],
            ),
          )
        ],
      ),
    );
  }
}
