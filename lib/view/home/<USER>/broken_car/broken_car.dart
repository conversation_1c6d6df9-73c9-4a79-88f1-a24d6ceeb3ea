import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/broken_car_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pick_location.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/broken_car/success_broken_car.dart';
import 'package:permission_handler/permission_handler.dart';

class BrokenCarPage extends StatefulWidget {
  const BrokenCarPage({Key? key}) : super(key: key);

  @override
  State<BrokenCarPage> createState() => _BrokenCarPageState();
}

class _BrokenCarPageState extends State<BrokenCarPage> {
  final SecureStorage secureStorage = SecureStorage();
  final TextEditingController _phoneController = TextEditingController();

  var token;

  String? locationName;
  String? locationLatLng;
  bool? locationStatus = false;

  final profileCtl = Get.put(ProfileController());

  RxString carType = "".obs;

  final brokenCarCtl = Get.put(BrokenCarController());

  RxBool statusBigCar = false.obs;
  RxBool statusSmallCar = false.obs;
  RxBool statusButton = true.obs;
  RxBool isLoading = true.obs;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "BrokenCar");
    getData();
  }

  getData() async {
    token = await secureStorage.readSecureData("accessToken");
    profileCtl.profile.value.mobile != null
        ? _phoneController.text = profileCtl.profile.value.mobile.toString()
        : "";
    isLoading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Obx(() {
      if (isLoading.value == true) {
        return AppLoader.loaderWaitPage(context);
      } else {
        return Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.0, 1.0],
                  colors: [
                    Color(0xFFF5F5F5),
                    Color(0xFFF5F5F5),
                  ],
                ),
              ),
            ),
            Column(
              children: [
                buildAppBar(),
                const SizedBox(
                  height: 10,
                ),
                buildHeadContent(),
                const SizedBox(
                  height: 10,
                ),
                buildContent(),
              ],
            )
          ],
        );
      }
    }));
  }

  buildAppBar() {
    return Container(
      margin: EdgeInsets.only(
        top: Get.width < 500 ? 50 : 80,
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(15),
                    topLeft: Radius.circular(15),
                    bottomRight: Radius.circular(15),
                    bottomLeft: Radius.circular(15),
                  ),
                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
              child: const Icon(
                Icons.arrow_back_ios_new,
                size: 18,
                color: Color(0xFFFFB100),
              ),
            ),
          ),
          AppWidget.boldTextS(context, "รถเสีย", Get.width < 500 ? 16 : 18, const Color(0xFF2B1710),
              FontWeight.w500),
          const SizedBox(
            width: 36,
            height: 36,
          ),
        ],
      ),
    );
  }

  buildHeadContent() {
    return Container(
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppWidget.boldText(context, "แจ้งปัญหาสอบถามเกี่ยวกับเรื่องรถ",
              Get.width < 500 ? 12 : 14, const Color(0xFF895F00), FontWeight.w500),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '* ',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: const Color(0xFFCA0E1E),
                        fontSize: Get.width < 500 ? 12 : 14,
                      ),
                    ),
                    TextSpan(
                      text: 'กรุณาเลือกรายการด้านล่าง เพื่อติดต่อ',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: const Color(0xFF282828),
                        fontSize: Get.width < 500 ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Image.asset(
            'assets/image/service/menu_emer.png',
            width: 72,height: 44,
          )
        ],
      ),
    );
  }

  buildContent() {
    return Container(
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppWidget.boldText(context, "เบอร์โทร", Get.width < 500 ? 14 : 16,
              const Color(0xFF282828), FontWeight.w500),
          const SizedBox(
            height: 10,
          ),
          Container(
            alignment: Alignment.center,
            width: Get.width,
            height: Get.width < 500 ? 50 : 60,
            decoration: const BoxDecoration(
              color: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(15),
                topLeft: Radius.circular(15),
                bottomRight: Radius.circular(15),
                bottomLeft: Radius.circular(15),
              ),
            ),
            child: TextField(
              onChanged: checkData,
              textAlign: TextAlign.center,
              controller: _phoneController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  AppService.number10RegExp(),
                ),
              ],
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Prompt-Medium',
                color: const Color(0xFF282828),
                fontSize: Get.width < 500 ? 14 : 16,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: 'กรอกหมายเลขโทรศัพท์',
                hintStyle: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF707070).withOpacity(0.5),
                  fontSize: Get.width < 500 ? 14 : 16,
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          // AppWidget.boldText(context, "ระบุตำแหน่งรถของคุณ", Get.width < 500 ? 14 : 16,
          //     const Color(0xFF282828), FontWeight.w500),
          // const SizedBox(
          //   height: 10,
          // ),
          // InkWell(
          //   onTap: () async {
          //     await requestLocationPermission();
          //     // await Permission.locationWhenInUse.request();
          //     // final PermissionStatus status = await Permission.location.request();
          //     List<String>? result = await showGeneralDialog(
          //       barrierLabel: "showGeneralDialog",
          //       barrierDismissible: true,
          //       barrierColor: Colors.black.withOpacity(0.6),
          //       transitionDuration: const Duration(milliseconds: 300),
          //       context: context,
          //       pageBuilder: (context, _, __) {
          //         return const PickLocationPage();
          //       },
          //       transitionBuilder: (_, animation1, __, child) {
          //         return SlideTransition(
          //           position: Tween(
          //             begin: const Offset(0, 1),
          //             end: const Offset(0, 0),
          //           ).animate(animation1),
          //           child: child,
          //         );
          //       },
          //     );
          //     if (result != null) {
          //       //เลือกตำแหน่งแล้ว
          //       setState(() {
          //         locationName = result[0];
          //         locationLatLng = result[1];
          //         locationStatus = true;
          //         checkData("");
          //       });
          //     }
          //   },
          //   child: locationLatLng != null
          //       ? Container(
          //           width: Get.width,
          //           height: Get.width < 500 ? 50 : 60,
          //           decoration: BoxDecoration(
          //             borderRadius: const BorderRadius.only(
          //               topRight: Radius.circular(15),
          //               topLeft: Radius.circular(15),
          //               bottomRight: Radius.circular(15),
          //               bottomLeft: Radius.circular(15),
          //             ),
          //             gradient: const LinearGradient(
          //               begin: Alignment.topCenter,
          //               end: Alignment.bottomCenter,
          //               stops: [0.0, 1.0],
          //               colors: [
          //                 Color(0xFF664701),
          //                 Color(0xFF1D1400),
          //               ],
          //             ),
          //             border:
          //                 Border.all(width: 1, color: const Color(0xFF895F00)),
          //             boxShadow: [
          //               BoxShadow(
          //                 color: Colors.grey.withOpacity(0.2),
          //                 offset: const Offset(1, 2),
          //                 blurRadius: 3,
          //               ),
          //             ],
          //           ),
          //           child: Row(
          //             mainAxisAlignment: MainAxisAlignment.center,
          //             children: [
          //               const Icon(
          //                 Icons.my_location_rounded,
          //                 size: 20,
          //                 color: Color(0xFFFFB100),
          //               ),
          //               const SizedBox(
          //                 width: 20,
          //               ),
          //               locationName != null
          //                   ? AppWidget.normalText(context, locationName, 15,
          //                       const Color(0xFFFFFFFF), FontWeight.w400)
          //                   : AppWidget.normalText(
          //                       context,
          //                       locationLatLng,
          //                       Get.width < 500 ? 16 : 18,
          //                       const Color(0xFFFFFFFF),
          //                       FontWeight.w400),
          //             ],
          //           ),
          //         )
          //       : Container(
          //           width: Get.width,
          //           height: Get.width < 500 ? 50 : 60,
          //           decoration: BoxDecoration(
          //             borderRadius: const BorderRadius.only(
          //               topRight: Radius.circular(15),
          //               topLeft: Radius.circular(15),
          //               bottomRight: Radius.circular(15),
          //               bottomLeft: Radius.circular(15),
          //             ),
          //             gradient: LinearGradient(
          //               begin: Alignment.topCenter,
          //               end: Alignment.bottomCenter,
          //               stops: const [0.0, 1.0],
          //               colors: [
          //                 const Color(0xFFF3F3F3),
          //                 const Color(0xFFFFFFFF).withOpacity(0.6),
          //               ],
          //             ),
          //             border:
          //                 Border.all(width: 1, color: const Color(0xFF895F00)),
          //             boxShadow: [
          //               BoxShadow(
          //                 color: Colors.grey.withOpacity(0.2),
          //                 offset: const Offset(1, 2),
          //                 blurRadius: 3,
          //               ),
          //             ],
          //           ),
          //           child: Row(
          //             mainAxisAlignment: MainAxisAlignment.center,
          //             children: [
          //               const Icon(
          //                 Icons.my_location_rounded,
          //                 size: 20,
          //               ),
          //               const SizedBox(
          //                 width: 20,
          //               ),
          //               AppWidget.normalText(
          //                   context,
          //                   "เพิ่มตำแหน่งที่ตั้งของคุณ",
          //                   Get.width < 500 ? 14 : 16,
          //                   const Color(0xFF895F00),
          //                   FontWeight.w400),
          //             ],
          //           ),
          //         ),
          // ),
          // const SizedBox(
          //   height: 20,
          // ),
          // AppWidget.boldText(context, "เลือกประเภทรถ", Get.width < 500 ? 14 : 16,
          //     const Color(0xFF282828), FontWeight.w500),
          // const SizedBox(
          //   height: 10,
          // ),
          // Row(
          //   children: [
          //     InkWell(
          //       onTap: () async {
          //         statusBigCar.value = false;
          //         statusSmallCar.value = true;
          //         carType.value = "รถเล็ก";
          //         brokenCarCtl.technicianPhone.value = "0957606184";
          //         checkData("");
          //       },
          //       child: Obx(() => statusSmallCar.value == false
          //           ? Container(
          //               width: Get.width < 500 ? 100 : 150,
          //               height: Get.width < 500 ? 50 : 55,
          //               decoration: const BoxDecoration(
          //                 borderRadius: BorderRadius.only(
          //                   topRight: Radius.circular(15),
          //                   topLeft: Radius.circular(15),
          //                   bottomRight: Radius.circular(15),
          //                   bottomLeft: Radius.circular(15),
          //                 ),
          //                 color: Color(0xFFFFFFFF),
          //               ),
          //               child: Center(
          //                 child: AppWidget.boldText(context, "รถเล็ก", Get.width < 500 ? 14 : 16,
          //                     const Color(0xFFBCBCBC), FontWeight.w400),
          //               ),
          //             )
          //           : Container(
          //               width: Get.width < 500 ? 100 : 150,
          //               height:  Get.width < 500 ? 50 : 55,
          //               decoration: BoxDecoration(
          //                 borderRadius: const BorderRadius.only(
          //                   topRight: Radius.circular(15),
          //                   topLeft: Radius.circular(15),
          //                   bottomRight: Radius.circular(15),
          //                   bottomLeft: Radius.circular(15),
          //                 ),
          //                 gradient: const LinearGradient(
          //                   begin: Alignment.topCenter,
          //                   end: Alignment.bottomCenter,
          //                   stops: [0.0, 0.5, 1.0],
          //                   colors: [
          //                     Color(0xFFFFC700),
          //                     Color(0xFFFFB100),
          //                     Color(0xFFFF9900),
          //                   ],
          //                 ),
          //                 border: Border.all(
          //                     width: 2, color: const Color(0xFF000000)),
          //                 boxShadow: [
          //                   BoxShadow(
          //                     color: Colors.grey.withOpacity(0.2),
          //                     offset: const Offset(1, 2),
          //                     blurRadius: 3,
          //                   ),
          //                 ],
          //               ),
          //               child: Center(
          //                 child: AppWidget.boldText(context, "รถเล็ก", Get.width < 500 ? 14 : 16,
          //                     const Color(0xFFFFFFFF), FontWeight.w400),
          //               ),
          //             )),
          //     ),
          //     const SizedBox(
          //       width: 10,
          //     ),
          //     InkWell(
          //         onTap: () {
          //           statusBigCar.value = true;
          //           statusSmallCar.value = false;
          //           carType.value = "รถใหญ่";
          //           brokenCarCtl.technicianPhone.value = "0957606184";
          //           checkData("");
          //         },
          //         child: Obx(() => statusBigCar.value == false
          //             ? Container(
          //                 width: Get.width < 500 ? 100 : 150,
          //                 height:  Get.width < 500 ? 50 : 55,
          //                 decoration: const BoxDecoration(
          //                   borderRadius: BorderRadius.only(
          //                     topRight: Radius.circular(15),
          //                     topLeft: Radius.circular(15),
          //                     bottomRight: Radius.circular(15),
          //                     bottomLeft: Radius.circular(15),
          //                   ),
          //                   color: Color(0xFFFFFFFF),
          //                 ),
          //                 child: Center(
          //                   child: AppWidget.boldText(context, "รถใหญ่", Get.width < 500 ? 14 : 16,
          //                       const Color(0xFFBCBCBC), FontWeight.w400),
          //                 ),
          //               )
          //             : Container(
          //                 width: Get.width < 500 ? 100 : 150,
          //                 height:  Get.width < 500 ? 50 : 55,
          //                 decoration: BoxDecoration(
          //                   borderRadius: const BorderRadius.only(
          //                     topRight: Radius.circular(15),
          //                     topLeft: Radius.circular(15),
          //                     bottomRight: Radius.circular(15),
          //                     bottomLeft: Radius.circular(15),
          //                   ),
          //                   gradient: const LinearGradient(
          //                     begin: Alignment.topCenter,
          //                     end: Alignment.bottomCenter,
          //                     stops: [0.0, 0.5, 1.0],
          //                     colors: [
          //                       Color(0xFFFFC700),
          //                       Color(0xFFFFB100),
          //                       Color(0xFFFF9900),
          //                     ],
          //                   ),
          //                   border: Border.all(
          //                       width: 2, color: const Color(0xFF000000)),
          //                   boxShadow: [
          //                     BoxShadow(
          //                       color: Colors.grey.withOpacity(0.2),
          //                       offset: const Offset(1, 2),
          //                       blurRadius: 3,
          //                     ),
          //                   ],
          //                 ),
          //                 child: Center(
          //                   child: AppWidget.boldText(context, "รถใหญ่", Get.width < 500 ? 14 : 16,
          //                       const Color(0xFFFFFFFF), FontWeight.w400),
          //                 ),
          //               ))),
          //   ],
          // ),
          // const SizedBox(
          //   height: 250,
          // ),
          /// ปุ่มยกเลิก
          // InkWell(
          //   onTap: (){
          //     saveData();
          //     AppService.callPhone(brokenCarCtl.technicianPhone.value);
          //     showGeneralDialog(
          //       barrierLabel: "showGeneralDialog",
          //       barrierDismissible: true,
          //       barrierColor: Colors.black.withOpacity(0.6),
          //       transitionDuration: const Duration(milliseconds: 300),
          //       context: context,
          //       pageBuilder: (context, _, __) {
          //         return const SuccessBrokenCarPage();
          //       },
          //       transitionBuilder: (_, animation1, __, child) {
          //         return SlideTransition(
          //           position: Tween(
          //             begin: const Offset(0, 1),
          //             end: const Offset(0, 0),
          //           ).animate(animation1),
          //           child: child,
          //         );
          //       },
          //     );
          //   },
          //   child: Container(
          //     width: Get.width,
          //     height:  Get.width < 500 ? 50 : 60,
          //     decoration: BoxDecoration(
          //       borderRadius: const BorderRadius.only(
          //         topRight: Radius.circular(15),
          //         topLeft: Radius.circular(15),
          //         bottomRight: Radius.circular(15),
          //         bottomLeft: Radius.circular(15),
          //       ),
          //       gradient: LinearGradient(
          //         begin: Alignment.topCenter,
          //         end: Alignment.bottomCenter,
          //         stops: const [0.0, 1.0],
          //         colors: [
          //           const Color(0xFF000000).withOpacity(0.7),
          //           const Color(0xFF000000),
          //         ],
          //       ),
          //       boxShadow: [
          //         BoxShadow(
          //           color: Colors.grey.withOpacity(0.2),
          //           offset: const Offset(1, 2),
          //           blurRadius: 3,
          //         ),
          //       ],
          //     ),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         AppWidget.boldText(
          //             context,
          //             "ยืนยัน ",
          //             Get.width < 500 ? 14 : 16,
          //             const Color(0xFFFFB100),
          //             FontWeight.w500),
          //         AppWidget.normalText(
          //             context,
          //             "เพื่อติดต่อ",
          //             Get.width < 500 ? 14 : 16,
          //             const Color(0xFFFFFFFF),
          //             FontWeight.w400)
          //       ],
          //     ),
          //   ),
          // ),
          Obx(() => InkWell(
                onTap: (){
                  if(statusButton.isFalse){
                    return;
                  }
                  saveData();
                  AppService.callPhone(brokenCarCtl.technicianPhone.value);
                  showGeneralDialog(
                    barrierLabel: "showGeneralDialog",
                    barrierDismissible: true,
                    barrierColor: Colors.black.withOpacity(0.6),
                    transitionDuration: const Duration(milliseconds: 300),
                    context: context,
                    pageBuilder: (context, _, __) {
                      return const SuccessBrokenCarPage();
                    },
                    transitionBuilder: (_, animation1, __, child) {
                      return SlideTransition(
                        position: Tween(
                          begin: const Offset(0, 1),
                          end: const Offset(0, 0),
                        ).animate(animation1),
                        child: child,
                      );
                    },
                  );
                },
                child: Container(
            width: Get.width,
            height:  Get.width < 500 ? 50 : 60,
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(15),
                  topLeft: Radius.circular(15),
                  bottomRight: Radius.circular(15),
                  bottomLeft: Radius.circular(15),
                ),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0.0, 1.0],
                  colors: [
                    statusButton.isTrue ? const Color(0xFF000000).withOpacity(0.7) : const Color(0xFF000000).withOpacity(0.3),
                    statusButton.isTrue ? const Color(0xFF000000) : const Color(0xFF000000).withOpacity(0.3),
                  ],
                ),
              boxShadow: statusButton.isTrue
                  ? [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  offset: const Offset(1, 2),
                  blurRadius: 3,
                ),
              ]
                  : null,
            ),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldText(
                      context,
                      "ยืนยัน ",
                      Get.width < 500 ? 14 : 16,
                      statusButton.isTrue ? const Color(0xFFFFB100) : const Color(0xFFFFB100).withOpacity(0.3),
                      FontWeight.w500),
                  AppWidget.normalText(
                      context,
                      "เพื่อติดต่อ",
                      Get.width < 500 ? 14 : 16,
                      statusButton.isTrue ? const Color(0xFFFFFFFF) :  const Color(0xFFFFFFFF).withOpacity(0.3),
                      FontWeight.w400)
                ],
            ),
          ),
              ),)
        ],
      ),
    );
  }

  void checkData(String value){
    if(_phoneController.text.length == 10 &&
      locationLatLng != null && (statusSmallCar.value != false || statusBigCar.value != false)
    ){
      statusButton.value = true;
    } else {
      statusButton.value = false;
    }
  }

  saveData() async {
    try{
      if(token != null){
        Map dataBrokenCar = {
          "type_member" : "member",
          "user_id" : profileCtl.profile.value.id,
          "phone" : _phoneController.text,
          // "location" : locationLatLng,
          "car_type" : carType.value,
          "technician_Number" : brokenCarCtl.technicianPhone.value,
        };
        final responseBrokenCar = await AppApi.post(AppUrl.createBrokenCar, dataBrokenCar);
      } else {
        Map dataBrokenCar = {
          "type_member" : "guest",
          "user_id" : "",
          "phone" : _phoneController.text,
          // "location" : locationLatLng,
          "car_type" : carType.value,
          "technician_Number" : brokenCarCtl.technicianPhone.value,
        };
        final responseBrokenCar = await AppApi.post(AppUrl.createBrokenCar, dataBrokenCar);
      }
    }catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }
}

requestLocationPermission() async {
  // ตรวจสอบสถานะสิทธิ์
  PermissionStatus status = await Permission.locationWhenInUse.status;

  if (status.isDenied) {
    // ขอสิทธิ์ถ้ายังไม่ได้รับอนุญาต
    status = await Permission.locationWhenInUse.request();
  }

  if (status.isGranted) {
    print('เปิดแล้ว');
    // ได้รับอนุญาตให้ใช้งาน
    // ทำอะไรก็ตามที่คุณต้องการทำ
  } else {
    // ไม่ได้รับอนุญาตให้ใช้งาน
    print('ไม่เปิดเปิดแล้ว');

    // จัดการตามที่คุณต้องการจัดการ
  }
}
