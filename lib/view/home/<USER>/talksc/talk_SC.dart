import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/save_activity.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/sc_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';

class TalkSCPage extends StatefulWidget {
  const TalkSCPage({Key? key}) : super(key: key);

  @override
  State<TalkSCPage> createState() => _TalkSCPageState();
}

class _TalkSCPageState extends State<TalkSCPage> with TickerProviderStateMixin{
  final SecureStorage secureStorage = SecureStorage();

  final scCtl = Get.put(SCController());
  final profileCtl = Get.put(ProfileController());
  final saveActivityCtl = Get.put(SaveActivityController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "TalkWithSale");
    // getSC();
  }

  getSC() async {
    if(scCtl.resSCHeadOffice.data!.isEmpty){
      await scCtl.getSC();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body:  Obx((){
        if(scCtl.isLoading.value){
          return AppLoader.loaderWaitPage(context);
        } else {
          return Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                  ),
                ),
              ),
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      top: Get.width < 500 ? 60 : 80,
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(15),
                                  topLeft: Radius.circular(15),
                                  bottomRight: Radius.circular(15),
                                  bottomLeft: Radius.circular(15),
                                ),
                                border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                            child: const Icon(
                              Icons.arrow_back_ios_new,
                              size: 18,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            AppWidget.boldTextS(context, "สนใจซื้อรถ", Get.width < 500 ? 16 : 18, const Color(0xFF2B1710),
                                FontWeight.w500),
                            AppWidget.normalTextS(context, "ติดต่อเซลส์", Get.width < 500 ? 16 : 18, const Color(0xFF664701),
                                FontWeight.w500),
                          ],
                        ),
                        const SizedBox(
                          width: 36,
                          height: 36,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10,),
                  // buildListView(),
                  buildTabBar(),
                  const SizedBox(height: 10,),
                  buildTabBarView(),
                ],
              ),
            ],
          );
        }
      }),
        );
  }

  buildTabBar(){
    return DefaultTabController(
        length: 6,
        child: PreferredSize(
          preferredSize: const Size.fromHeight(30.0),
          child: TabBar(
            controller: scCtl.scTabController,
            indicatorColor: Colors.transparent,
            isScrollable: true,
            padding: const EdgeInsets.only(
              left: 24,
              right: 24,
            ),
            labelPadding: const EdgeInsets.only(
              right: 4
            ),
            tabs: [
              Tab(
                child: buildTitle(0, "สนญ."),
              ),
              Tab(
                child: buildTitle(1, "นายายอาม"),
              ),
              Tab(
                child: buildTitle(2, "ขลุง"),
              ),
              Tab(
                child: buildTitle(3, "สอยดาว"),
              ),
              Tab(
                child: buildTitle(4, "รถใหญ่"),
              ),
              Tab(
                child: buildTitle(5, "ออนไลน์"),
              ),
            ],
          ),
        )
    );
  }
  buildTabBarView(){
    return Expanded(
      child: TabBarView(
        controller: scCtl.scTabController,
        children: [
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCHeadOffice.data!.length,
            itemBuilder: (BuildContext context, int index) {
              return buildRowSale(
                context,
                scCtl.resSCHeadOffice.data![index].saleNickname,
                scCtl.resSCHeadOffice.data![index].saleName,
                scCtl.resSCHeadOffice.data![index].saleProduct,
                scCtl.resSCHeadOffice.data![index].salePosition,
                scCtl.resSCHeadOffice.data![index].salePictureBg,
                scCtl.resSCHeadOffice.data![index].saleLineId,
                scCtl.resSCHeadOffice.data![index].salePhone,
                scCtl.resSCHeadOffice.data![index].saleFacebook
              );
            }
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCNaYaiAm.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return buildRowSale(
                  context,
                  scCtl.resSCNaYaiAm.data![index].saleNickname,
                  scCtl.resSCNaYaiAm.data![index].saleName,
                  scCtl.resSCNaYaiAm.data![index].saleProduct,
                  scCtl.resSCNaYaiAm.data![index].salePosition,
                  scCtl.resSCNaYaiAm.data![index].salePictureBg,
                  scCtl.resSCNaYaiAm.data![index].saleLineId,
                  scCtl.resSCNaYaiAm.data![index].salePhone,
                  scCtl.resSCNaYaiAm.data![index].saleFacebook
                );
              }
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCKlung.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return buildRowSale(
                  context,
                  scCtl.resSCKlung.data![index].saleNickname,
                  scCtl.resSCKlung.data![index].saleName,
                  scCtl.resSCKlung.data![index].saleProduct,
                  scCtl.resSCKlung.data![index].salePosition,
                  scCtl.resSCKlung.data![index].salePictureBg,
                  scCtl.resSCKlung.data![index].saleLineId,
                  scCtl.resSCKlung.data![index].salePhone,
                  scCtl.resSCKlung.data![index].saleFacebook
                );
              }
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCSoiDao.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return buildRowSale(
                  context,
                  scCtl.resSCSoiDao.data![index].saleNickname,
                  scCtl.resSCSoiDao.data![index].saleName,
                  scCtl.resSCSoiDao.data![index].saleProduct,
                  scCtl.resSCSoiDao.data![index].salePosition,
                  scCtl.resSCSoiDao.data![index].salePictureBg,
                  scCtl.resSCSoiDao.data![index].saleLineId,
                  scCtl.resSCSoiDao.data![index].salePhone,
                  scCtl.resSCSoiDao.data![index].saleFacebook
                );
              }
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCBigTruck.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return buildRowSale(
                  context,
                  scCtl.resSCBigTruck.data![index].saleNickname,
                  scCtl.resSCBigTruck.data![index].saleName,
                  scCtl.resSCBigTruck.data![index].saleProduct,
                  scCtl.resSCBigTruck.data![index].salePosition,
                  scCtl.resSCBigTruck.data![index].salePictureBg,
                  scCtl.resSCBigTruck.data![index].saleLineId,
                  scCtl.resSCBigTruck.data![index].salePhone,
                  scCtl.resSCBigTruck.data![index].saleFacebook
                );
              }
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: scCtl.resSCOnline.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return buildRowSale(
                  context,
                  scCtl.resSCOnline.data![index].saleNickname,
                  scCtl.resSCOnline.data![index].saleName,
                  scCtl.resSCOnline.data![index].saleProduct,
                  scCtl.resSCOnline.data![index].salePosition,
                  scCtl.resSCOnline.data![index].salePictureBg,
                  scCtl.resSCOnline.data![index].saleLineId,
                  scCtl.resSCOnline.data![index].salePhone,
                  scCtl.resSCOnline.data![index].saleFacebook
                );
              }
          ),
        ],
      ),
    );
  }

  buildTitle(value, text){
    return Obx(() => Container(
      padding: const EdgeInsets.only(
          left: 14,
          right: 14,
          top: 3,
          bottom: 3
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: scCtl.currentIndex.value == value ? null :
        Border.all(
            width: 1,
            color: const Color(0xFFE8E6E2)
        ),
        gradient: scCtl.currentIndex.value == value ? const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: [
            0, 0.5125, 1
          ],
          colors: [
            Color(0xFFFFC700),
            Color(0xFFFFB100),
            Color(0xFFFF9900),
          ],
        ) : null,
      ),
      child: AppWidget.boldText(
        context,
        text,
        14,
        scCtl.currentIndex.value == value ? const Color(0xFF2B1710) : const Color(0xFF2B1710),
        FontWeight.w400,
      ),
    ));
  }

  buildRowSale (context, name, fullname, product, position, picture, lineId, phone,facebook){
    return Container(
      width: Get.width,
      height: 184,
      margin: const EdgeInsets.only(

          bottom: 10),
      padding: EdgeInsets.only(
          left: Get.width < 500 ? 18 : 54,
          right: Get.width < 500 ? 18 : 54,
        bottom: 10
      ),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: Get.width,
              height: 155,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFFF3F3F3),
                    const Color(0xFFFFFFFF).withOpacity(0.3),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, 4), // changes position of shadow
                  ),
                ],
              ),
              padding: const EdgeInsets.only(
                left: 21,
                top: 18,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppWidget.boldTextS(context, "SC. $name", 16, const Color(0xFF2B1710),
                      FontWeight.w500),
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                        left: 14,
                        right: 14,
                        top: 6,
                        bottom: 6
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                    child: AppWidget.boldTextS(context, product, 12, const Color(0xFF282828),
                        FontWeight.w500),
                  ),
                ],
              ),
            ),
          ),
          // Container(
          //   width: Get.width,
          //   padding: const EdgeInsets.only(left: 20),
          //   alignment: Alignment.center,
          //   child: ImageFiltered(
          //     imageFilter: ImageFilter.blur(sigmaX: 10, sigmaY: 4,tileMode: TileMode.decal),
          //     child: Image.network(
          //       picture,
          //       opacity: const AlwaysStoppedAnimation(.7),
          //       errorBuilder: (context, url, error) => const SizedBox(),
          //     ),
          //   ),
          // ),
          Container(
            padding: const EdgeInsets.only(left: 50),
            alignment: Alignment.center,
            child: CachedNetworkImage(
              imageUrl: picture,
              fit: BoxFit.cover,
              placeholder: (context, url) => const SizedBox(
                width: 50,
                height: 50,
                child: Center(
                  child: CircularProgressIndicator(color: Colors.orange),
                ),
              ),
              errorWidget: (context, url, error) => Image.asset('assets/image/mascot2.png'),
            ),
          ),
          Positioned(
            right: 20,
            bottom: 20,
            child: Column(
              children: [
                InkWell(
                  onTap: (){
                    if(profileCtl.token.value != null){
                      saveActivityCtl.saveActivity("ติดต่อเซลส์","phone",fullname);
                      AppService.callPhone(phone);
                    } else {
                      AppWidget.showDialogPageSlide(context, const LoginPage());
                    }
                  },
                  child: Container(
                    width: 34,
                    height: 34,
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xFFF3F3F3),
                            const Color(0xFFFFFFFF).withOpacity(0.6),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(width: 1,color: const Color(0xFFE8E6E2))
                    ),
                    child: Image.asset('assets/image/service/phone.png'),
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                InkWell(
                  onTap: (){
                    if(profileCtl.token.value != null){
                      saveActivityCtl.saveActivity("ติดต่อเซลส์","line",fullname);
                      AppService.launchUrl("http://line.me/ti/p/~$lineId");
                    } else {
                      AppWidget.showDialogPageSlide(context, const LoginPage());
                    }
                  },
                  child: Container(
                    width: 34,
                    height: 34,
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xFFF3F3F3),
                            const Color(0xFFFFFFFF).withOpacity(0.6),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(width: 1,color: const Color(0xFFE8E6E2))
                    ),
                    child: Image.asset('assets/image/service/line.png'),
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                InkWell(
                  onTap: (){
                    if(profileCtl.token.value != null){
                      saveActivityCtl.saveActivity("ติดต่อเซลส์","facebook",fullname);
                      AppService.launchUrl(facebook);
                    } else {
                      AppWidget.showDialogPageSlide(context, const LoginPage());
                    }
                  },
                  child: Container(
                    width: 34,
                    height: 34,
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xFFF3F3F3),
                            const Color(0xFFFFFFFF).withOpacity(0.6),
                          ],
                        ),
                        shape: BoxShape.circle,
                        border: Border.all(width: 1,color: const Color(0xFFE8E6E2))
                    ),
                    child: Image.asset('assets/image/service/facebook.png'),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
