import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/detail_standard_MR.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class RegisSuccessMRPage extends StatefulWidget {
  const RegisSuccessMRPage({Key? key}) : super(key: key);

  @override
  State<RegisSuccessMRPage> createState() => _RegisSuccessMRPageState();
}

class _RegisSuccessMRPageState extends State<RegisSuccessMRPage> {

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "RegisterMarketingRepresentative");
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Scaffold(
            body: Stack(
              children: <Widget>[
                Container(
                  width: _width,
                  height: _height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFE8E6E2),
                        Color(0xFFD9D8D5),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  height: 371.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      // stops: [1.0, 1.0],
                      colors: [
                        const Color(0xFFFFB100).withOpacity(1),
                        const Color(0xFFFFC700).withOpacity(0.7),
                        const Color(0xFFFFC700).withOpacity(0.4),
                        const Color(0xFFFFC700).withOpacity(0),
                        // Color(0xFFFFB100),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 13.h,),
                      // color: Colors.red,
                      width: _width,
                      // height: 700.h,
                      child: Stack(
                        children: [
                          Container(
                            width: _width,
                          ),
                          Container(
                            margin: EdgeInsets.only(left: 25.w),
                            width: 400.w,
                            height: 80.h,
                            child: Container(
                              margin: EdgeInsets.only(
                                  top: 20.h
                              ),
                            ),
                          ),
                          Column(
                            children: [
                              Container(
                                  margin: EdgeInsets.only(top: 80.0.h,
                                      left: 20.w
                                  ),
                                  alignment: Alignment.topCenter,
                                  // color: Colors.red,
                                  child: Image.asset('assets/image/MR/Success_Mascot.png',
                                    height: 127.h,
                                    width: 245.w,
                                  )
                              ),
                              SizedBox(height: 8.h,),
                              GradientText('คุณได้เป็นตัวแทนผู้แนะนำ กับเรา..เรียบร้อย',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt',
                                  fontSize: 14.w,
                                ),
                                gradientType: GradientType.linear,
                                gradientDirection: GradientDirection.ttb,
                                radius: 10,
                                colors: const [
                                  Color(0xFF664701),
                                  Color(0xFF151423),
                                ],),
                              SizedBox(height: 22.h,),
                              Text('ขอบคุณที่สนใจเป็นตัวแทนผู้แนะนำลูกค้า MR กับประชากิจฯ',
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    fontSize: 12.w,
                                    color: const Color(0xFF664701)
                                ),),
                              RichText(
                                text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'ระดับของคุณ คือ ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: const Color(0xFF664701),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'Standard',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF664701),
                                          fontSize: 14.w,
                                        ),
                                      )
                                    ]
                                ),
                              ),
                              SizedBox(height: 24.h,),
                              Padding(
                                padding: EdgeInsets.only(bottom: 35.h),
                                child: InkWell(
                                  onTap: (){
                                    Navigator.of(context).push(MaterialPageRoute(
                                      builder: (context) => const  DetailStandardMRPage(),
                                    ));
                                  },
                                  child: Container(
                                    width: 323.w,
                                    height: 40.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5.h),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: Colors.black45,
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                      color: const Color(0xFF282828),
                                    ),
                                    child: Center(
                                      child: Text(
                                        'ดูข้อมูลของคุณ',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Colors.white,
                                          fontSize: 14.w,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              RichText(
                                text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'คุณสามารถ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: const Color(0xFF664701),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                      TextSpan(
                                        text: ' เพิ่มระดับ ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF664701),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'ของคุณได้',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: const Color(0xFF664701),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                      TextSpan(
                                        text: '\nจากการร่วมทำกิจกรรมแนะนำของทางบริษัท',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: const Color(0xFF664701),
                                          fontSize: 11.w,
                                        ),
                                      ),
                                    ]
                                ),
                              ),
                              SizedBox(height: 26.h,),
                              Text('สิทธิพิเศษการได้รับคะแนนสะสม',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: 12.w,
                                    color: const Color(0xFF282828)
                                ),),
                              SizedBox(height: 22.h,),
                              SizedBox(
                                width: 215.w,
                                height: 60.h,
                                child: Row(
                                  children: [
                                    Image.asset('assets/image/MR/ProcoinShadow.png',
                                      width: 30.h,
                                      height: 30.h,
                                    ),
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    RichText(text: TextSpan(
                                      children:[
                                        TextSpan(
                                          text: 'ตัวแทนผู้แนะนำ ระดับ Pro',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),

                                        ),
                                        TextSpan(
                                          text: '\nรับคะแนนสูงสุด 50%',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 11.w,
                                          ),
                                        ),
                                      ] ,
                                    ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 215.w,
                                height: 60.h,
                                child: Row(
                                  children: [
                                    Image.asset('assets/image/MR/PluscoinShadow.png',
                                      width: 30.h,
                                      height: 30.h,
                                    ),
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    RichText(text: TextSpan(
                                      children:[
                                        TextSpan(
                                          text: 'ตัวแทนผู้แนะนำ ระดับ Plus',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),

                                        ),
                                        TextSpan(
                                          text: '\nรับคะแนนสูงสุด 25%',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 11.w,
                                          ),
                                        ),
                                      ] ,
                                    ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 215.w,
                                height: 60.h,
                                child: Row(
                                  children: [
                                    Image.asset('assets/image/MR/StandardcoinShadow.png',
                                      width: 30.h,
                                      height: 30.h,
                                    ),
                                    SizedBox(
                                      width: 10.w,
                                    ),
                                    RichText(text: TextSpan(
                                      children:[
                                        TextSpan(
                                          text: 'ตัวแทนผู้แนะนำ ระดับ Standard',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),

                                        ),
                                        TextSpan(
                                          text: '\nรับคะแนนตามเกณฑ์มาตรฐาน',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 11.w,
                                          ),
                                        ),
                                      ] ,
                                    ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }
    );
  }
}
