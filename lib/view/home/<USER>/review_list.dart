import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class ReviewPage extends StatefulWidget {
  final running;

  ReviewPage(this.running);

  @override
  State<ReviewPage> createState() => _ReviewPageState(this.running);
}

class _ReviewPageState extends State<ReviewPage> {
  final running;

  _ReviewPageState(this.running);

  late YoutubePlayerController _controller;
  late PlayerState _playerState;
  late YoutubeMetaData _videoMetaData;
  late TextEditingController _idController;
  late TextEditingController _seekToController;

  final List<String> _ids = [];
  int indexClip = 0;

  List<dynamic> _listFeelWellTV = [];
  Map primaryClip = {};
  String videoId = '';

  bool _isPlayerReady = false;

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    analytics.setCurrentScreen(screenName: "ReviewList");
    if (running == "all") {
      getFeelWellTV("รีวิวรถ", 0);
    } else {
      getFeelWellTVForRunning("รีวิวรถ", widget.running);
    }
  }

  @override
  void deactivate() {
    _controller.pause();
    super.deactivate();
  }

  @override
  void dispose() {
    _controller.dispose();
    _idController.dispose();
    _seekToController.dispose();
    super.dispose();
  }

  getFeelWellTV(String type, int index) async {
    try {
      Map data = {"type_feelwelltv": type};
      final response =
      await AppApi.post(AppUrl.getFeelWellTVByTypeForApp, data);
      int status = response['status'];
      if (status == 200) {
        var result = (response['result'] as List);
        setState(() {
          primaryClip = response['result'][index];
          _listFeelWellTV = result;
          for (var i = 0; i < result.length; i++) {
            var id = YoutubePlayer.convertUrlToId(result[i]['link_video'])!;
            _ids.add(id);
          }
          isLoading = false;
        });
        convertUrlToId(primaryClip['link_video']);
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getFeelWellTV in FeelWellList');
    }
  }

  getFeelWellTVForRunning(String type, String running) async {
    try {
      Map data = {"type_feelwelltv": type};
      final response =
      await AppApi.post(AppUrl.getFeelWellTVByTypeForApp, data);
      int status = response['status'];
      if (status == 200) {
        var result = (response['result'] as List);
        setState(() {
          _listFeelWellTV = result;
          for (var i = 0; i < result.length; i++) {
            if (result[i]['running'].toString() == running) {
              var index = i;
              primaryClip = response['result'][index];
              indexClip = i;
            }
            var id = YoutubePlayer.convertUrlToId(result[i]['link_video'])!;
            _ids.add(id);
          }
          isLoading = false;
        });
        convertUrlToId(primaryClip['link_video']);
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getFeelWellTV in FeelWellList');
    }
  }

  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }
    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        SystemChrome.setPreferredOrientations(DeviceOrientation.values);
      },
      player: YoutubePlayer(
        controller: _controller,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Colors.blueAccent,
        onReady: () {
          _isPlayerReady = true;
        },
        onEnded: (data) {
          _controller
              .load(_ids[(_ids.indexOf(data.videoId) + 1) % _ids.length]);
          setState(() {
            primaryClip =
            _listFeelWellTV[(_ids.indexOf(data.videoId) + 1) % _ids.length];
          });
        },
      ),
      builder: (context, player) => Scaffold(
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [Color(0xff505050), Color(0xff282828)],
                  stops: [0.0, 1.0],
                ),
              ),
            ),
            ListView(
              padding: EdgeInsets.zero,
              children: [
                player,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _space,
                    Container(
                      margin: const EdgeInsets.only(
                        left: 18,
                        right: 18,
                      ),
                      child: Text(
                        "${primaryClip['name']}",
                        style: const TextStyle(
                            fontSize: 16,
                            color:  Colors.white,
                            fontWeight: FontWeight.w600
                        ),
                      ),
                    ),
                    _space,
                    Container(
                      margin: const EdgeInsets.only(
                        left: 18,
                        right: 18,
                      ),
                      child: AppWidget.boldText(
                          context,
                          AppService.dateThaiDate(primaryClip['create_time']),
                          14,
                          const Color(0xFFFFB100),
                          FontWeight.w400),
                    ),
                    _space,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            InkWell(
                              onTap: () async {
                                AppService.launchUrl(
                                    "https://www.youtube.com/channel/UCyyGPdaXzXiLBYVnMaevnkw?sub_confirmation=1");
                              },
                              child: Container(
                                margin: const EdgeInsets.only(
                                    left: 18
                                ),
                                width: 100,
                                height: 40,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(
                                        30
                                    ),
                                  ),
                                  color: Color(0xFF707070),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.center,
                                  //Center Column contents vertically,
                                  crossAxisAlignment:
                                  CrossAxisAlignment.center,
                                  //Center Column contents horizontally,
                                  children: [
                                    Container(
                                      child: Image.asset(
                                          'assets/image/news/icon_youtube.png',
                                          width: 20
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(
                                        left: 8,
                                      ),
                                      child: const Text(
                                        "ติดตาม",
                                        style: TextStyle(
                                          fontFamily: 'Prompt',
                                          fontSize: 14,
                                          color: Color(0xFFFFFFFF),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      margin: const EdgeInsets.only(
                        top: 10,
                      ),
                      child: const Divider(
                        color: Color(0xFF707070),
                      ),
                    ),
                    _space,
                    Container(
                      margin: const EdgeInsets.only(
                        left: 18,
                        right: 18,
                      ),
                      child: AppWidget.boldText(
                          context,
                          "วิดีโอ LIVE ย้อนหลัง",
                          16,
                          const Color(0xFFFFFFFF),
                          FontWeight.w400),
                    ),
                    _space,
                    Container(
                      margin: const EdgeInsets.only(
                        left:18,
                        right: 18,
                      ),
                      width: Get.width,
                      height: 250,
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount: _listFeelWellTV.length,
                        itemBuilder: (context, i) {
                          return SizedBox(
                            width: Get.width,
                            child: GestureDetector(
                              onTap: _isPlayerReady
                                  ? () {
                                setState(() {
                                  primaryClip = _listFeelWellTV[i];
                                });

                                var id = YoutubePlayer.convertUrlToId(
                                  _listFeelWellTV[i]['link_video'],
                                ) ??
                                    '';
                                _controller.load(id);
                                FocusScope.of(context)
                                    .requestFocus(FocusNode());
                              }
                                  : null,
                              child: Column(
                                children: [
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CachedNetworkImage(
                                        imageUrl: _listFeelWellTV[i]['primary_img'],
                                        imageBuilder: (context, imageProvider) =>
                                            Container(
                                              width: 140,
                                              height: 90,
                                              decoration: BoxDecoration(
                                                image: DecorationImage(
                                                  fit: BoxFit.cover,
                                                  alignment: Alignment.topCenter,
                                                  image: imageProvider,
                                                ),
                                                borderRadius: const BorderRadius.all(
                                                  Radius.circular(
                                                    15,
                                                  ),
                                                ),
                                              ),
                                            ),
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => const SizedBox(
                                          width: 140,
                                          height: 90,
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: Colors.orange,
                                            ),
                                          ),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Container(
                                              width: 140,
                                              height: 90,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: const Color(0xFFFFB100),
                                                  width: 0.5,
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: const Color(0xFF000000)
                                                        .withOpacity(0.1),
                                                    offset: const Offset(0, 3),
                                                    blurRadius: 3.0,
                                                  ),
                                                ],
                                              ),
                                              child: const Center(
                                                child: Icon(
                                                  Icons.error,
                                                  color: Color(0xFFFFB100),
                                                ),
                                              ),
                                            ),
                                      ),
                                      Container(
                                        margin: const EdgeInsets.only(
                                          left: 20,
                                        ),
                                        child: Column(
                                          children: [
                                            SizedBox(
                                              width: 150,
                                              child: Text(
                                                _listFeelWellTV[i]['name'].length >
                                                    44
                                                    ? _listFeelWellTV[i]['name']
                                                    .substring(0, 44) +
                                                    '...'
                                                    : _listFeelWellTV[i]['name'],
                                                style: TextStyle(
                                                  fontFamily: 'Prompt',
                                                  fontSize: 14,
                                                  color: const Color(0xFFFFFFFF)
                                                      .withOpacity(0.9),
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 150,
                                              child: Text(
                                                AppService.dateThaiDate(
                                                    _listFeelWellTV[i]
                                                    ['create_time']),
                                                style: const TextStyle(
                                                  fontFamily: 'Prompt',
                                                  fontSize: 12,
                                                  color: Color(0xFFFFB100),
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin: const EdgeInsets.only(
                                      top: 10,
                                      bottom: 10,
                                    ),
                                    child: const Divider(
                                      color: Color(0xFF707070),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget get _space => const SizedBox(height: 10);

  void convertUrlToId(String rul) {
    setState(() {
      videoId = YoutubePlayer.convertUrlToId(rul)!;
    });
    settingYoutube();
  }

  settingYoutube() {
    setState(() {
      _controller = YoutubePlayerController(
        initialVideoId: _ids[indexClip],
        flags: const YoutubePlayerFlags(
          mute: false,
          autoPlay: true,
          disableDragSeek: false,
          loop: false,
          isLive: true,
          forceHD: true,
          enableCaption: true,
        ),
      )..addListener(listener);
      _idController = TextEditingController();
      _seekToController = TextEditingController();
      _videoMetaData = const YoutubeMetaData();
      _playerState = PlayerState.unknown;
    });
  }

  void listener() {
    if (_isPlayerReady && mounted && !_controller.value.isFullScreen) {
      setState(() {
        _playerState = _controller.value.playerState;
        _videoMetaData = _controller.metadata;
      });
    }
  }

}
