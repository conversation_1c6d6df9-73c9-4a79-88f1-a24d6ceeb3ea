import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/new_car_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class DialogNewCar extends StatefulWidget {
  const DialogNewCar({Key? key}) : super(key: key);

  @override
  State<DialogNewCar> createState() => _DialogNewCarState();
}

class _DialogNewCarState extends State<DialogNewCar> {

  final newCarCtl = Get.put(NewCarController());

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (){
        newCarCtl.isShow.value = !newCarCtl.isShow.value;
      },
      child: Container(
        width: Get.width,
        height: 300,
        margin: EdgeInsets.only(
          left: Get.width < 500 ? 8 : 54,
          right: Get.width < 500 ? 8 : 54,
        ),
        padding: const EdgeInsets.only(
            top: 8,
            left: 10,
            right: 10,
        ),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFFFFFF),
                Color(0xFFF8F8F8),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              blurRadius: 50,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: PageView.builder(
          controller: newCarCtl.pageController,
          itemCount: newCarCtl.newCar.newCarList!.length,
          clipBehavior: Clip.none,
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          itemBuilder: (BuildContext context, int index) => InkWell(
            onTap: () {
              newCarCtl.isShow.value = !newCarCtl.isShow.value;
            },
            child: Column(
              children: [
                Row(
                  children: [
                    Image.asset(
                      'assets/image/service/menu_sale.png',
                      width: 28,
                    ),
                    AppWidget.boldTextS(context, "  สถานะ", 14,
                        const Color(0xFF282828), FontWeight.w600),
                    AppWidget.normalTextS(context, " : ", 14,
                        const Color(0xFF282828), FontWeight.w600),
                    AppWidget.boldTextS(
                        context,
                        getStatusMessage(newCarCtl.newCar.newCarList![index].statusApp.toString()),
                        14,
                        const Color(0xFF895F00),
                        FontWeight.w600),
                    const Spacer(),
                    SvgPicture.string(
                        '<svg width="18" height="4" viewBox="0 0 18 4" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 2C18 1.17157 17.3284 0.5 16.5 0.5C15.6716 0.5 15 1.17157 15 2C15 2.82843 15.6716 3.5 16.5 3.5C17.3284 3.5 18 2.82843 18 2Z" fill="#FFB100"/> <path d="M10.5 2C10.5 1.17157 9.82843 0.5 9 0.5C8.17157 0.5 7.5 1.17157 7.5 2C7.5 2.82843 8.17157 3.5 9 3.5C9.82843 3.5 10.5 2.82843 10.5 2Z" fill="#FFB100"/> <path d="M3 2C3 1.17157 2.32843 0.5 1.5 0.5C0.671573 0.5 0 1.17157 0 2C0 2.82843 0.671573 3.5 1.5 3.5C2.32843 3.5 3 2.82843 3 2Z" fill="#FFB100"/></svg>')
                  ],
                ),
                newCarCtl.isShow.isTrue
                    ? Column(
                  children: [
                    const Divider(
                      thickness: 1,
                      color: Color(0xFFBCBCBC),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    AppWidget.boldTextS(
                        context,
                        newCarCtl.newCar.newCarList![index].carType,
                        14,
                        const Color(0xFF282828),
                        FontWeight.w600),
                    const SizedBox(
                      height: 6,
                    ),
                    Image.network(
                      newCarCtl.newCar.newCarList![index].carImage
                          .toString(),
                      width: 135,
                      height: 76,
                      errorBuilder: (BuildContext context,
                          Object exception,
                          StackTrace? stackTrace) {
                        return Container(
                          width: 135,
                          height: 76,
                          padding: const EdgeInsets.all(10),
                          child: Image.asset(
                            "assets/image/car_detail/car_mockup.png",
                          ),
                        );
                      },
                      loadingBuilder: (BuildContext context,
                          Widget child,
                          ImageChunkEvent? loadingProgress) {
                        if (loadingProgress == null) {
                          return child;
                        }
                        return Container(
                            width: 135,
                            height: 76,
                            alignment: Alignment.center,
                            child: Stack(
                              children: [
                                Container(
                                  width: 126,
                                  height: 76,
                                  alignment: Alignment.center,
                                  padding: const EdgeInsets.all(10),
                                  child: Image.asset(
                                    "assets/image/car_detail/car_mockup.png",
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.center,
                                  child: SizedBox(
                                    width: 15,
                                    height: 15,
                                    child:
                                    CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color:
                                      const Color(0xFFFFFFFF),
                                      value: loadingProgress
                                          .expectedTotalBytes !=
                                          null
                                          ? loadingProgress
                                          .cumulativeBytesLoaded /
                                          loadingProgress
                                              .expectedTotalBytes!
                                          : null,
                                    ),
                                  ),
                                ),
                              ],
                            ));
                      },
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(children: [
                        TextSpan(
                            text: "เกรด : ",
                            style: TextStyle(
                              color: const Color(0xFF282828),
                              fontSize: 13,
                              fontFamily: 'Prompt',
                              fontWeight: FontWeight.w300,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 5.0,
                                  color: const Color(0xFF000000)
                                      .withOpacity(0.1),
                                ),
                              ],
                            )),
                        TextSpan(
                            text: newCarCtl.newCar.newCarList![index].car,
                            style: TextStyle(
                              color: const Color(0xFF895F00),
                              fontSize: 12,
                              fontFamily: 'Prompt-Medium',
                              fontWeight: FontWeight.w600,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 5.0,
                                  color: const Color(0xFF000000)
                                      .withOpacity(0.1),
                                ),
                              ],
                            )),
                      ]),
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    RichText(
                      text: TextSpan(children: [
                        TextSpan(
                            text: "สี : ",
                            style: TextStyle(
                              color: const Color(0xFF282828),
                              fontSize: 13,
                              fontFamily: 'Prompt',
                              fontWeight: FontWeight.w300,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 5.0,
                                  color: const Color(0xFF000000)
                                      .withOpacity(0.1),
                                ),
                              ],
                            )),
                        TextSpan(
                            text: newCarCtl
                                .newCar.newCarList![index].color,
                            style: TextStyle(
                              color: const Color(0xFF282828),
                              fontSize: 12,
                              fontFamily: 'Prompt-Medium',
                              fontWeight: FontWeight.w600,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 5.0,
                                  color: const Color(0xFF000000)
                                      .withOpacity(0.1),
                                ),
                              ],
                            )),
                      ]),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    InkWell(
                      onTap: () {
                        AppService.callPhone(newCarCtl
                            .newCar.newCarList![index].advisorPhone
                            .toString());
                      },
                      child: Row(
                        crossAxisAlignment:
                        CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(
                            width: 6,
                          ),
                          SizedBox(
                            width: 38,
                            height: 38,
                            child: Stack(
                              children: [
                                Align(
                                  alignment: Alignment.center,
                                  child: ClipRRect(
                                    borderRadius:
                                    BorderRadius.circular(50),
                                    child: Container(
                                        decoration: BoxDecoration(
                                            borderRadius:
                                            BorderRadius
                                                .circular(50),
                                            border: Border.all(
                                              color: const Color(
                                                  0xFF282828),
                                              width: 1,
                                            ),
                                            color: Colors
                                                .grey.shade300),
                                        width: 32,
                                        height: 32,
                                        child: Image.network(
                                          newCarCtl
                                              .newCar
                                              .newCarList![index]
                                              .advisorPic!,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (BuildContext context,
                                              Object exception,
                                              StackTrace?
                                              stackTrace) {
                                            return SizedBox(
                                              width: 32,
                                              height: 32,
                                              child: Image.asset(
                                                "assets/image/mascot2.png",
                                              ),
                                            );
                                          },
                                        )),
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.bottomRight,
                                  child: Container(
                                    width: 18,
                                    height: 18,
                                    padding:
                                    const EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          const Color(0xFFF3F3F3),
                                          const Color(0xFFFFFFFF)
                                              .withOpacity(0.6),
                                        ],
                                      ),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Image.asset(
                                        'assets/image/service/phone.png'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Column(
                            crossAxisAlignment:
                            CrossAxisAlignment.start,
                            mainAxisAlignment:
                            MainAxisAlignment.center,
                            children: [
                              AppWidget.normalText(
                                  context,
                                  "ติดต่อ",
                                  11,
                                  const Color(0xFF000000),
                                  FontWeight.w300),
                              AppWidget.normalTextS(
                                  context,
                                  "SC${newCarCtl.newCar.newCarList![index].advisorNickname}",
                                  11,
                                  const Color(0xFF000000),
                                  FontWeight.w500)
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                )
                    : const SizedBox()
              ],
            ),
          ),
        ),
      ),
    );
  }

  String getStatusMessage(String statusApp) {
    if (statusApp == "book an appointment") {
      return "จองรถเรียบร้อยแล้ว";
    } else if (statusApp == "waiting finance") {
      return "รอเซ็นสัญญา";
    } else if (statusApp == "finance approve") {
      return "ไฟแนนซ์อนุมัติ";
    } else if (statusApp == "finance not approve") {
      return "ไฟแนนซ์ไม่อนุมัติ";
    } else if (statusApp == "have car in stock") {
      return "รอรถมาส่ง 1-3 วัน";
    } else if (statusApp == "car to headoffice") {
      return "รถมาส่งถึงสำนักงานใหญ่แล้ว";
    } else if (statusApp == "custom car") {
      return "อยู่ระหว่างขั้นตอนการสั่งแต่ง";
    } else if (statusApp == "delivery car") {
      return "รถพร้อมส่งมอบ";
    } else {
      return "ส่งมอบรถแล้ว";
    }
  }
}
