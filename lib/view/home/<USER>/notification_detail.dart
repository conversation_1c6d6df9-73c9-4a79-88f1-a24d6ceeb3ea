import 'dart:io';
import 'dart:typed_data';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;


class NotificationDetailPage extends StatefulWidget {
  final notificationItem;
  final phoneNumber;
  const NotificationDetailPage(this.notificationItem, this.phoneNumber, {super.key});

  @override
  State<NotificationDetailPage> createState() => _NotificationDetailPageState(this.notificationItem, this.phoneNumber);
}

class _NotificationDetailPageState extends State<NotificationDetailPage> {
  Map notificationItem;
  String phoneNumber;
  _NotificationDetailPageState(this.notificationItem, this.phoneNumber);

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  final notificationCtl = Get.put(NotificationController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "NotificationDetail");
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body:Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0, 0.5],
                colors: [
                  Color(0xFFEDEDED),
                  Color(0xFFF2F2F2),
                ],
              ),
            ),
          ),
          Column(
            children: [
              buildTopContainer(),
              const SizedBox(
                height: 20,
              ),
              buildContent()
            ],
          ),
          Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom))
        ],
      ),
    );
  }

  buildTopContainer(){
    return Container(
      margin: const EdgeInsets.only(
        top: 70,
        left: 18,
        right: 18,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: () async {
              await notificationCtl.getNotification();
              Get.back();
            },
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
              child: const Icon(
                Icons.arrow_back_ios_new,
                size: 15,
                color: Color(0xFFFFB100),
              ),
            ),
          ),
          AppWidget.boldTextS(
              context,
              "ข้อความ",
              16,
              const Color(0xFF2B1710),
              FontWeight.w400),
          InkWell(
            onTap: () async {
              var result = await AppAlert.showConfirm(context,  'ยืนยันลบข้อความ',
                  'คุณต้องการจะลบข้อความ\nใช่หรือไม่?',
                  'ยืนยัน');
              if(result == true){
                await notificationCtl.deleteNotification(context, notificationItem['docId'].toString());
              }
            },
            child: Container(
              padding: const EdgeInsets.only(
                top: 6,
                bottom: 6,
                right: 10,
                left: 10,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: 1,
                    color: const Color(0xFF707070).withOpacity(0.4),
                  )
              ),
              child: Image.asset('assets/image/notification/notification_trash.png',
                width: 11, height: 14,),
            ),
          )
        ],
      ),
    );
  }

  buildContent(){
    return Container(
      margin: const EdgeInsets.only(
        left: 18,
        right: 18,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 10,
          ),
          AppWidget.normalTextS(
              context,
              AppService.dateThai(notificationItem['create']),
              14,
              const Color(0xFF895F00),
              FontWeight.w400),
          const SizedBox(
            height: 10,
          ),
          SizedBox(
            child: Text(
              notificationItem['title'],
              style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                  fontFamily: "Prompt-Medium",
                  fontWeight: FontWeight.w400
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
              alignment: Alignment.topLeft,
              child: Text(notificationItem['detail'],
                  style: const TextStyle(
                      color: Color(0xFF6A6A6A),
                      fontSize: 16.0,
                      fontFamily: "Prompt",
                      fontWeight: FontWeight.w400
                  )
              )
          ),
          notificationItem['note'] != null &&
              notificationItem['note'] != ''
              ? Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(
                  alignment: Alignment.topLeft,
                  child: const Text('หมายเหตุ :',
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 16.0,
                          fontFamily: "Prompt-Medium",
                          fontWeight: FontWeight.w400
                      )
                  )
              ),
              Container(
                  alignment: Alignment.topLeft,
                  child: Text(notificationItem['note'],
                      style: const TextStyle(
                          color: Color(0xFF6A6A6A),
                          fontSize: 16.0,
                          fontFamily: "Prompt",
                          fontWeight: FontWeight.w400
                      )
                  )
              )
            ],
          )
              : Container(),
          const SizedBox(
            height: 20,
          ),
          if (notificationItem['type'] == 'notificationLink')
            notificationItem['url'] != null && notificationItem['url'] != ''
                ? Container(
              alignment: Alignment.topLeft,
              child: GestureDetector(
                onTap: () {
                  AppService.launchUrl(notificationItem['url']);
                },
                child: SizedBox(
                  width: 100,
                  height: 38,
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(
                        Radius.circular(
                          30,
                        ),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000)
                              .withOpacity(0.1),
                          blurRadius: 5,
                          offset: const Offset(0, 2),
                        )
                      ],
                      color: const Color(0xFFFFB100),
                    ),
                    child: const Text(
                      "คลิ๊กที่นี่",
                      style: TextStyle(
                        fontFamily: "Prompt-Medium",
                        fontSize:
                        16.0,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            )
                : Container(),
          if (notificationItem['type'] == 'notificationPic')
            notificationItem['url'] != null && notificationItem['url'] != ''
                ? Container(
              alignment: Alignment.topLeft,
              child: Image.network(
                notificationItem['url'],
                fit: BoxFit.cover,
              ),
            )
                : Container(),
          notificationItem['type'] == 'notificationAppointment'
              ? Container(
            alignment: Alignment.topLeft,
            child: GestureDetector(
              onTap: () {
                Get.to(() => const AppointmentPage());
              },
              child: SizedBox(
                width: 100,
                height: 38,
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(
                        30,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000)
                            .withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      )
                    ],
                    color: const Color(0xFFFFB100),
                  ),
                  child: const Text(
                    "จองคิว",
                    style: TextStyle(
                      fontFamily: "Prompt-Medium",
                      fontSize:
                      16.0,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          )
              : Container(),
          notificationItem['type'] == 'notificationPhone'
              ? Container(
            alignment: Alignment.topLeft,
            child: GestureDetector(
              onTap: () {
                AppService.callPhone(notificationItem['phone']);
              },
              child: SizedBox(
                width: 100,
                height: 38,
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(
                        30,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000)
                            .withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      )
                    ],
                    color: const Color(0xFFFFB100),
                  ),
                  child: const Text(
                    "โทร",
                    style: TextStyle(
                      fontFamily: "Prompt-Medium",
                      fontSize: 16.0,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          )
              : Container(),
          notificationItem['type'] == 'notificationPic'
              ? notificationItem['url'] != null && notificationItem['url'] != ""
          ? InkWell(
            onTap: (){
              saveImage(notificationItem['url']);
            },
            child: AppWidget.boldText(context, "บันทึกรูปภาพ", 16, Colors.brown, FontWeight.w400),
          ) : const SizedBox()
              : const SizedBox()
        ],
      ),
    );
  }

  Future<void> saveImage(imageUrl) async {
    var response = await http.get(Uri.parse(imageUrl));
    final bytes = response.bodyBytes;
    final result = await ImageGallerySaver.saveImage(bytes);

    if (result['isSuccess']) {
      print('บันทึกรูปภาพสำเร็จ');
    } else {
      print('เกิดข้อผิดพลาดในการบันทึกรูปภาพ');
    }
  }

}