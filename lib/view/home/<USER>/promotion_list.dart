import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/promotion_view.dart';


class PromotionListPage extends StatefulWidget {
  const PromotionListPage({Key? key}) : super(key: key);

  @override
  State<PromotionListPage> createState() => _PromotionListPageState();
}

class _PromotionListPageState extends State<PromotionListPage> {

  final promotionCtl  = Get.find<PromotionController>();

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "PromotionList");
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.0, -1.0),
                  end: Alignment(0.0, 1.0),
                  colors: [Color(0xff505050), Color(0xff282828)],
                  stops: [0.0, 1.0],
                ),
              ),
            ),
            Column(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  width: Get.width,
                  height: 90,
                  padding: const EdgeInsets.only(
                      left: 18,
                      right: 18
                  ),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.0, -1.0),
                      end: Alignment(0.0, 1.0),
                      colors: [
                        Color(0xff505050),
                        Color(0xff282828)],
                      stops: [0.0, 1.0],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: (){
                          Navigator.pop(context);
                        },
                        child:  Container(
                          width: 50,
                          height: 50,
                          alignment: Alignment.centerLeft,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: Color(0xFFFFB100),
                            size: 16,
                          ),
                        ),
                      ),
                      AppWidget.boldTextS(
                          context,
                          "โปรโมชั่น",
                          14,
                          Colors.white,
                          FontWeight.w400),
                      const SizedBox(
                        width: 50,
                        height: 50,
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                      itemCount: promotionCtl.promotionList.data!.length,
                      itemBuilder: (BuildContext context, int index){
                        return buildContent(
                            context,
                          promotionCtl.promotionList.data![index].promotionId,
                            '${promotionCtl.urlEvent.value}${promotionCtl.promotionList.data![index].promotionPic}',
                          promotionCtl.promotionList.data![index].promotionName,
                            AppService.parseHtmlString(promotionCtl.promotionList.data![index].promotionBody.toString()),
                            );
                      }
                  ),
                )
              ],
            ),
          ],
        )
    );
  }

  buildContent(context, id, photo, name, body){
    return Container(
      margin: const EdgeInsets.all(18),
      child: CachedNetworkImage(
        imageUrl: photo,
        imageBuilder: (context, imageProvider) => Container(
          width: Get.width * 0.9,
          height: Get.width * 0.9,
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              alignment: Alignment.topCenter,
              image: imageProvider,
            ),
            borderRadius: const BorderRadius.all(
              Radius.circular(
                  15
              ),
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                bottom: 20,
                right: 20,
                child: Material(
                  color: const Color(0xFF000000).withOpacity(0.7),
                  borderRadius: const BorderRadius.all(
                    Radius.circular(
                        30
                    ),
                  ),
                  child: InkWell(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(
                          30
                      ),
                    ),
                    splashColor: Colors.white,
                    onTap: () {
                      Navigator.push(context,
                          MaterialPageRoute(
                            builder: (context) => PromotionViewPage(id, photo, name, body),
                          )
                      );
                    },
                    child: Container(
                      width: 85,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(
                          Radius.circular(
                            30,
                          ),
                        ),
                        border: Border.all(
                          color: Colors.black.withOpacity(0.95),
                        ),
                      ),
                      child: Center(
                        child: Text(
                          'ดูเพิ่มเติม',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 14,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        fit: BoxFit.cover,
        placeholder: (context, url) => const SizedBox(
          width: 50,
          height: 50,
          child: Center(
            child: CircularProgressIndicator(
              color: Colors.orange,
            ),
          ),
        ),
        errorWidget: (context, url, error) => const Icon(
          Icons.error,
          color: Color(0xFFFFB100),
        ),
      ),
    );
  }
}

