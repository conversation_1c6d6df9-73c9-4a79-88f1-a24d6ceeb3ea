import 'package:dotted_line/dotted_line.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/lifetime_appointment_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/insurance/insurance.dart';

class MyCarDetail extends StatefulWidget {
  const MyCarDetail({Key? key}) : super(key: key);

  @override
  State<MyCarDetail> createState() => _MyCarDetailState();
}

class _MyCarDetailState extends State<MyCarDetail> {

  final lifetimeAppointmentCtl = Get.put(LifetimeAppointmentController());
  final appointmentCtl = Get.put(AppointmentController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "LifetimeAppointment");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 1.0],
                colors: [
                  Color(0xFFEDEDED),
                  Color(0xFFF2F2F2),
                ],
              ),
            ),
          ),
          SingleChildScrollView(
            child: Stack(
              children: [
                Column(
                  children: [
                    const SizedBox(
                      height: 220,
                    ),
                    buildListViewCarData(),
                    const SizedBox(
                      height: 14,
                    ),
                    lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].miles!.isNotEmpty ?
                    buildListViewMiles() : const SizedBox(),
                    SizedBox(
                      height: lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].miles!.isNotEmpty ? 14 : 0,
                    ),
                    lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].rustproof!.isNotEmpty ?
                    buildListViewRust() : const SizedBox(),
                    SizedBox(
                      height: lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].rustproof!.isNotEmpty ? 14 : 0,
                    ),
                    lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].insurance!.isNotEmpty ?
                    buildListViewInsurance() : const SizedBox(),
                    SizedBox(
                      height: lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].insurance!.isNotEmpty ? 14 : 0,
                    ),
                    lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].pmg!.isNotEmpty ?
                    buildListViewPMG() : const SizedBox(),
                    const SizedBox(
                      height: 30,
                    )
                  ],
                ),
                Column(
                  children: [
                    const SizedBox(
                        height: 60
                    ),
                    Container(
                      width: Get.width,
                      padding: EdgeInsets.only(
                        left: Get.width < 500 ? 8 : 54,
                        right: Get.width < 500 ? 8 : 54,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () {
                              Get.back();
                            },
                            child: Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(15),
                                    topLeft: Radius.circular(15),
                                    bottomRight: Radius.circular(15),
                                    bottomLeft: Radius.circular(15),
                                  ),
                                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                              child: const Icon(
                                Icons.arrow_back_ios_new,
                                size: 18,
                                color: Color(0xFFFFB100),
                              ),
                            ),
                          ),
                          AppWidget.boldTextS(context, "รถของคุณ", Get.width < 500 ? 16 : 20, const Color(0xFF2B1710),
                              FontWeight.w500),
                          const SizedBox(
                            width: 36,
                            height: 36,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                    Container(
                      width: Get.width,
                      padding: EdgeInsets.only(
                        left: Get.width < 500 ? 26 : 70,
                        right: Get.width < 500 ? 26 : 70,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          lifetimeAppointmentCtl.lifetimeAppointment.carList!.length > 1 ?
                          InkWell(
                            onTap:(){
                              if (lifetimeAppointmentCtl.carSelectIndex.value > 0) {
                                lifetimeAppointmentCtl.carSelectIndex.value--;
                              }
                              lifetimeAppointmentCtl.scrollToPage(lifetimeAppointmentCtl.pageDetailController.page!.toInt() - 1);
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].miles!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage2(lifetimeAppointmentCtl.pageDetailController2.page!.toInt() - 1);
                              }
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].rustproof!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage3(lifetimeAppointmentCtl.pageDetailController3.page!.toInt() - 1);
                              }
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].insurance!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage4(lifetimeAppointmentCtl.pageDetailController4.page!.toInt() - 1);
                              }
                            },
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                              alignment: Alignment.center,
                              child: const Icon(
                                Icons.arrow_back_ios_new,
                                size: 12,
                                color: Color(0xFF282828),
                              ),),
                          ) : const SizedBox(),
                          Image.network(
                            lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].carImage.toString(),
                            width: 220,
                            height: 125,
                            errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                              return Container(
                                width: 220,
                                height: 125,
                                padding: const EdgeInsets.all(15),
                                child: Image.asset("assets/image/car_detail/car_mockup.png",
                                ),
                              );
                            },
                            loadingBuilder: (BuildContext context, Widget child,
                                ImageChunkEvent? loadingProgress) {
                              if (loadingProgress == null) {
                                return child;
                              }
                              return Container(
                                  width: 190,
                                  height: 125,
                                  alignment: Alignment.center,
                                  child: Stack(
                                    children: [
                                      Container(
                                        width: 190,
                                        height: 125,
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.all(10),
                                        child: Image.asset("assets/image/car_detail/car_mockup.png",),
                                      ),
                                      Align(
                                        alignment: Alignment.center,
                                        child: SizedBox(
                                          width: 25,
                                          height: 25,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: const Color(0xFFFFFFFF),
                                            value: loadingProgress.expectedTotalBytes != null
                                                ? loadingProgress.cumulativeBytesLoaded /
                                                loadingProgress.expectedTotalBytes!
                                                : null,
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                              );
                            },
                          ),
                          lifetimeAppointmentCtl.lifetimeAppointment.carList!.length > 1 ?
                          InkWell(
                            onTap:(){
                              if (lifetimeAppointmentCtl.carSelectIndex.value < lifetimeAppointmentCtl.lifetimeAppointment.carList!.length - 1) {
                                lifetimeAppointmentCtl.carSelectIndex.value++;
                              }
                              lifetimeAppointmentCtl.scrollToPage(lifetimeAppointmentCtl.pageDetailController.page!.toInt() + 1);
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].miles!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage2(lifetimeAppointmentCtl.pageDetailController2.page!.toInt() + 1);
                              }
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].rustproof!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage3(lifetimeAppointmentCtl.pageDetailController3.page!.toInt() + 1);
                              }
                              if(lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].insurance!.isNotEmpty){
                                lifetimeAppointmentCtl.scrollToPage4(lifetimeAppointmentCtl.pageDetailController4.page!.toInt() + 1);
                              }
                            },
                            child: Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                              alignment: Alignment.center,
                              child: const Icon(
                                Icons.arrow_forward_ios,
                                size: 12,
                                color: Color(0xFF282828),
                              ),),
                          ): const SizedBox(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                )
              ],
            ),
          ),

        ],
      ))
    );
  }

  appointmentButton(){
    return InkWell(
      onTap: (){
        appointmentCtl.carRegSelect.value = lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].reg.toString();
        Get.to(() => const AppointmentPage());
      },
      child: Container(
        width: 109,
        height: 34,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
          ),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 0.50,
              strokeAlign: BorderSide.strokeAlignOutside,
              color: Color(0xFFFFB100)
            ),

            borderRadius: BorderRadius.circular(10),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x0C000000),
              blurRadius: 10,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.string('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5305)"> <path d="M17.5 10V14.5C17.5 16.15 16.15 17.5 14.5 17.5H5.49998C3.84998 17.5 2.5 16.15 2.5 14.5V5.50004C2.5 3.85004 3.84998 2.50003 5.49998 2.50003H10" stroke="#664701" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M15.4167 7.64171L12 11.0584C11.85 11.2084 11.675 11.2917 11.4667 11.3084L9.42499 11.5C8.89165 11.55 8.44166 11.0917 8.49166 10.5584L8.67497 8.57504C8.69163 8.3667 8.77501 8.18336 8.92501 8.04169L12.375 4.59171L15.4167 7.64171V7.64171Z" stroke="#664701" stroke-linejoin="round"/> <path d="M17.25 5.81673L15.4167 7.64173L12.375 4.59173L14.2 2.76672C14.5333 2.43339 15.0834 2.43339 15.4167 2.76672L17.25 4.59173C17.575 4.92506 17.575 5.48339 17.25 5.81673V5.81673Z" stroke="#664701" stroke-linejoin="round"/></g><defs> <clipPath id="clip0_3260_5305"> <rect width="20" height="20" fill="white"/></clipPath></defs></svg>'),
            const SizedBox(width: 5),
            const Text(
              'นัดหมาย',
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Color(0xFF282828),
                fontSize: 12,
                fontFamily: 'Prompt',
                fontWeight: FontWeight.w400,
                shadows: [
                  Shadow(
                    color: Color(0x0C000000),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildListViewCarData(){
    return SizedBox(
      height: 266,
      child: PageView.builder(
          clipBehavior: Clip.none,
          itemCount: lifetimeAppointmentCtl.lifetimeAppointment.carList!.length,
          controller: lifetimeAppointmentCtl.pageDetailController,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) =>
              Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: Get.width,
                      height: 218,
                      margin: EdgeInsets.only(
                        left: Get.width < 500 ? 8 : 54,
                        right: Get.width < 500 ? 8 : 54,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 1.0],
                          colors: [
                            Color(0xFFFFFFFF),
                            Color(0xFFF8F8F8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: Get.width,
                            height: 44,
                            padding: const EdgeInsets.only(
                                left: 10
                            ),
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(15),
                                topRight: Radius.circular(15),
                              ),
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                stops: [0.0, 0.5, 1.0],
                                colors: [
                                  Color(0xFFFEFEFE),
                                  Color(0xFFFCFCFC),
                                  Color(0xFFF8F8F8),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  spreadRadius: 0,
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            alignment: Alignment.centerLeft,
                            child: AppWidget.boldTextS(context, "ข้อมูลรถ", 14, const Color(0xFF282828),
                                FontWeight.w500),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  ทะเบียน", 12, const Color(0xFF777777),
                                  FontWeight.w300),
                              AppWidget.normalTextS(context, "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].reg != "" ? lifetimeAppointmentCtl.lifetimeAppointment.carList![index].reg : "ไม่ทราบข้อมูล"}  ", 12, const Color(0xFF282828),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  รุ่นรถ", 12, const Color(0xFF777777),
                                  FontWeight.w300),
                              AppWidget.normalTextS(context, "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModelSa != "" ? lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModelSa : "ไม่ทราบข้อมูล"}  ", 12, const Color(0xFF282828),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              AppWidget.normalText(context, "  เกรด", 12, const Color(0xFF777777),
                                  FontWeight.w300),
                              AppWidget.normalTextS(context, "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModel != "" ? lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carModel : "ไม่ทราบข้อมูล"}  ", 12, const Color(0xFF895F00),
                                  FontWeight.w300),
                            ],
                          ),
                          DottedLine(
                            dashColor: const Color(0xFF000000).withOpacity(0.05),
                            lineThickness: 1,
                          ),
                         Row(
                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                           children: [
                             AppWidget.normalText(context, "  เลขประจำตัวรถ", 12, const Color(0xFF777777),
                                 FontWeight.w300),
                             AppWidget.normalTextS(context, "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carVIN != "" ? lifetimeAppointmentCtl.lifetimeAppointment.carList![index].carVIN : "ไม่ทราบข้อมูล"}  ", 12, const Color(0xFF282828),
                                 FontWeight.w300),
                           ],
                         ),
                          const SizedBox(
                            height: 4,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )
      ),
    );
  }

  buildListViewMiles(){
    return SizedBox(
      height: 248,
      child: PageView.builder(
          clipBehavior: Clip.none,
          itemCount: lifetimeAppointmentCtl.lifetimeAppointment.carList!.length,
          controller: lifetimeAppointmentCtl.pageDetailController2,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) =>
              Container(
                width: Get.width,
                height: 248,
                margin: EdgeInsets.only(
                  left: Get.width < 500 ? 8 : 54,
                  right: Get.width < 500 ? 8 : 54,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF8F8F8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: Get.width,
                      height: 44,
                      padding: const EdgeInsets.only(
                          left: 10
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 0.5, 1.0],
                          colors: [
                            Color(0xFFFEFEFE),
                            Color(0xFFFCFCFC),
                            Color(0xFFF8F8F8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      alignment: Alignment.centerLeft,
                      child: AppWidget.boldTextS(context, "การเช็กระยะ", 14, const Color(0xFF282828),
                          FontWeight.w500),
                    ),
                    Container(
                        width: Get.width,
                        height: 35,
                        // color: const Color(0xFFFFEDED),
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                                              SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                                AppWidget.normalText(context, " ครบกำหนดครั้งถัดไป", 12, const Color(0xFFFA4862),
                                    FontWeight.w400),
                              ],
                            ),
                            AppWidget.boldText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate != "" ?
                                " ${lifetimeAppointmentCtl.dateEngToThaiMiniPlus6M("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}")}" : "ไม่ทราบข้อมูล",
                                12, const Color(0xFFFA4862),
                                FontWeight.w500),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          AppWidget.boldText(context,
                              lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileage != "" ?
                              "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileage} กม." : "ไม่ทราบข้อมูล",
                              18, const Color(0xFF2B1710),
                              FontWeight.w500),
                          appointmentButton()
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            AppWidget.normalText(context, "ครั้งล่าสุด", 12, const Color(0xFF777777),
                                FontWeight.w300),
                            AppWidget.normalText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate != "" ?
                                AppService.dateEngToThaiMini("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileageDate}") : "ไม่ทราบข้อมูล",
                                12, const Color(0xFF282828),
                                FontWeight.w400),
                          ],
                        )
                    ),

                    const SizedBox(
                      height: 8,
                    ),
                    DottedLine(
                      dashColor: const Color(0xFF000000).withOpacity(0.05),
                      lineThickness: 1,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            AppWidget.normalText(context, "ระยะทาง", 12, const Color(0xFF777777),
                                FontWeight.w300),
                            AppWidget.normalText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileage != "" ?
                                "${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileage} กม." : "ไม่ทราบข้อมูล",
                                12, const Color(0xFF282828),
                                FontWeight.w400),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: AppWidget.normalText(context, "เงื่อนไขการเช็กระยะ", 12, const Color(0xFF282828),
                          FontWeight.w500),
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: AppWidget.normalText(context, "ทุก 6 เดือน หรือ ${AppService.numberFormatNon0(int.parse(lifetimeAppointmentCtl.lifetimeAppointment.carList![index].miles![0].mileage!.replaceAll(',', '')) + 10000)} กม. แล้วแต่อย่างใดอย่างหนึ่ง", 12, const Color(0xFF895F00),
                          FontWeight.w300),
                    ),
                  ],
                ),
              )
      ),
    );
  }

  buildListViewRust(){
    return SizedBox(
      height: 138,
      child: PageView.builder(
          clipBehavior: Clip.none,
          itemCount: lifetimeAppointmentCtl.lifetimeAppointment.carList!.length,
          controller: lifetimeAppointmentCtl.pageDetailController3,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) =>
              Container(
                width: Get.width,
                height: 138,
                margin: EdgeInsets.only(
                  left: Get.width < 500 ? 8 : 54,
                  right: Get.width < 500 ? 8 : 54,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF8F8F8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: Get.width,
                      height: 44,
                      padding: const EdgeInsets.only(
                          left: 10
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 0.5, 1.0],
                          colors: [
                            Color(0xFFFEFEFE),
                            Color(0xFFFCFCFC),
                            Color(0xFFF8F8F8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      alignment: Alignment.centerLeft,
                      child: AppWidget.boldTextS(context, "เช็กพ่นกันสนิม", 14, const Color(0xFF282828),
                          FontWeight.w500),
                    ),
                    Container(
                        width: Get.width,
                        height: 35,
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                                              SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                                AppWidget.normalText(context, " ครบกำหนดครั้งถัดไป", 12, const Color(0xFFFA4862),
                                    FontWeight.w400),
                              ],
                            ),
                            AppWidget.boldText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate != "" ?
                                " ${lifetimeAppointmentCtl.dateEngToThaiMiniPlus1Y("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}")}" : "ไม่ทราบข้อมูล",
                                12, const Color(0xFFFA4862),
                                FontWeight.w500),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    DottedLine(
                      dashColor: const Color(0xFF000000).withOpacity(0.05),
                      lineThickness: 1,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(context, "ครั้งล่าสุด", 12, const Color(0xFF777777),
                                  FontWeight.w400),
                              AppWidget.boldText(context,
                                  lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate != "" ?
                                  AppService.dateEngToThaiMini("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].rustproof![0].rustDate}") : "ไม่ทราบข้อมูล",
                                  12, const Color(0xFF777777),
                                  FontWeight.w400),
                            ],
                          ),
                          appointmentButton()
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                  ],
                ),
              )
      ),
    );
  }

  buildListViewInsurance(){
    return SizedBox(
      height: 138,
      child: PageView.builder(
          clipBehavior: Clip.none,
          itemCount: lifetimeAppointmentCtl.lifetimeAppointment.carList!.length,
          controller: lifetimeAppointmentCtl.pageDetailController4,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) =>
              Container(
                width: Get.width,
                height: 138,
                margin: EdgeInsets.only(
                  left: Get.width < 500 ? 8 : 54,
                  right: Get.width < 500 ? 8 : 54,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF8F8F8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: Get.width,
                      height: 44,
                      padding: const EdgeInsets.only(
                          left: 10
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 0.5, 1.0],
                          colors: [
                            Color(0xFFFEFEFE),
                            Color(0xFFFCFCFC),
                            Color(0xFFF8F8F8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      alignment: Alignment.centerLeft,
                      child: AppWidget.boldTextS(context, "ประกัน / พรบ.", 14, const Color(0xFF282828),
                          FontWeight.w500),
                    ),
                    Container(
                        width: Get.width,
                        height: 35,
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                                              SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                                AppWidget.normalText(context, " ครบกำหนดครั้งถัดไป", 12, const Color(0xFFFA4862),
                                    FontWeight.w400),
                              ],
                            ),
                            AppWidget.boldText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd != "" ?
                                " ${lifetimeAppointmentCtl.dateEngToThaiMini("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDateEnd}")}" : "ไม่ทราบข้อมูล",
                                12, const Color(0xFFFA4862),
                                FontWeight.w500),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    DottedLine(
                      dashColor: const Color(0xFF000000).withOpacity(0.05),
                      lineThickness: 1,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(context, "ครั้งล่าสุด : ", 12, const Color(0xFF777777),
                                  FontWeight.w400),
                              AppWidget.normalText(context,
                                  lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDate != "" ?
                                  AppService.dateEngToThaiMini("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].insurance![0].insuranceDate}") : "ไม่ทราบข้อมูล",
                                  12, const Color(0xFF777777),
                                  FontWeight.w400),
                            ],
                          ),
                          InkWell(
                        onTap: (){
                          // appointmentCtl.carRegSelect.value = lifetimeAppointmentCtl.lifetimeAppointment.carList![lifetimeAppointmentCtl.carSelectIndex.value].reg.toString();
                          Get.to(() => const InsurancePage());
                        },
                        child: Container(
                          width: 109,
                          height: 34,
                          decoration: ShapeDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0, 1),
                              colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                            ),
                            shape: RoundedRectangleBorder(
                              side: const BorderSide(
                                  width: 0.50,
                                  strokeAlign: BorderSide.strokeAlignOutside,
                                  color: Color(0xFFFFB100)
                              ),

                              borderRadius: BorderRadius.circular(10),
                            ),
                            shadows: const [
                              BoxShadow(
                                color: Color(0x0C000000),
                                blurRadius: 10,
                                offset: Offset(0, 4),
                                spreadRadius: 0,
                              )
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SvgPicture.string('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5305)"> <path d="M17.5 10V14.5C17.5 16.15 16.15 17.5 14.5 17.5H5.49998C3.84998 17.5 2.5 16.15 2.5 14.5V5.50004C2.5 3.85004 3.84998 2.50003 5.49998 2.50003H10" stroke="#664701" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M15.4167 7.64171L12 11.0584C11.85 11.2084 11.675 11.2917 11.4667 11.3084L9.42499 11.5C8.89165 11.55 8.44166 11.0917 8.49166 10.5584L8.67497 8.57504C8.69163 8.3667 8.77501 8.18336 8.92501 8.04169L12.375 4.59171L15.4167 7.64171V7.64171Z" stroke="#664701" stroke-linejoin="round"/> <path d="M17.25 5.81673L15.4167 7.64173L12.375 4.59173L14.2 2.76672C14.5333 2.43339 15.0834 2.43339 15.4167 2.76672L17.25 4.59173C17.575 4.92506 17.575 5.48339 17.25 5.81673V5.81673Z" stroke="#664701" stroke-linejoin="round"/></g><defs> <clipPath id="clip0_3260_5305"> <rect width="20" height="20" fill="white"/></clipPath></defs></svg>'),
                              const SizedBox(width: 5),
                              const Text(
                                'นัดหมาย',
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  color: Color(0xFF282828),
                                  fontSize: 12,
                                  fontFamily: 'Prompt',
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                  ],
                ),
              )
      ),
    );
  }

  buildListViewPMG(){
    return SizedBox(
      height: 138,
      child: PageView.builder(
          clipBehavior: Clip.none,
          itemCount: lifetimeAppointmentCtl.lifetimeAppointment.carList!.length,
          controller: lifetimeAppointmentCtl.pageDetailController4,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemBuilder: (BuildContext context, int index) =>
              Container(
                width: Get.width,
                height: 138,
                margin: EdgeInsets.only(
                  left: Get.width < 500 ? 8 : 54,
                  right: Get.width < 500 ? 8 : 54,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF8F8F8),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      spreadRadius: 0,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: Get.width,
                      height: 44,
                      padding: const EdgeInsets.only(
                          left: 10
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          topRight: Radius.circular(15),
                        ),
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 0.5, 1.0],
                          colors: [
                            Color(0xFFFEFEFE),
                            Color(0xFFFCFCFC),
                            Color(0xFFF8F8F8),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      alignment: Alignment.centerLeft,
                      child: AppWidget.boldTextS(context, "เคลือบแก้ว", 14, const Color(0xFF282828),
                          FontWeight.w500),
                    ),
                    Container(
                        width: Get.width,
                        height: 35,
                        padding: const EdgeInsets.only(
                            left: 10,
                            right: 10
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                SvgPicture.string('<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3260_5302)"> <circle cx="9.5" cy="9.00003" r="7.5" stroke="url(#paint0_linear_3260_5302)" stroke-width="1.5"/> <path d="M9.49326 11.25H9.5" stroke="url(#paint1_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9.5 9.00003L9.5 6.00003" stroke="url(#paint2_linear_3260_5302)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g><defs> <linearGradient id="paint0_linear_3260_5302" x1="9.55612" y1="1.17146" x2="9.55612" y2="16.1715" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint1_linear_3260_5302" x1="9.4968" y1="11.2336" x2="9.4968" y2="11.9836" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <linearGradient id="paint2_linear_3260_5302" x1="8.99626" y1="9.06574" x2="8.99626" y2="6.06574" gradientUnits="userSpaceOnUse"> <stop stop-opacity="0.7"/> <stop offset="1"/></linearGradient> <clipPath id="clip0_3260_5302"> <rect width="18" height="18" fill="white" transform="translate(0.5 3.05176e-05)"/></clipPath></defs></svg>', color: Colors.black,),
                                AppWidget.normalText(context, " ครบกำหนดครั้งถัดไป", 12, const Color(0xFFFA4862),
                                    FontWeight.w400),
                              ],
                            ),
                            AppWidget.boldText(context,
                                lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate != "" ?
                                " ${lifetimeAppointmentCtl.dateEngToThaiMiniPlus6M("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}")}" : "ไม่ทราบข้อมูล",
                                12, const Color(0xFFFA4862),
                                FontWeight.w500),
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    DottedLine(
                      dashColor: const Color(0xFF000000).withOpacity(0.05),
                      lineThickness: 1,
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      padding: const EdgeInsets.only(
                          left: 10,
                          right: 10
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.normalText(context, "ครั้งล่าสุด", 12, const Color(0xFF777777),
                                  FontWeight.w400),
                              AppWidget.boldText(context,
                                  lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate != "" ?
                                  lifetimeAppointmentCtl.dateEngToThaiMiniPlus4M("${lifetimeAppointmentCtl.lifetimeAppointment.carList![index].pmg![0].pmgDate}") : "ไม่ทราบข้อมูล",
                                  12, const Color(0xFF777777),
                                  FontWeight.w400),
                            ],
                          ),
                          appointmentButton()
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                  ],
                ),
              )
      ),
    );
  }
}
