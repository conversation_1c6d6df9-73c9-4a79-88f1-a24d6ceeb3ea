import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class NotificationController extends GetxController {

  RxList notificationItems = [].obs;

  final profileCtl = Get.put(ProfileController());

  getNotification() async {
    print("getNotification");
    var notification = await FireStore.getNotification(profileCtl.profile.value.mobile.toString());
    print(notification);
    if (notification['status']) {
        notificationItems.value = notification['data'];
      update();
    } else {
      AppAlert.toastError('ผิดพลาด!! ดึงข้อมูลแจ้งเตือนไม่ได้');
    }
  }

  Future<void> deleteNotification(context, String docID) async {
    AppLoader.loader(context);
    var deleteNoti = await FireStore.deleteByDocIdNotification(profileCtl.profile.value.mobile.toString(), docID);
    if(deleteNoti){
      await getNotification();
    }
    Get.back();
    AppLoader.dismiss(context);
    update();
  }


}