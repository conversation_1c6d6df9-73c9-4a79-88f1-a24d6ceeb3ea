import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/member.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/regis_bookbank_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/show_detail_regis_MR.dart';

import '../../../component/url.dart';

class RegisAccountMRPage extends StatefulWidget {
  const RegisAccountMRPage({Key? key}) : super(key: key);

  @override
  State<RegisAccountMRPage> createState() => _RegisAccountMRPageState();
}

class _RegisAccountMRPageState extends State<RegisAccountMRPage> {
  final SecureStorage secureStorage = SecureStorage();

  final TextEditingController _fullName = TextEditingController();
  final TextEditingController _idCard = TextEditingController();
  final TextEditingController _phone = TextEditingController();
  final TextEditingController _noteCareer	 = TextEditingController();
  final TextEditingController _notePlaceNameCareer	 = TextEditingController();

  int userID = 0;

  List<String> ListJob = [
    'เกษตรกรรม',
    'ค้าขาย',
    'เจ้าของธุรกิจ',
    'ข้าราชการ',
    'พนักงานเอกชน',
    'พนักงานโรงงาน',
    'รับจ้างทั่วไป',
    'พนักงานรัฐวิสาหกิจ'
  ];
  String? jobSelect;

  bool openButton = false;
  bool openJob = false;
  bool showSwitchStatus = false;
  bool lineStatus = false;
  bool facebookStatus = false;
  bool isLoading = true;

  ResponseMember? resProfile;

  final profileCtl = Get.put(ProfileController());

  @override
  void initState() {
    super.initState();
    getProfile();
  }

  getProfile() async {
      setState(() {
        userID = profileCtl.profile.value.id!;
        _fullName.text = profileCtl.profile.value.fullname!;
        _idCard.text = profileCtl.profile.value.idcard!;
        _phone.text = profileCtl.profile.value.mobile!;
      });
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Scaffold(
              body: Stack(
                children: <Widget>[
                  Container(
                    width: _width,
                    height: _height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: _width,
                    height: 125.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xFFFFB100).withOpacity(1),
                          const Color(0xFFFFC700).withOpacity(0.7),
                          const Color(0xFFFFC700).withOpacity(0.4),
                          const Color(0xFFFFC700).withOpacity(0),
                        ],
                      ),
                    ),
                  ),
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(
                            left: 23.w,
                            top: 12.h,
                          ),
                          width: _width,
                          height: 700.h,
                          child: Stack(
                            children: [
                              Column(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 12.h),
                                    width: 400.w,
                                    height: 80.h,
                                    child: Container(
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          InkWell(
                                            onTap: (){
                                              Navigator.pushReplacement(context,
                                                  MaterialPageRoute(builder: (context) => const ShowDetailRegisMR()));
                                            },
                                            child: SizedBox(
                                              height: 30.h,
                                              child: Icon(Icons.arrow_back_ios_new,
                                                color: const Color(0xFF282828),
                                                size: 15.w,
                                              ),
                                            ),
                                          ),
                                          Container(
                                            margin: EdgeInsets.only(
                                                top: 27.h,
                                                left: 16.w
                                            ),
                                            width: 185.w,
                                            height: 50.h,
                                            child: RichText(
                                              text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: 'ข้อมูลสมัครตัวแทนผู้แนะนำ',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.w500,
                                                        fontFamily: 'Prompt-Medium',
                                                        color: const Color(0xFF282828),
                                                        fontSize: 14.w,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: '\nกรุณาใส่รายละเอียดข้อมูลให้ครบถ้วน',
                                                      style: TextStyle(
                                                        fontWeight: FontWeight.w400,
                                                        fontFamily: 'Prompt',
                                                        color: const Color(0xFF664701),
                                                        fontSize: 11.w,
                                                      ),
                                                    ),
                                                  ]
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      top: 26.h,
                                      right: 24.w,
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('ชื่อ - นามสกุล',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Colors.black,
                                            fontSize: 12.w,
                                          ),
                                        ),
                                        TextField(
                                          controller: _fullName,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                              AppService.thaiAZ100RegExp(),
                                            ),
                                            FilteringTextInputFormatter.deny(AppService.deny()),
                                          ],
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF664701),
                                            fontSize: 12.w,
                                          ),
                                          decoration: InputDecoration(
                                            hintText: 'พิมพ์ชื่อ-นามสกุล',
                                            hintStyle: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Prompt',
                                              color: const Color(0xFF707070).withOpacity(0.5),
                                              fontSize: 12.w,
                                            ),
                                            enabledBorder: UnderlineInputBorder(
                                              borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                                            ),
                                            focusedBorder: const UnderlineInputBorder(
                                              borderSide: BorderSide(color: Color(0xFF895F00)),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('เลขบัตรประชาชน',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              controller: _idCard,
                                              keyboardType: TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter.allow(
                                                  AppService.number13RegExp(),
                                                ),
                                              ],
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText: 'กรอกหมายเลขบัตรประชาชน',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070).withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder: UnderlineInputBorder(
                                                  borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                                                ),
                                                focusedBorder: const UnderlineInputBorder(
                                                  borderSide: BorderSide(color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('เบอร์โทรศัพท์',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            TextField(
                                              controller: _phone,
                                              keyboardType: TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter.allow(
                                                  AppService.number10RegExp(),
                                                ),
                                              ],
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: const Color(0xFF664701),
                                                fontSize: 12.w,
                                              ),
                                              decoration: InputDecoration(
                                                hintText: 'กรอกหมายเลขโทรศัพท์',
                                                hintStyle: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070).withOpacity(0.5),
                                                  fontSize: 12.w,
                                                ),
                                                enabledBorder: UnderlineInputBorder(
                                                  borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                                                ),
                                                focusedBorder: const UnderlineInputBorder(
                                                  borderSide: BorderSide(color: Color(0xFF895F00)),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Column(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text('อาชีพ',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Prompt',
                                                color: Colors.black,
                                                fontSize: 12.w,
                                              ),
                                            ),
                                            selectJob(context, ListJob),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 12.h,
                                        ),
                                        Container(
                                          child: showJobSelect(jobSelect),
                                        ),
                                      ],
                                    ),
                                  ),

                                ],
                              ),
                              Positioned(
                                top: 30.h,
                                right: 0.w,
                                child: Image.asset('assets/image/MR/LadyWhite.png',
                                  opacity: const AlwaysStoppedAnimation(.6),
                                  width: 137.w,
                                  height: 127.h,
                                ),
                              ),
                            ],
                          ),
                        ),
                        openButton != false
                            ? Padding(
                          padding: EdgeInsets.only(bottom: 30.h),
                          child: InkWell(
                            onTap: () async {
                              saveData();
                            },
                            child: Container(
                                margin: EdgeInsets.only(top: 30.h),
                                width: 323.w,
                                height: 40.h,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5.h),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.black45,
                                      offset: Offset(1, 1),
                                      blurRadius: 2,
                                    ),
                                  ],
                                  color: const Color(0xFF282828),
                                ),
                                child: Center(
                                  child: Text(
                                    'ดำเนินการต่อ',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt-Medium',
                                      color: Colors.white,
                                      fontSize: 14.h,
                                    ),
                                  ),
                                )

                            ),
                          ),
                        )
                            : Container(),
                        Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom)),
                      ],
                    ),

                  ),
                ],
              ),
            ),
          );
        }
    );
  }

  selectJob(context, items) {
    return GestureDetector(
      onTap: () {
        setState(() {
          openJob = true;
        });
        showCupertinoModalPopup(
          context: context,
          builder: (context) {
            int selectTempIndex;
            if (jobSelect != null) {
              selectTempIndex = items.indexOf(jobSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      CupertinoButton(
                        onPressed: () {
                          openJob = false;
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ยกเลิก',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'เลือกอาชีพ',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () {
                          openJob = false;
                          setState(() {
                            jobSelect = items[selectTempIndex];
                          });
                          showButton();
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: Text(
                          'ตกลง',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: const Color(0xFF282828),
                            fontSize: 13.w,
                            letterSpacing: 0.4,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  height: 150,
                  color: CupertinoColors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: CupertinoColors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 13.w,
                            color: const Color(0xFF282828),
                            letterSpacing: 0.4,
                          ),
                        ),
                      );
                    }),
                  ),
                )
              ],
            );
          },
        );
      },
      child: Container(
        width: 375.w,
        padding: EdgeInsets.symmetric(
          vertical: 10.h,
        ),
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(
            color: const Color(0xFF895F00).withOpacity(0.5),
            width: 1.0,
          ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            jobSelect == null
                ? SizedBox(
              width: 310.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'เลือกอาชีพ',
                    style:  TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 12.w,
                    ),
                  ),
                  openJob == false
                      ? Icon(Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            )
                : SizedBox(
              width: 310.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(jobSelect!,
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF664701),
                      fontSize: 12.w,
                    ),
                  ),
                  openJob == false
                      ? Icon(Icons.keyboard_arrow_down,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                      : Icon(Icons.keyboard_arrow_up,
                    size: 16.w,
                    color: const Color(0xFF895F00),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  showJobSelect (jobSelect){
    try{
      switch (jobSelect) {
        case 'เกษตรกรรม':
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('ระบุเพิ่มเติม',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Colors.black,
                  fontSize: 12.w,
                ),
              ),
              TextField(
                onChanged: (value) {
                  showButton();
                },
                controller: _noteCareer,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    AppService.thaiAZ100RegExp(),
                  ),
                ],
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF664701),
                  fontSize: 12.w,
                ),
                decoration: InputDecoration(
                  hintText: 'เช่น ทำสวนทุเรียน, ทำนาข้าว, ทำไร่ข้าวโพด เป็นต้น',
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070).withOpacity(0.5),
                    fontSize: 12.w,
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFF895F00)),
                  ),
                ),
              ),
            ],
          );
          break;
        case 'ค้าขาย' :
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('ระบุเพิ่มเติม',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Colors.black,
                  fontSize: 12.w,
                ),
              ),
              TextField(
                onChanged: (value) {
                  setState(() {
                    showButton();
                  });
                },
                controller: _noteCareer,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    AppService.thaiAZ100RegExp(),
                  ),
                ],
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF664701),
                  fontSize: 12.w,
                ),
                decoration: InputDecoration(
                  hintText: 'เช่น ขายอาหารตามสั่ง, ขายขนม, ขายยำ เป็นต้น',
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070).withOpacity(0.5),
                    fontSize: 12.w,
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFF895F00)),
                  ),
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              Text('ชื่อร้าน (ถ้ามี)',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Colors.black,
                  fontSize: 12.w,
                ),
              ),
              TextField(
                controller: _notePlaceNameCareer,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    AppService.thaiAZ100RegExp(),
                  ),
                ],
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF664701),
                  fontSize: 12.w,
                ),
                decoration: InputDecoration(
                  hintText: 'เช่น ร้านขายอาหารตามสั่ง, ร้านขายขนม เป็นต้น',
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070).withOpacity(0.5),
                    fontSize: 12.w,
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFF895F00)),
                  ),
                ),
              ),
            ],
          );
          break;
        case 'เจ้าของธุรกิจ' :
          return Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('ระบุเพิ่มเติม',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Colors.black,
                    fontSize: 12.w,
                  ),
                ),
                TextField(
                  controller: _noteCareer,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      AppService.thaiAZ100RegExp(),
                    ),
                  ],
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF664701),
                    fontSize: 12.w,
                  ),
                  decoration: InputDecoration(
                    hintText: 'เช่น ธุรกิจโรงแรม, ทำรีสอร์ท, ร้านกาแฟ เป็นต้น',
                    hintStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 12.w,
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF895F00)),
                    ),
                  ),
                ),
                SizedBox(
                  height: 15.h,
                ),
                Text('ชื่อธุรกิจ',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Colors.black,
                    fontSize: 12.w,
                  ),
                ),
                TextField(
                  onChanged: (value) {
                    setState(() {
                      showButton();
                    });
                  },
                  controller: _notePlaceNameCareer,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      AppService.thaiAZ100RegExp(),
                    ),
                  ],
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF664701),
                    fontSize: 12.w,
                  ),
                  decoration: InputDecoration(
                    hintText: 'เช่น รีสอร์ทน้ำใส, ร้านปังปิงคาเฟ่ เป็นต้น',
                    hintStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Prompt',
                      color: const Color(0xFF707070).withOpacity(0.5),
                      fontSize: 12.w,
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF895F00)),
                    ),
                  ),
                ),
              ],
            ),
          );
          break;
        case 'ข้าราชการ' :
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('ระบุเพิ่มเติม',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Colors.black,
                  fontSize: 12.w,
                ),
              ),
              TextField(
                onChanged: (value) {
                  setState(() {
                    showButton();
                  });
                },
                controller: _noteCareer,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    AppService.thaiAZ100RegExp(),
                  ),
                ],
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: const Color(0xFF664701),
                  fontSize: 12.w,
                ),
                decoration: InputDecoration(
                  hintText: 'เช่น ครู, ทหาร, ตำรวจ, กำนันผู้ใหญ่บ้าน เป็นต้น',
                  hintStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070).withOpacity(0.5),
                    fontSize: 12.w,
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: const Color(0xFF895F00).withOpacity(0.5)),
                  ),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFF895F00)),
                  ),
                ),
              ),
            ],
          );
          break;
      }
    }catch (e){
      print('error showJobSelect');
      print(e);
    }
  }

  showButton(){
    try{
      if(_fullName.text != "" &&
          _idCard.text != "" &&
          _phone.text != "" &&
          jobSelect != "") {
        openButton = true;
      }
    }catch(e){
      print(e);
    }
  }

  saveData() async {
    try{
      if(jobSelect == "เกษตรกรรม"){
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != "" &&
            _noteCareer.text != ""
        ){
          AppLoader.loader(context);
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          profileCtl.profile.value.careerNoteMR = _noteCareer.text;
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
            'careerNote': _noteCareer.text,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      } else if (jobSelect == "ค้าขาย"){
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != "" &&
            _noteCareer.text != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          profileCtl.profile.value.careerNoteMR = _noteCareer.text;
          profileCtl.profile.value.businessNameMR = _notePlaceNameCareer.text;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
            'careerNote': _noteCareer.text,
            'businessName': _notePlaceNameCareer.text,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      } else if (jobSelect == "เจ้าของธุรกิจ"){
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != "" &&
            _noteCareer.text != "" &&
            _notePlaceNameCareer.text != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          profileCtl.profile.value.careerNoteMR = _noteCareer.text;
          profileCtl.profile.value.businessNameMR = _notePlaceNameCareer.text;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
            'careerNote': _noteCareer.text,
            'businessName': _notePlaceNameCareer.text,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      } else if(jobSelect == "ข้าราชการ") {
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != "" &&
            _noteCareer.text != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          profileCtl.profile.value.careerNoteMR = _noteCareer.text;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
            'careerNote': _noteCareer.text,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      }else if(jobSelect == "พนักงานเอกชน") {
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      }else if(jobSelect == "พนักงานโรงงาน") {
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      }else if(jobSelect == "รับจ้างทั่วไป") {
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      }else if(jobSelect == "พนักงานรัฐวิสาหกิจ") {
        if(
        _fullName.text != "" &&
            _idCard.text != "" &&
            _phone.text.length == 10 &&
            jobSelect != ""
        ){
          profileCtl.profile.value.fullNameMR = _fullName.text;
          profileCtl.profile.value.idCardMR = _idCard.text;
          profileCtl.profile.value.phoneNumberMR = _phone.text;
          profileCtl.profile.value.careerMR = jobSelect;
          AppLoader.loader(context);
          Map data = {
            'MrCode': profileCtl.profile.value.mrCode,
            'fullname': _fullName.text,
            'idcard': _idCard.text,
            'phone': _phone.text,
            'career': jobSelect,
          };
          final response = await AppApi.post(AppUrl.updateInformationPersonal, data);
          if (response['status'] == 200) {
            AppLoader.dismiss(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RegisBookbankMRPage(),
              ),
            );
          } else {
            AppAlert.showError(context, 'ผิดพลาด!! บันทึกข้อมูลไม่ได้', 'ตกลง');
            AppLoader.dismiss(context);
          }
        } else {
          AppAlert.showNewAccept(context, 'ตรวจสอบข้อมูล', 'กรุณากรอกข้อมูลให้ครบ', 'ตกลง');
        }
      }
    }catch(e){
      print(e);
    }
  }
}
