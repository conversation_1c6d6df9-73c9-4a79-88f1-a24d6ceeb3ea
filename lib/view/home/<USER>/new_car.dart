import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:loading_animations/loading_animations.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/new_car_controller.dart';


class NewCarCard extends StatefulWidget {
  const NewCarCard({Key? key}) : super(key: key);

  @override
  State<NewCarCard> createState() => _NewCarCardState();
}

class _NewCarCardState extends State<NewCarCard> {
  final newCarCtl = Get.put(NewCarController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "NewCar");
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => newCarCtl.isLoading.value
        ? const SizedBox()
        : defaultContainer()
    );
  }

  Widget defaultContainer(){
    return InkWell(
      onTap: (){
        newCarCtl.isShow.value = !newCarCtl.isShow.value;
      },
      child: Container(
        width: Get.width,
        height: 48,
        alignment: Alignment.center,
        padding: const EdgeInsets.only(
          left: 10,
          right: 12
        ),
        margin: EdgeInsets.only(
            left: Get.width < 500 ? 8 : 54,
            right: Get.width < 500 ? 8 : 54,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: const LinearGradient(
            colors: [
              Color(0xFFFFFFFF),
              Color(0xFFF8F8F8),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: PageView.builder(
          controller: newCarCtl.pageController,
          itemCount: newCarCtl.newCar.newCarList!.length,
          clipBehavior: Clip.none,
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          itemBuilder: (BuildContext context, int index) => Row(
            children: [
              Image.asset(
                'assets/image/service/menu_sale.png',
                width: 28,
              ),
              AppWidget.boldTextS(context, "  สถานะ", 14,
                  const Color(0xFF282828), FontWeight.w600),
              AppWidget.normalTextS(context, " : ", 14,
                  const Color(0xFF282828), FontWeight.w600),
              AppWidget.boldTextS(
                  context,
                  getStatusMessage(newCarCtl.newCar.newCarList![index].statusApp.toString()),
                  14,
                  const Color(0xFF895F00),
                  FontWeight.w600),
              const Spacer(),
              SvgPicture.string(
                  '<svg width="18" height="4" viewBox="0 0 18 4" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M18 2C18 1.17157 17.3284 0.5 16.5 0.5C15.6716 0.5 15 1.17157 15 2C15 2.82843 15.6716 3.5 16.5 3.5C17.3284 3.5 18 2.82843 18 2Z" fill="#FFB100"/> <path d="M10.5 2C10.5 1.17157 9.82843 0.5 9 0.5C8.17157 0.5 7.5 1.17157 7.5 2C7.5 2.82843 8.17157 3.5 9 3.5C9.82843 3.5 10.5 2.82843 10.5 2Z" fill="#FFB100"/> <path d="M3 2C3 1.17157 2.32843 0.5 1.5 0.5C0.671573 0.5 0 1.17157 0 2C0 2.82843 0.671573 3.5 1.5 3.5C2.32843 3.5 3 2.82843 3 2Z" fill="#FFB100"/></svg>')
            ],
          ),
        ),
      ),
    );
  }

  String getStatusMessage(String statusApp) {
    if (statusApp == "book an appointment") {
      return "จองรถเรียบร้อยแล้ว";
    } else if (statusApp == "waiting finance") {
      return "รอเซ็นสัญญา";
    } else if (statusApp == "finance approve") {
      return "ไฟแนนซ์อนุมัติ";
    } else if (statusApp == "finance not approve") {
      return "ไฟแนนซ์ไม่อนุมัติ";
    } else if (statusApp == "have car in stock") {
      return "รอรถมาส่ง 1-3 วัน";
    } else if (statusApp == "car to headoffice") {
      return "รถมาส่งถึงสำนักงานใหญ่แล้ว";
    } else if (statusApp == "custom car") {
      return "อยู่ระหว่างขั้นตอนการสั่งแต่ง";
    } else if (statusApp == "delivery car") {
      return "รถพร้อมส่งมอบ";
    } else {
      return "ส่งมอบรถแล้ว";
    }
  }
}
