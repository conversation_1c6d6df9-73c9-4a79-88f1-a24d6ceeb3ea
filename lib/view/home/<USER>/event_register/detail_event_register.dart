import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';

import '../../../../component/alert.dart';
import '../../../../component/api.dart';
import '../../../../component/loader.dart';
import '../../../../component/url.dart';
import '../../../../controller/service/service.dart';
import '../../../../controller/setting_controller/event_register_controller.dart';
import '../../../../controller/setting_controller/profile_controller.dart';
import '../../../../controller/setting_controller/promotion_controller.dart';
import '../../../profile/profile_edit.dart';
import '../../home_navigator.dart';

class DetailEventRegisterPage extends StatefulWidget {
  Map listEvent;
  DetailEventRegisterPage(this.listEvent);

  @override
  State<DetailEventRegisterPage> createState() => _DetailEventRegisterPageState(this.listEvent);
}

class _DetailEventRegisterPageState extends State<DetailEventRegisterPage> {
  final TextEditingController _fullName = TextEditingController();
  final TextEditingController _address = TextEditingController();


  Map listEvent;
  _DetailEventRegisterPageState(this.listEvent);
  // final SecureStorage secureStorage = SecureStorage();

  DateTime now = DateTime.now();
  var fDate;
  var eDate;
  var fPreDate;
  var ePreDate;

  var fullNameArr;

  bool status = false;

  final profileCtl = Get.put(ProfileController());
  final promotionCtl = Get.find<PromotionController>();
  final eventRegisterCtl = Get.put(EventRegisterController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Event");
    getData();
  }

  getData() async {
    convert();
    await eventRegisterCtl.checkEventRegister(widget.listEvent["id_activity"], profileCtl.profile.value.id);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFEDEDED),
                    Color(0xFFF2F2F2),
                  ],
                  stops: [0.0, 1.0],
                ),
              ),
            ),
            Column(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  width: Get.width,
                  height: 100,
                  padding: const EdgeInsets.only(
                      left: 18,
                      right: 18,
                      bottom: 10
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(15),
                                topLeft: Radius.circular(15),
                                bottomRight: Radius.circular(15),
                                bottomLeft: Radius.circular(15),
                              ),
                              border: Border.all(width: 1, color: const Color(0xFFD9D8D5))),
                          child: const Icon(
                            Icons.arrow_back_ios_new,
                            size: 18,
                            color: Color(0xFFFFB100),
                          ),
                        ),
                      ),
                      AppWidget.boldTextS(
                          context,
                          "รายละเอียด",
                          16,
                          const Color(0xFF2B1710),
                          FontWeight.w500),
                      const SizedBox(width: 36,),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        eventRegisterCtl.statusRegis.isTrue
                            ? Container(
                          width: Get.width,
                          height: 40,
                          color: const Color(0xFFFFC700),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/image/car_detail/alert-circle.png', width: 18, height: 18,),
                              const SizedBox(
                                width: 5,
                              ),
                              AppWidget.normalText(
                                  context,
                                  "คุณได้ทำการลงทะเบียนแล้ว",
                                  14,
                                  const Color(0xFF2B1710),
                                  FontWeight.w400),
                            ],
                          ),
                        )
                            : const SizedBox(),
                        CachedNetworkImage(
                          imageUrl: "${promotionCtl.urlEvent.value}${widget.listEvent['information_pic']}",
                          width: Get.width,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const SizedBox(
                            width: 50,
                            height: 50,
                            child: Center(
                              child: CircularProgressIndicator(
                                color: Colors.orange,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.error,
                            color: Color(0xFFFFB100),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(
                            top: 10,
                            left: 18,
                            right: 18,
                          ),
                          child: Column(
                            children: [
                              AppWidget.boldTextS(
                                  context,
                                  widget.listEvent['information_name'],
                                  14,
                                  const Color(0xFF895F00),
                                  FontWeight.w500),
                              const SizedBox(
                                height: 10,
                              ),
                              AppWidget.normalText(
                                  context,
                                  AppService.parseHtmlString(widget.listEvent['information_body'],),
                                  14,
                                  const Color(0xFF2B1710),
                                  FontWeight.w400),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 80,
                        ),
                        buildButton(),
                        const Padding(padding: EdgeInsets.only(bottom: 40)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        )
      ///
    ));
  }

  void convert() {
    String startPreDateString = widget.listEvent["fdate_regis_activity"];
    String endPreDateString = widget.listEvent["edate_regis_activity"];
    String startDateString = widget.listEvent["fdate_activity"];
    String endDateString = widget.listEvent["edate_activity"];
    DateTime startPreDate = DateTime.parse(startPreDateString.replaceAll("Z", ""));
    DateTime endPreDate = DateTime.parse(endPreDateString.replaceAll("Z", ""));
    DateTime startDate = DateTime.parse(startDateString.replaceAll("Z", ""));
    DateTime endDate = DateTime.parse(endDateString.replaceAll("Z", ""));
    fPreDate = startPreDate;
    ePreDate = endPreDate;
    fDate = startDate;
    eDate = endDate;
  }

  checkProfile() async {
    if(profileCtl.profile.value.fullname == "" || profileCtl.profile.value.fullname == null){
      var res = await AppAlert.showNewConfirm(context, "กรุณาตรวจสอบ", "กรุณาอัพเดทข้อมูลส่วนตัว\nแล้วกดลงทะเบียนอีกครั้ง", "ตกลง", "ยกเลิก");
      if(res){
        Get.to(() => const ProfileEditPage());
      }
    } else {
      await saveData();
    }
  }

  saveData() async {
    try{
      var userId = profileCtl.profile.value.id;
      if(now.compareTo(fPreDate) >= 0 && now.compareTo(ePreDate) <= 0){
        var result = await AppAlert.showConfirm(context, "ยืนยันการลงทะเบียน", "คุณต้องการลงทะเบียนล่วงหน้า\nใช่หรือไม่", "ตกลง");
        if(result == true){
          AppLoader.loader(context);
          Map data = {
            "id_activity" : widget.listEvent["id_activity"],
            "member" : userId.toString(),
            "assessment" : "N",
            "reward" : widget.listEvent["LP_activity"],
          };
          print(data);
          final response = await AppApi.callAPIjwt("POST", AppUrl.registerEvent, data);
          print(response);
          if (response['status'] == 200) {
            status = response['result']['regisActivty'];
            eventRegisterCtl.statusRegis.value = true;
            AppLoader.dismiss(context);
            await AppAlert.showNewAccept(context, 'ลงทะเบียนสำเร็จ', 'คุณได้ทำการลงทะเบียน\nเข้าร่วมกิจกรรมกับประชากิจฯ แล้วเรียบร้อย\nกรุณาแจ้งทีมงาน เพื่อรับของที่ระลึก\nขอบคุณมากค่ะ', 'ตกลง');
            Get.to(() => const HomeNavigator());
          } else if(response['status'] == 513){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'คุณได้ทำการลงทะเบียน\nกิจกรรมนี้แล้ว', '', 'ตกลง');
          } else if(response['status'] == 511){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'ยังไม่ถึงเวลาที่กำหนด', '', 'ตกลง');
          }  else if(response['status'] == 512){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'เลยเวลาการลงทะเบียนกิจกรรมนี้แล้ว', '', 'ตกลง');
          } else {
            AppLoader.dismiss(context);
            AppAlert.showError(context, 'ลงทะเบียนกิจกรรมไม่สำเร็จ \nกรุณาลองใหม่อีกครั้ง', 'ตกลง');
          }
        }
      } else if(now.compareTo(fDate) >= 0 && now.compareTo(eDate) <= 0){
        var result = await AppAlert.showConfirm(context, "ยืนยันการลงทะเบียน", "คุณต้องการลงทะเบียน ใช่หรือไม่", "ตกลง");
        if(result == true){
          AppLoader.loader(context);
          Map data = {
            "id_activity" : widget.listEvent["id_activity"],
            "member" : userId.toString(),
            "assessment" : "N",
            "reward" : widget.listEvent["LP_activity"],
          };
          final response = await AppApi.callAPIjwt("POST", AppUrl.registerEvent, data);
          if (response['status'] == 200) {
            status = response['result']['regisActivty'];
            eventRegisterCtl.statusRegis.value = true;
            AppLoader.dismiss(context);
            await AppAlert.showNewAccept(context, 'ลงทะเบียนสำเร็จ', 'คุณได้ทำการลงทะเบียน\nเข้าร่วมกิจกรรมกับประชากิจฯ แล้วเรียบร้อย\nกรุณาแจ้งทีมงาน เพื่อรับของที่ระลึก\nขอบคุณมากค่ะ', 'ตกลง');
            Get.to(() => const HomeNavigator());
          } else if(response['status'] == 513){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'คุณได้ทำการลงทะเบียน\nกิจกรรมนี้แล้ว', '', 'ตกลง');
          } else if(response['status'] == 511){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'ยังไม่ถึงเวลาที่กำหนด', '', 'ตกลง');
          }  else if(response['status'] == 512){
            AppLoader.dismiss(context);
            AppAlert.showNewAccept(context, 'เลยเวลาการลงทะเบียนกิจกรรมนี้แล้ว', '', 'ตกลง');
          } else {
            AppLoader.dismiss(context);
            AppAlert.showError(context, 'ลงทะเบียนกิจกรรมไม่สำเร็จ \nกรุณาลองใหม่อีกครั้ง', 'ตกลง');
          }
        }
      } else {
        AppAlert.showNewAccept(
            context,
            "หมดเขตการลงทะเบียนล่วงหน้า",
            "กรุณาลงทะเบียนในวันที่ \n${AppService.dateThaiDate(widget.listEvent["fdate_activity"])} เวลา ${AppService.dateThaiDateTime(widget.listEvent["fdate_activity"])} น.",
            "ตกลง");
      }
    }catch(e){
      print('$e ==> Save data detail event regis');
    }
  }

  saveProfile() async {
    try{
      AppLoader.loader(context);
      if(_fullName.text != "" &&
          _address.text != ""){
        fullNameArr = _fullName.text.split(" ");
        Map data = {
          "table": "customercenter",
          "firstname": fullNameArr[0],
          "lastname": fullNameArr[1],
          "address": _address.text,
          "phone" : profileCtl.profile.value.mobile,
        };
        final response = await AppApi.post(AppUrl.saveProfileNameAdd, data);
        if(response['status'] == 200){
          AppLoader.dismiss(context);
          await AppAlert.showNewAccept(context, 'บันทึกข้อมูลสำเร็จ', 'คุณได้ลงทะเบียนกิจกรรมนี้\nเรียบร้อยแล้ว', "ตกลง");
          Get.to(() => const HomeNavigator());
        } else {
          AppLoader.dismiss(context);
          AppAlert.showError(context, 'แก้ไขข้อมูลไม่สำเร็จ', 'ตกลง');
        }
      }
    }catch(e){
      AppService.sendError(e, 'ERROR : saveProfile in Profile');
    }
  }

  Widget buildButton() {
    if(profileCtl.token.value == null) {
      return const SizedBox();
    }
    if(eventRegisterCtl.statusRegis.isTrue) {
      return Container(
          margin: const EdgeInsets.only(
              left: 18,
              right: 18
          ),
          width: Get.width,
          height: 50,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: const Color(0xffD9D8D5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.boldText(
                  context,
                  "ลงทะเบียนแล้ว",
                  14,
                  const Color(0xFF111111),
                  FontWeight.w500
              ),
            ],
          )
      );
    }
    if(now.compareTo(fPreDate) >= 0 && now.compareTo(eDate) <= 0) {
      return InkWell(
        onTap: () async {
          await checkProfile();
        },
        child: Container(
            margin: const EdgeInsets.only(
                left: 18,
                right: 18
            ),
            width: Get.width,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xff000000).withOpacity(0.7),
                  const Color(0xff000000),
                ],
                stops: const [0.0, 1.0],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppWidget.normalText(
                    context,
                    "ลงทะเบียนร่วมกิจกรรม ",
                    14,
                    const Color(0xFFFFFFFF).withOpacity(0.9),
                    FontWeight.w400
                ),
                AppWidget.normalText(
                    context,
                    "กับประชากิจฯ",
                    14,
                    const Color(0xFFFFB100),
                    FontWeight.w500
                ),
              ],
            )
        ),
      );
    } else {
      return const SizedBox();
    }
  }

}
