import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mapp_prachakij_v3/model/car_service.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';

class AllCarServicePage extends StatefulWidget {
  const AllCarServicePage({Key? key}) : super(key: key);

  @override
  State<AllCarServicePage> createState() => _AllCarServicePageState();
}

class _AllCarServicePageState extends State<AllCarServicePage> {
  final SecureStorage secureStorage = SecureStorage();

  String mapsKey = "AIzaSyAmIZ39-kRoLGdQ7uqdyctMQGQgizSGyLs";
  bool isLoading = true;
  bool showInfo = false;
  LatLng? currentPosition;
  final Completer<GoogleMapController> mapController = Completer();
  Set<Marker> _markers = Set<Marker>();
  BitmapDescriptor? carIcon;
  BitmapDescriptor? serviceIcon;
  List technicalTeam = [];

  CarServiceList? resCarServiceList;
  List<dynamic> _listCar = [];

  Map<String, dynamic>? dataMap;
  Map<String, dynamic>? carInfo;

  var token;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "AllCarService");
    getData();
  }

  getData() async {
    token = await secureStorage.readSecureData("accessToken");
    setSourceAndDestinationIcons();
    await getALLCarService();
    await getCurrentLocation();
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    void onMapCreated(GoogleMapController controller) {
      this.mapController.complete(controller);
    }

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: [
                    const Color(0xFF333333).withOpacity(0.9),
                    const Color(0xFF282828),
                  ],
                  begin: const FractionalOffset(0.0, 0.0),
                  end: const FractionalOffset(1.0, 0.0),
                  stops: const [0, 1.0],
                  tileMode: TileMode.clamp),
            ),
          ),
          Container(
            width: 1.sw,
            margin: EdgeInsets.only(top: 0.05.sh),
            padding: EdgeInsets.only(
              bottom: 0.02.sh,
              top: 0.02.sh,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(
                  0.05.sw,
                ),
                topRight: Radius.circular(
                  0.05.sw,
                ),
              ),
              color: const Color(0xFFF9F9F9),
            ),
            child: Stack(
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(0.05.sw),
                    margin: EdgeInsets.only(
                      left: 0.05.sw,
                    ),
                    child: SvgPicture.string(
                      '<svg viewBox="1.1 15.7 32.2 19.6" ><path transform="matrix(0.0, -1.0, 1.0, 0.0, -36.57, 62.73)" d="M 28.01108932495117 52.52221298217773 L 42.35295867919922 38.18108367919922 C 43.04440307617188 37.48963165283203 44.16607666015625 37.48963165283203 44.85752105712891 38.18108367919922 L 46.53042602539062 39.8539924621582 C 47.22113800048828 40.54470443725586 47.22187805175781 41.66342544555664 46.53338623046875 42.35560607910156 L 35.16688919067383 53.77449417114258 L 46.53264617919922 65.19410705566406 C 47.22187805175781 65.88629913330078 47.22040557861328 67.00502014160156 46.52969360351562 67.69572448730469 L 44.85678863525391 69.36863708496094 C 44.16533660888672 70.06008148193359 43.04366302490234 70.06008148193359 42.35221862792969 69.36863708496094 L 28.01108932495117 55.02678298950195 C 27.31963729858398 54.33533096313477 27.31963729858398 53.21365737915039 28.01108932495117 52.52221298217773 Z" fill="#ffb100" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                      allowDrawingOutsideViewBox: true,
                      width: 0.05.sw,
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                    top: 0.02.sh,
                  ),
                  alignment: Alignment.topCenter,
                  child: AppWidget.boldText(
                      context,
                      "ตำแหน่งรถให้บริการ",
                      20.sp,
                      Colors.black,
                      FontWeight.w400)
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 0.3.sw),
            width: 1.sw,
            height: 1.sh,
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: currentPosition!,
                zoom: 10,
              ),
              myLocationButtonEnabled: true,
              myLocationEnabled: true,
              zoomControlsEnabled: false,
              compassEnabled: true,
              onMapCreated: onMapCreated,
              markers: _markers,
              onTap: (latLng) {
                setState(() {
                  showInfo = false;
                });
              },
            ),
          ),
          showInfo
              ? Container(
            margin: EdgeInsets.only(
              top: 0.5.sh,
              left: 0.05.sw,
              right: 0.05.sw
            ),
            width: 1.sw,
            height: 0.5.sh,
            // color: Colors.red,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    width: 1.sw,
                    padding: EdgeInsets.only(
                      top: 0.01.sh,
                      left: 0.02.sw,
                      right: 0.02.sw,
                      bottom: 0.01.sh,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 3),
                          blurRadius: 3.0,
                        ),
                      ],
                      borderRadius: BorderRadius.all(
                          Radius.circular(0.04.sw)),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                                child: carInfo!['status'] == 'repair'
                                    ? SizedBox(
                                    width: 0.01.sh,
                                    height: 0.01.sh,
                                    child: const DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: Color(0xFFFFB100),
                                        shape: BoxShape.circle,
                                      ),
                                    ))
                                    : SizedBox(
                                    width: 0.01.sh,
                                    height: 0.01.sh,
                                    child: const DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: Color(0xFF76FF7B),
                                        shape: BoxShape.circle,
                                      ),
                                    )
                                )
                            ),
                            SizedBox(
                              width: 0.02.sw,
                            ),
                            AppWidget.boldText(
                                context,
                                "ทีมงานสานสัมพันธ์ถึงบ้าน ทีม ${carInfo!['team']}",
                                16.sp,
                                Colors.black,
                                FontWeight.w400),
                          ],
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.01.sh,
                              height: 0.01.sh,
                            ),
                            SizedBox(
                              width: 0.02.sw,
                            ),
                            AppWidget.normalText(
                                context,
                                "สถานะ : ${carInfo!['status'] == 'repair' ? 'อยู่ในระหว่างการให้บริการ' : 'พร้อมให้บริการ'}",
                                16.sp,
                                Colors.black,
                                FontWeight.w400),
                          ],
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 0.01.sh,
                  ),
                  Container(
                    width: 1.sw,
                    padding: EdgeInsets.only(
                      top: 0.01.sh,
                      left: 0.02.sw,
                      right: 0.02.sw,
                      bottom: 0.01.sh,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 3),
                          blurRadius: 3.0,
                        ),
                      ],
                      borderRadius: BorderRadius.all(
                          Radius.circular(0.04.sw)),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            SizedBox(
                              width: 0.01.sh,
                              height: 0.01.sh,
                            ),
                            SizedBox(
                              width: 0.02.sw,
                            ),
                            SizedBox(
                              width: 0.02.sw,
                            ),
                            AppWidget.boldText(
                                context,
                                "ตำแหน่งพิกัดการให้บริการ",
                                16.sp,
                                Colors.black,
                                FontWeight.w400),
                          ],
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: 0.01.sh,
                              height: 0.01.sh,
                            ),
                            SizedBox(
                              width: 0.02.sw,
                            ),
                            AppWidget.normalText(
                                context,
                                ": ",
                                14.sp,
                                Colors.black,
                                FontWeight.w400),
                            AppWidget.normalText(
                                context,
                                carInfo!['service_area'],
                                14.sp,
                                const Color(0xFFFFB100),
                                FontWeight.w400),
                          ],
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 0.01.sh,
                  ),
                  Container(
                    width: 1.sw,
                    padding: EdgeInsets.only(
                      top: 0.01.sh,
                      left: 0.02.sw,
                      right: 0.02.sw,
                      bottom: 0.01.sh,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFFFFF),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 3),
                          blurRadius: 3.0,
                        ),
                      ],
                      borderRadius: BorderRadius.all(
                          Radius.circular(0.04.sw)),
                    ),
                    child: Container(
                      margin: EdgeInsets.only(left: 0.05.sw),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppWidget.boldText(
                              context,
                              "รายละเอียดข้อมูลทีมงาน",
                              16.sp,
                              Colors.black,
                              FontWeight.w400),
                          SizedBox(
                              child: buildTeam(context, technicalTeam),
                          ),
                          SizedBox(
                            height: 0.01.sh,
                          ),
                          Container(
                            child: Image.asset('assets/image/service/home_service/vaccinated.png',
                            width: 0.4.sw,),
                          ),
                          SizedBox(
                            height: 0.01.sh,
                          ),
                          AppWidget.boldText(
                              context,
                              "ทีมช่าง : ${carInfo!['team']}",
                              14.sp,
                              Colors.black,
                              FontWeight.w400),
                          AppWidget.boldText(
                              context,
                              'ทะเบียนรถ : ${carInfo!['regis']}',
                              14.sp,
                              Colors.black,
                              FontWeight.w400),
                          SizedBox(
                            height: 0.01.sh,
                          ),
                          AppWidget.boldText(
                              context,
                              'ติดต่อทีมงาน :',
                              16.sp,
                              Colors.black,
                              FontWeight.w400),
                          InkWell(
                            onTap: (){
                              if(token == null){
                                AppWidget.showDialogPageSlide(context, const LoginPage());
                              } else {
                                AppService.callPhone(
                                    carInfo!['mobile']);
                              }
                            },
                            child: Container(
                              width: 0.2.sw,
                              height: 0.04.sh,
                              // color: Colors.red,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(
                                    0.05.sw,
                                  ),
                                ),
                                boxShadow: const [
                                  BoxShadow(
                                      color: Colors.black,
                                      spreadRadius: 2),
                                ],
                                color: const Color(0xFFFFB100),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.call,
                                  size: 0.02.sh,
                                  color: Colors.white,
                                ),
                              ),
                            ),

                          )

                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
              : Container(),
        ],
      ),
    );
  }

  setMarkersService() async {
    _markers.add(
      Marker(
        infoWindow: const InfoWindow(title: "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด สำนักงานใหญ่"),
        markerId: const MarkerId('pkg'),
        position: const LatLng(12.6628558,102.0806582),
        icon: serviceIcon!,
      ),
    );

    _markers.add(
      Marker(
        infoWindow: const InfoWindow(title: "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด นายายอาม"),
        markerId: const MarkerId('pkg2'),
        position: const LatLng(12.777146476810904, 101.82898270953247),
        icon: serviceIcon!,
      ),
    );

    _markers.add(
      Marker(
        infoWindow: const InfoWindow(title: "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด สอยดาว"),
        markerId: const MarkerId('pkg3'),
        position: const LatLng(13.130783010334909, 102.2103395480061),
        icon: serviceIcon!,
      ),
    );

    _markers.add(
      Marker(
        infoWindow: const InfoWindow(title: "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด ขลุง"),
        markerId: const MarkerId('pkg4'),
        position: const LatLng(12.464012821819592, 102.2245693711473),
        icon: serviceIcon!,
      ),
    );
  }

  void setSourceAndDestinationIcons() async {
    print('setSourceAndDestinationIcons');
    if (io.Platform.isAndroid) {
      carIcon = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(devicePixelRatio: 0.5, size: Size(1, 1)),
          'assets/image/service/home_service/icon_car.png');
      serviceIcon = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(devicePixelRatio: 0.5, size: Size(2, 2)),
          'assets/image/service/home_service/service_icon.png');

    } else {
      carIcon = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(devicePixelRatio: 0.5, size: Size(1, 1)),
          'assets/image/service/home_service/icon_car_ios.png');
      serviceIcon = await BitmapDescriptor.fromAssetImage(
          const ImageConfiguration(devicePixelRatio: 0.5, size: Size(2, 2)),
          'assets/image/service/home_service/service_icon_ios.png');
    }
    setMarkersService();
    getCarService();
  }

  getCurrentLocation() async {
    try {
      print('getCurrentLocation()');
      try {
        Position position = await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high);
        setState(() {
          currentPosition = LatLng(position.latitude, position.longitude);
          isLoading = false;
        });
      } on PlatformException catch (e) {
        if (e.code == 'PERMISSION_DENIED') {
          AppService.sendError('Permission denied to getCurrentLocation',
              'Pick Location In Home Service');
          print('Permission denied');

          Navigator.pop(context);
        } else {
          AppService.sendError('PlatformException to getCurrentLocation',
              'Pick Location In Home Service');

          Navigator.pop(context);
        }
      }
    } catch (e) {
      AppService.sendError('PlatformException to getCurrentLocation',
          'Pick Location In Home Service');
      Navigator.pop(context);
    }
  }

  getCarService() async {
    try {
      print('getCarService');
      FirebaseFirestore.instance
          .collection('tracking')
          .snapshots()
          .listen((querySnapshot) {
        for (var change in querySnapshot.docs) {
          var dataMap = change.data();
          setState(() {
            if (dataMap['lat'] != null) {
              setMarkers(
                  LatLng(dataMap['lat'], dataMap['lng']),
                  dataMap['team'].toString(),
                  dataMap['running'].toString(),
                  dataMap['rotation'],
                  dataMap['status']);
            }
          });
        }
      });
    } catch (e) {
      AppService.sendError(e, 'getDataMap in Tracking');
    }
  }

  setMarkers(LatLng pinPosition, team, running, rotation, status) {
    try {
      print('setMarker');
      _markers.removeWhere((m) => m.markerId.value == running);
      _markers.add(
        Marker(
            infoWindow: InfoWindow(title: "ทีม $team"),
            rotation: rotation == null ? 90 : rotation,
            markerId: MarkerId(running),
            position: pinPosition,
            // updated position
            icon: carIcon!,
            onTap: () {
              getInfo(team, status);
            }),
      );
    } catch (e) {
      AppService.sendError(e, 'setMarkers in All Car Tracking');
    }
  }

  getInfo(team, status) async {
    try {
      AppLoader.loader(context);
      technicalTeam = [];
      await FirebaseFirestore.instance
          .collection("setting")
          .doc("team")
          .collection(team)
          .get()
          .then((value) {
        for (var value in value.docs) {
          setState(() {
            Map<String, dynamic> res = value.data();
            technicalTeam.add(res);
          });
        }
      });
      setState(() {
        carInfo = _listCar.singleWhere((item) => item['team'] == team);
        carInfo!['status'] = status;
        showInfo = true;
      });
      AppLoader.dismiss(context);
    } catch (e) {
      AppService.sendError(e, 'getInfo in All Car Tracking');
    }
  }

  getALLCarService() async {
    try {
      final response = await AppApi.get(AppUrl.getCarServiceForAppPMS);
      int status = response['status'];
      if (status == 200) {
        setState(() {
          _listCar = response['result'];
        });
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getALLCarService in AllCarServicePage');
    }
  }

  buildTeam(context, List<dynamic> team){
    try {
      List<Widget> list = [];
      for (int i = 0; i < team.length; i++) {
        list.add(Row(
          children: [
            Container(
              width: 0.05.sh,
              height: 0.05.sh,
          margin: EdgeInsets.only(
            top: 0.01.sh
          ),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: const Color(0xFF282828),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.1),
                offset: const Offset(0, 3),
                blurRadius: 3.0,
              ),
            ],
            image: DecorationImage(
              fit: BoxFit.fill,
              image:
              team[i]['T_picture'] == null || team[i]['T_picture'] == ""
                  ? const AssetImage(
                'assets/image/service/home_service/fix_logo_half.png',
              ) as ImageProvider
                  : NetworkImage(team[i]['T_picture']),
            ),
            ),
            ),
            SizedBox(
              width: 0.03.sw,
            ),
            Container(
              child: AppWidget.boldText(
                  context,
                  team[i]['T_name'],
                  16.sp,
                  Colors.black,
                  FontWeight.w400),
            )
          ],
        ),
        );
      }
      return Column(
        children: list,
      );
    }catch (e){
      print('$e ==> error buildTeam');
    }
  }



}
