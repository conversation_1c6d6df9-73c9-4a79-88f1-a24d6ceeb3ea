import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/home_service_controller.dart';
import 'package:mapp_prachakij_v3/model/service_model/appointment.dart';
import 'package:mapp_prachakij_v3/model/license.dart';
import 'package:mapp_prachakij_v3/model/member.dart';
import 'package:mapp_prachakij_v3/model/province.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pick_location.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/appointment/appointment.dart';
import 'package:permission_handler/permission_handler.dart';

class BookingFixHomeServicePage extends StatefulWidget {
  const BookingFixHomeServicePage({Key? key}) : super(key: key);

  @override
  State<BookingFixHomeServicePage> createState() => _BookingFixHomeServicePageState();
}

class _BookingFixHomeServicePageState extends State<BookingFixHomeServicePage> {
  final SecureStorage secureStorage = SecureStorage();

  final TextEditingController _characterController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();
  final TextEditingController _detailController = TextEditingController();

  ResponseMember? resProfile;

  String? carSelect;
  carLicenseList? resCarLicense;
  bool addLicenseStatus = false;

  String? provinceSelect;
  ProvinceList? resProvince;
  List<String> provinceItem = [];
  ResponseAddLicense? resAddLicense;

  RxBool appointFormStatus = false.obs;
  String? locationName;
  String? locationLatLng;
  bool locationStatus = false;

  ResponseAppointment? resAppointment;

  bool isLoading = true;

  final profileCtl = Get.put(ProfileController());
  final homeServiceCtl = Get.put(HomeServiceController());
  final appointmentCtl = Get.put(AppointmentController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "BookingFixHomeService");
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Obx(() {
        if(homeServiceCtl.isLoading.value == true){
          return AppLoader.loaderWaitPage(context);
        } else {
          return InkWell(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: SingleChildScrollView(
              child: Stack(
                children: [
                  Container(
                    width: Get.width,
                    height: Get.height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFEDEDED),
                          Color(0xFFF2F2F2),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                      top: Get.width < 500 ? 60 : 80,
                      right: Get.width < 500 ? 18 : 54,
                      child: Image.asset("assets/image/service/menu_home_repair.png",width: 72,height: 44,)),

                  Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          top: Get.width < 500 ? 50 : 80,
                          left: Get.width < 500 ? 18 : 54,
                          right: Get.width < 500 ? 18 : 54,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: () {
                                Get.back();
                              },
                              child: Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.only(
                                      topRight: Radius.circular(15),
                                      topLeft: Radius.circular(15),
                                      bottomRight: Radius.circular(15),
                                      bottomLeft: Radius.circular(15),
                                    ),
                                    border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                                child: const Icon(
                                  Icons.arrow_back_ios_new,
                                  size: 18,
                                  color: Color(0xFFFFB100),
                                ),
                              ),
                            ),
                            AppWidget.boldTextS(context, "บริการซ่อมถึงบ้าน", Get.width < 500 ? 16 : 18, const Color(0xFF2B1710),
                                FontWeight.w500),
                            const SizedBox(
                              width: 36,
                              height: 36,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10,),
                      Container(
                        margin: EdgeInsets.only(
                          top: 10,
                          left: Get.width < 500 ? 18 : 54,
                          right: Get.width < 500 ? 18 : 54,
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 3,
                              height: 3,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Color(0xFF664701),
                              ),
                            ),
                            AppWidget.boldTextS(context, " กรอกข้อมูลให้ครบถ้วน ", Get.width < 500 ? 12 : 14, const Color(0xFF2B1710),
                                FontWeight.w500),
                          ],
                        ),
                      ),
                      buildContent(),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom))
                ],
              ),
            ),
          );
        }
      }),
    );
  }

  buildContent(){
    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.only(
          left: 18,
          right: 18,
        ),
        width: Get.width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 10,
            ),
            AppWidget.boldText(context, "ข้อมูลทะเบียนรถ",
                Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500),
            const SizedBox(
              height: 10,
            ),
            InkWell(
              onTap: () async {
                await buildCarRegDialog();
              },
              child: Container(
                width: Get.width,
                height: Get.width < 500 ? 50 : 60,
                padding: const EdgeInsets.only(
                    left: 15,
                    right: 15
                ),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: const Color(0xFFFFFFFF),
                    border: appointmentCtl.carRegSelect.value != ""
                        ? Border.all(
                        width: 1,
                        color: const Color(0xFFFFB100)
                    ) : null
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      width: 25,
                    ),
                    Obx(() => appointmentCtl.carRegSelect.value == ""
                        ? AppWidget.normalText(context, "เลือกหมายเลขทะเบียนรถ",
                        Get.width < 500 ? 14 : 16, const Color(0xFFBCBCBC), FontWeight.w500)
                        : AppWidget.boldText(context, appointmentCtl.carRegSelect.value,
                        Get.width < 500 ? 14 : 16, const Color(0xFF282828), FontWeight.w500)),
                    const Icon(Icons.arrow_drop_down_rounded, color: Color(0xFFFFC700), size: 25,),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            AppWidget.boldTextS(
                context,
                "ตำแหน่งที่ตั้งการให้บริการ",
                Get.width < 500 ? 14 : 16,
                Colors.black,
                FontWeight.w600),
            const SizedBox(
              height: 10,
            ),
            InkWell(
              onTap: ()async{
                await Permission.locationWhenInUse.request();
                List<String> result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const PickLocationPage()),
                );
                if (result != null){
                  setState(() {
                    locationName = result[0];
                    locationLatLng = result[1];
                    locationStatus = true;
                  });
                } else {
                  //ไม่ได้เลือกตำแหน่ง
                  setState(() {
                    locationName = null;
                    locationLatLng = null;
                    locationStatus = false;
                  });
                }
              },
              child: locationStatus == true
                ? Container(
                width: Get.width,
                height: Get.width < 500 ? 50 : 60,
                // color: Colors.red,
                decoration: BoxDecoration(
                  color: const Color(0xFF282828),
                  borderRadius: const BorderRadius.all(
                      Radius.circular(
                          15
                      )
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withOpacity(0.05),
                      blurRadius: 3,
                      offset: const Offset(0, 2),
                    )
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.my_location, color: Color(0xFFFFB100), size: 20,),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.normalTextS(
                        context,
                        locationName,
                        Get.width < 500 ? 14 : 16,
                        const Color(0xFFFFB100),
                        FontWeight.w400),
                  ],
                ),
              )
                  : Container(
                width: Get.width,
                height: Get.width < 500 ? 50 : 60,
                // color: Colors.red,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.all(
                      Radius.circular(
                          15
                      )
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withOpacity(0.05),
                      blurRadius: 3,
                      offset: const Offset(0, 2),
                    )
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.my_location, color: Color(0xFFBCBCBC), size: 20,),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.normalTextS(
                        context,
                        "ปักหมุดตำแหน่งที่ตั้งของคุณ",
                        Get.width < 500 ? 14 : 16,
                        const Color(0xFFBCBCBC),
                        FontWeight.w400),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(alignment: Alignment.center,
            child: AppWidget.normalTextS(
                context,
                "กรณีปักหมุดไม่ได้กรุณาพิมพ์รายละเอียดด้านล่าง",
                12,
                const Color(0xFFFF3B30),
                FontWeight.w400),
            ),
            const SizedBox(
              height: 10
            ),
            AppWidget.boldTextS(
                context,
                "รายละเอียดเพิ่มเติม",
                Get.width < 500 ? 14 : 16,
                Colors.black,
                FontWeight.w600),
            const SizedBox(
              height: 10,
            ),
            Container(
              padding: const EdgeInsets.only(
                top: 10,
                bottom: 10,
                left: 10,
                right: 10,
              ),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(
                  Radius.circular(
                    15,
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  )
                ],
                color: Colors.white,

              ),
              child: TextField(
                controller: _detailController,
                maxLines: 7,
                keyboardType: TextInputType.multiline,
                style: TextStyle(
                  fontFamily: 'Prompt',
                  fontSize: Get.width < 500 ? 14 : 16,
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.2,
                ),
                decoration: InputDecoration.collapsed(
                    hintText: "พิมพ์รายละเอียดเพิ่มเติม...",
                  hintStyle: TextStyle(
                    fontFamily: 'Prompt',
                    fontSize: Get.width < 500 ? 14 : 16,
                    color: const Color(0xFFBCBCBC),
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 10
            ),
            AppWidget.boldTextS(
                context,
                "เบอร์โทรติดต่อ",
                Get.width < 500 ? 14 : 16,
                Colors.black,
                FontWeight.w600),
            const SizedBox(
              height: 10,
            ),
            Container(
              height: Get.width < 500 ? 50 : 60,
              padding: const EdgeInsets.only(
                left: 10,
                right: 10,
              ),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(
                  Radius.circular(
                      15
                  ),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  )
                ],
                color: Colors.white,
              ),
              child: TextField(
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    RegExp(r'^[0-9]{0,10}$'),
                  ),
                ],
                controller: homeServiceCtl.phoneController,
                onChanged: checkForm(),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  letterSpacing: 0.2,
                ),
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: 'กรุณาใส่เบอร์โทรศัพท์',
                  hintStyle: TextStyle(
                    fontSize: Get.width < 500 ? 14 : 16,
                    color: const Color(0xFFBCBCBC),
                    fontFamily: 'Prompt',
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Align(
              alignment: Alignment.center,
              child: _buildSaveBtn(),
            )
          ],
        ),
      ),
    );
  }

  selectCar(context, items, title) {
    return InkWell(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (context){
            int selectTempIndex;
            if (carSelect != null) {
              selectTempIndex = items[0]["car_license"].indexOf(carSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: (){
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: AppWidget.boldText(
                            context,
                            "ยกเลิก",
                            16,
                            Colors.black,
                            FontWeight.w600),
                      ),
                      CupertinoButton(
                        onPressed: (){
                          setState(() {
                            carSelect = items[selectTempIndex]["car_license"];
                          });

                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: AppWidget.boldText(
                            context,
                            "ตกลง",
                            16,
                            Colors.black,
                            FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: Get.width,
                  height: 180,
                  color: Colors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: Colors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: AppWidget.boldText(
                            context,
                            items[index]["car_license"],
                            16,
                            Colors.black,
                            FontWeight.w600),
                      );
                    }),
                  ),
                ),
              ],
            );
          },
        );
      },
      child: Container(
        width: Get.width,
        height: 40,
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
              10
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.05),
              blurRadius: 3,
              offset: const Offset(0, 2),
            )
          ],
          color: Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            carSelect == null
                ? AppWidget.boldText(
                context,
                "เลือกหมายเลขทะเบียน",
                14,
                const Color(0xFF707070).withOpacity(0.5),
                FontWeight.w600)
                : AppWidget.boldText(
                context,
                carSelect,
                14,
                Colors.black,
                FontWeight.w600),
            const Icon(Icons.arrow_drop_down_rounded,
                color: Color(0xFFFFB100)),
          ],
        ),
      ),
    );
  }

  _inputAddLicense(hint, type, _controller){
    return Container(
      width: 70,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          width: 1,
          color: const Color(0xFFFFB100),
        ),
        borderRadius: const BorderRadius.all(
            Radius.circular(
                10
            )
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 2),
          )
        ],
      ),
      child: TextField(
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            AppService.numberAZThai4RegExp(),
          ),
        ],
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        controller: _controller,
        style: const TextStyle(
          fontFamily: 'Prompt',
          fontSize: 14,
          color: Color(0xFF707070),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.2,
        ),
        keyboardType: type,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(
            fontFamily: 'Prompt',
            fontSize: 14,
            color: const Color(0xFF707070).withOpacity(0.5),
            fontWeight: FontWeight.w400,
            letterSpacing: 0.2,
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }

  _inputNumberAddLicense(hint, type, _controller){
    return Container(
      width: 70,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          width: 1,
          color: const Color(0xFFFFB100),
        ),
        borderRadius: const BorderRadius.all(
            Radius.circular(
                10
            )
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 2),
          )
        ],
      ),
      child: TextField(
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            AppService.numberAZThai4RegExp(),
          ),
        ],
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,

        controller: _controller,
        style: const TextStyle(
          fontFamily: 'Prompt',
          fontSize: 14,
          color: Color(0xFF707070),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.2,
        ),
        keyboardType: type,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(
            fontFamily: 'Prompt',
            fontSize: 14,
            color: const Color(0xFF707070).withOpacity(0.5),
            fontWeight: FontWeight.w400,
            letterSpacing: 0.2,
          ),
          border: InputBorder.none,
        ),
      ),
    );
  }

  selectProvince(context, items) {
    return InkWell(
      onTap: () {
        showCupertinoModalPopup(
          context: context,
          builder: (context){
            int selectTempIndex;
            if (provinceSelect != null) {
              selectTempIndex = items.indexOf(provinceSelect);
            } else {
              selectTempIndex = 0;
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xffffffff),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(0xff999999),
                        width: 0.0,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: (){
                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: AppWidget.boldText(
                            context,
                            "ยกเลิก",
                            16,
                            Colors.black,
                            FontWeight.w600),
                      ),
                      CupertinoButton(
                        onPressed: (){
                          setState(() {
                            provinceSelect = items[selectTempIndex];
                          });

                          Navigator.pop(context);
                        },
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 10,
                        ),
                        child: AppWidget.boldText(
                            context,
                            "ตกลง",
                            16,
                            Colors.black,
                            FontWeight.w600),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 200,
                  color: Colors.white,
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectTempIndex,
                    ),
                    itemExtent: 50,
                    useMagnifier: true,
                    backgroundColor: Colors.white,
                    onSelectedItemChanged: (index) {
                      setState(() {
                        selectTempIndex = index;
                      });
                    },
                    children: List.generate(items.length, (index) {
                      return Center(
                        child: AppWidget.boldText(
                            context,
                            items[index],
                            16,
                            Colors.black,
                            FontWeight.w600),
                      );
                    }),
                  ),
                ),
              ],
            );
          },
        );
      },
      child: Container(
        width: 70,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            width: 1,
            color: const Color(0xFFFFB100),
          ),
          borderRadius: const BorderRadius.all(
              Radius.circular(
                  10
              )
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.05),
              blurRadius: 3,
              offset: const Offset(0, 2),
            )
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            provinceSelect == null
                ? AppWidget.normalText(
                context,
                "จังหวัด",
                14,
                const Color(0xFF707070).withOpacity(0.5),
                FontWeight.w400)
                : SizedBox(
              width: 70,
              // height: 0.045.sh,
              child: Text(provinceSelect!,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontFamily: 'Prompt',
                  fontSize: 14,
                  color: Color(0xFF707070),
                  fontWeight: FontWeight.w400,
                  letterSpacing: 0.2,
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }

  checkForm() {
    try {
      if (appointmentCtl.carRegSelect.value != "" &&
          (locationLatLng != null || _detailController.text != '') &&
          homeServiceCtl.phoneController.text != '' &&
          homeServiceCtl.phoneController.text.length == 10) {
          appointFormStatus.value = true;
      } else {
          appointFormStatus.value = false;
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : checkForm in HomeService');
    }
  }

  _buildSaveBtn() {
    return Obx(() => appointFormStatus.isTrue
        ? SizedBox(
      width: 120,
      height: Get.width < 500 ? 40 : 50,
      child: InkWell(
        onTap: () {
          newSaveAppointment();
        },
        child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(
                Radius.circular(
                    15
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                )
              ],
              color: const Color(0xFFFFB100),
            ),
            child: AppWidget.boldTextS(
                context,
                "ยืนยันข้อมูล",
                Get.width < 500 ? 14 : 16,
                const Color(0xFF282828),
                FontWeight.normal)
        ),
      ),
    )
        : Container(
      width: 120,
      height: Get.width < 500 ? 40 : 50,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(
          Radius.circular(
            15,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          )
        ],
        color: const Color(0xFFF0F0F0),
      ),
      child: AppWidget.boldText(
          context,
          "ยืนยันข้อมูล",
          Get.width < 500 ? 14 : 16,
          const Color(0xFF000000).withOpacity(0.10),
          FontWeight.normal),
    ));
  }

  newSaveAppointment() async {
    AppLoader.loader(context);
    var status = await homeServiceCtl.createHomeService(appointmentCtl.carRegSelect.value, homeServiceCtl.phoneController.text, locationLatLng);
    if(status == 200){
      setState(() {
        addLicenseStatus = false;
        locationStatus = false;
        locationName = null;
        locationLatLng = null;
        _detailController.clear();
        homeServiceCtl.phoneController.clear();
      });
      AppLoader.dismiss(context);
      AppAlert.showNewAccept(
          context,
          'ขอบคุณมากค่ะ',
          'ได้รับข้อมูลการจองคิวของคุณ\nเป็นที่เรียบร้อย กรุณารอการติดต่อกลับ\nเพื่อยืนยันอีกครั้งค่ะ',
          'ตกลง');
    } else {
      AppLoader.dismiss(context);
      AppAlert.showNewAccept(
          context,
          'เกิดข้อผิดพลาด',
          'ไม่สามารถจองคิวได้\nกรุณาลองอีกครั้ง',
          'ตกลง');
    }
  }

  requestLocationPermission() async {
    // ตรวจสอบสถานะสิทธิ์
    PermissionStatus status = await Permission.locationWhenInUse.status;

    if (status.isDenied) {
      // ขอสิทธิ์ถ้ายังไม่ได้รับอนุญาต
      status = await Permission.locationWhenInUse.request();
    }

    if (status.isGranted) {
      print('เปิดแล้ว');
      // ได้รับอนุญาตให้ใช้งาน
      // ทำอะไรก็ตามที่คุณต้องการทำ
    } else {
      // ไม่ได้รับอนุญาตให้ใช้งาน
      print('ไม่เปิดเปิดแล้ว');
      // จัดการตามที่คุณต้องการจัดการ
    }
  }

  buildCarRegDialog(){
    return showModalBottomSheet(
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        isScrollControlled: true,
        context: context,
        builder: (_) => const DialogCarReg()).then((value) {
      appointmentCtl.addCar.value = false;
    });
  }

}
