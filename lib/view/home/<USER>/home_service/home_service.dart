import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/broken_car/broken_car.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/all_car_service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/booking_fix_home_sevice.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_service/car_service_tracking.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class HomeServicePage extends StatefulWidget {
  const HomeServicePage({Key? key}) : super(key: key);

  @override
  State<HomeServicePage> createState() => _HomeServicePageState();
}

class _HomeServicePageState extends State<HomeServicePage> {
  final SecureStorage secureStorage = SecureStorage();
  List<dynamic> _listCar = [];

  var token;

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "HomeService");
    getData();
  }

  getData() async {
    await getCarService();
    token = await secureStorage.readSecureData("accessToken");
    // Permission.locationWhenInUse.request();

    setState(() {
      isLoading = false;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: SingleChildScrollView(
          child: Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    stops: const [0.2, 0.8],
                    colors: [
                      const Color(0xFFF4F4F4).withOpacity(0.85),
                      const Color(0xFFFFFFFF).withOpacity(0.85),
                    ],
                  ),
                ),
              ),
              Positioned(
                  top: Get.width < 500 ? 60 : 80,
                  right: Get.width < 500 ? 18 : 54,
                  child: Image.asset("assets/image/service/menu_home_repair.png",width: 72,height: 44,)),
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      top: Get.width < 500 ? 50 : 80,
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(15),
                                  topLeft: Radius.circular(15),
                                  bottomRight: Radius.circular(15),
                                  bottomLeft: Radius.circular(15),
                                ),
                                border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                            child: const Icon(
                              Icons.arrow_back_ios_new,
                              size: 18,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ),
                        AppWidget.boldTextS(context, "บริการซ่อมถึงบ้าน", Get.width < 500 ? 16 : 18, const Color(0xFF2B1710),
                            FontWeight.w500),
                        const SizedBox(
                          width: 36,
                          height: 36,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10,),
                  Container(
                    margin: EdgeInsets.only(
                      top: 10,
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 3,
                          height: 3,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color(0xFF664701),
                          ),
                        ),
                        AppWidget.boldTextS(context, " เลือกทีมงานที่ปรึกษา ", Get.width < 500 ? 12 : 14, const Color(0xFF2B1710),
                            FontWeight.w500),
                        AppWidget.normalTextS(context, "เพื่อสอบถามงานบริการ", Get.width < 500 ? 12 : 14, const Color(0xFF664701),
                            FontWeight.w400),
                      ],
                    ),
                  ),
                  buildContent(),
                ],
              ),
              Padding(padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom))
            ],
          ),
        ),
      ),
    );
  }

  buildContent(){
    return Container(
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.boldTextS(
                  context,
                  "กรุณาเลือกทีมที่ต้องการ",
                  16,
                  Colors.black,
                  FontWeight.w600),
              InkWell(
                onTap: () async {
                  await requestLocationPermission();
                  Navigator.push(
                    context, MaterialPageRoute(
                      builder: (context) => const AllCarServicePage(),
                    ),
                  );
                },
                child: Container(
                  width: Get.width < 500 ? 110 : 180,
                  height: Get.width < 500 ? 40 : 60,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(
                      Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF000000).withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      )
                    ],
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFF333333).withOpacity(0.8),
                        const Color(0xFF282828)
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.boldTextS(
                          context,
                          "ดูตำแหน่ง\nรถให้บริการ",
                          Get.width < 500 ? 12 : 14,
                          Colors.white,
                          FontWeight.w600),
                      Image.asset(
                        'assets/image/service/home_service/marker_location.png',
                        height: 30,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          buildListview(_listCar),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppWidget.normalText(
                  context,
                  "หรือ",
                  16,
                  const Color(0xFF707070),
                  FontWeight.w400),
              AppWidget.boldText(
                  context,
                  " สามารถเลือกการนัดหมายเพิ่มเติมได้",
                  16,
                  const Color(0xFF000000),
                  FontWeight.w400),
            ],
          ),
          AppWidget.normalText(
              context,
              "กดที่ปุ่ม นัดหมาย ด้านล่างได้เลย",
              16,
              const Color(0xFF707070),
              FontWeight.w400),
          const SizedBox(
            height: 10,
          ),
          InkWell(
            onTap: (){
              if(token == null){
                AppWidget.showDialogPageSlide(context, const LoginPage());
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BookingFixHomeServicePage(),
                  ),
                );
              }
            },
            child: Container(
              alignment: Alignment.center,
              width: 300,
              height: Get.width < 500 ? 50 : 60,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 0.5],
                  colors: [
                    const Color(0xFF333333).withOpacity(0.8),
                    const Color(0xFF282828),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF000000).withOpacity(0.1),
                    offset: const Offset(0, 2),
                    blurRadius: 2.0,
                  ),
                ],
              ),
              child: AppWidget.boldText(
                  context,
                  "นัดหมาย",
                  Get.width < 500 ? 14 : 16,
                  const Color(0xFFFFB100),
                  FontWeight.w400),
            ),
          )
        ],
      ),
    );
  }

  buildListview(listCar){
    return SizedBox(
      width: Get.width,
      height: Get.height * 0.5,
      child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: listCar.length,
          itemBuilder: (BuildContext context, int index){
            return Container(width: Get.width,
            height: Get.width < 500 ? 65 : 80,
            padding: const EdgeInsets.only(
              left: 20,
              right: 20,
              top: 10,
              bottom: 10
            ),
            margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(15),
                    topRight: Radius.circular(15),
                    bottomLeft: Radius.circular(15),
                    bottomRight: Radius.circular(15),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0, 0.5],
                    colors: [
                      const Color(0xFFFFFFFF).withOpacity(0.9),
                      const Color(0xFFFFFFFF),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withOpacity(0.1),
                      offset: const Offset(0, 2),
                      blurRadius: 2.0,
                    ),
                  ],
                ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppWidget.boldTextS(
                        context,
                        "ทีม ${listCar[index]['team']}",
                        15,
                        Colors.black,
                        FontWeight.w700),
                    Row(
                      children: [
                        Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: listCar[index]['status'] == 'repair'
                                ? const Color(0xFFFFB100)
                                : const Color(0xFF76FF7B),
                          ),
                        ),
                        Container(
                          child: AppWidget.normalText(
                              context,
                              " สถานะ : ",
                              Get.width < 500 ? 12 : 15,
                              const Color(0xFF787878),
                              FontWeight.w400),
                        ),
                        Container(
                          child: AppWidget.normalText(
                              context,
                              listCar[index]['status'] == 'repair'
                                  ? 'อยู่ในระหว่างการให้บริการ'
                                  : 'พร้อมให้บริการ',
                              Get.width < 500 ? 12 : 15,
                              const Color(0xFF787878),
                              FontWeight.w400),
                        ),
                      ],
                    )
                  ],
                ),
                InkWell(
                  onTap: () async {
                    await Permission.locationWhenInUse.request();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            CarServiceTrackingPage(listCar[index]),
                      ),
                    );
                  },
                  child: Container(
                    width: Get.width < 500 ? 60 : 100,
                    height: 40,
                    // color: Colors.red,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                      border: Border.all(
                        color: const Color(0xFF000000),
                        width: 2,
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0, 0.5],
                        colors: [
                          const Color(0xFFFFB100).withOpacity(0.9),
                          const Color(0xFFFFB100),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 2),
                          blurRadius: 2.0,
                        ),
                      ],
                    ),
                    child: Icon(Icons.search_rounded, color: Colors.white, size: Get.width < 500 ? 20 : 30,),
                  ),
                )
              ],
            )
            );
          }
      ),
    );
  }

  getCarService() async {
    try {
      final response = await AppApi.get(AppUrl.getCarServiceForAppPMS);
      int status = response['status'];
      if (status == 200) {
        _listCar = response['result'];
        await _statusCar(_listCar);
      }
    } catch (e) {
      AppService.sendError(
          e, 'ERROR : getFeelWellTVType in TrackingCarListPage');
    }
  }

  _statusCar(listCar) async {
    try {
      for (int i = 0; i < listCar.length; i++) {
        await FirebaseFirestore.instance
            .collection('tracking')
            .doc(listCar[i]['team'])
            .snapshots()
            .listen((row) {
          _listCar[i]['status'] = row.data()!['status'];
        });
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : _statusCar in TrackingCarListPage');
    }
  }


}
