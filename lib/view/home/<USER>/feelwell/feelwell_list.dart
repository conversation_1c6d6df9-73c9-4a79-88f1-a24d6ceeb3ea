import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/feelwell/feelwell_tv.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/knowledge_list.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/review_list.dart';

import '../../../../component/url.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class FeelWellListPage extends StatefulWidget {
  final running;
  final typeFeelwell;

  FeelWellListPage(this.running, this.typeFeelwell);

  @override
  State<FeelWellListPage> createState() => _FeelWellListPageState(this.running, this.typeFeelwell);
}

class _FeelWellListPageState extends State<FeelWellListPage>  with SingleTickerProviderStateMixin {
  final running;
  final typeFeelwell;
  _FeelWellListPageState(this.running, this.typeFeelwell);


  int currentFeelWellIndex = 0;
  List<dynamic> _listFeelWellTVType = [];

  String runningFeelWell = "all";
  String runningReview = "all";
  String runningKnowledge = "all";

  TabController? _feelWellTabController;

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "FeelWellList");
    switch (typeFeelwell) {
      case "สาระความรู้":
        setState(() {
          runningKnowledge = running.toString();
          currentFeelWellIndex = 0;
        });
        break;
      case "รีวิวรถ":
        setState(() {
          runningReview = running.toString();
          currentFeelWellIndex = 1;
        });
        break;
      default:
    }
    getFeelWellTVType();
  }

  getFeelWellTVType() async {
    try {
      final response = await AppApi.get(AppUrl.getFeelWellTVTypeForApp);
      int status = response['status'];
      if (status == 200) {
        _feelWellTabController = TabController(vsync: this, length: 2);
        _feelWellTabController!.addListener(_handleTabSelection);
        setState(() {
          _listFeelWellTVType = response['result'];
          isLoading = false;
        });
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getFeelWellTVType in FeelWellList');
    }
  }

  void _handleTabSelection() {
    setState(() {
      currentFeelWellIndex = _feelWellTabController!.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF505050),
                  Color(0xFF282828),
                ],
              ),
            ),
          ),
          Column(
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                width: Get.width,
                height: 90,
                padding: const EdgeInsets.only(
                    left: 18,
                    right: 19
                ),
                child:  Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: (){
                        Navigator.pop(context);
                      },
                      child: Container(
                        width: 50,
                        height: 50,
                        alignment: Alignment.centerLeft,
                        child: const Icon(
                          Icons.arrow_back_ios,
                          color: Color(0xFFFFB100),
                          size: 16,
                        ),
                      ),
                    ),
                    const Text('คอนเทนต์ / สาระความรู้',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Prompt-Medium',
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(
                      width: 50,
                      height: 50,
                    )
                  ],
                ),
              ),
              const Divider(
                color: Color(0xFFFFB100),
              ),
              buildTabBar(),
              Expanded(child:
              buildTabBarView())
            ],
          )
        ],
      ),
    );
  }

  Widget buildTabBar(){
    return SizedBox(
      width: Get.width,
      height: 50,
      child: Container(
        alignment: Alignment.topCenter,
        child: TabBar(
          onTap: _onItemTapped,
          controller: _feelWellTabController,
          labelColor: const Color(0xff282828),
          unselectedLabelColor: const Color(0xff707070).withOpacity(0.5),
          isScrollable: true,
          indicator: const BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(
                20,
              ),
            ),
          ),
          indicatorWeight: 0,
          tabs: [
            _buildTab(context, _listFeelWellTVType[1]['name'], 0),
            _buildTab(context, _listFeelWellTVType[2]['name'], 1),
          ],
        ),
      ),
    );
  }

  Widget buildTabBarView(){
    return TabBarView(
      controller: _feelWellTabController,
      children: [
        KnowledgePage(runningKnowledge),
        ReviewPage(runningReview),
      ],
    );
  }

  _buildTab(context, branch, number) {
    return Tab(
      child: Text(
        branch,
        style: currentFeelWellIndex == number
            ? const TextStyle(
          fontFamily: 'Prompt',
          fontSize: 16,
          color: Color(0xFFFFB100),
        )
            : const TextStyle(
          fontFamily: 'Prompt',
          fontSize: 16,
          color: Color(0xFFFFFFFF),
        ),
      ),
    );
  }

  void _onItemTapped(int index) {
    if (kDebugMode) {
      print(index);
    }
  }

}
