import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/regis_account_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/regis_bookbank_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/regis_success_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';

class ShowDetailRegisMR extends StatefulWidget {
  const ShowDetailRegisMR({Key? key}) : super(key: key);

  @override
  State<ShowDetailRegisMR> createState() => _ShowDetailRegisMRState();
}

class _ShowDetailRegisMRState extends State<ShowDetailRegisMR> {

  String? StatusAcc;
  String? StatusBb;

  bool selectRegisBb = false;

  bool isLoading = true;

  final profileCtl = Get.put(ProfileController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  getData() async {
    await setStatusAcc();
    await setStatusBb();
    setState(() {
      isLoading = false;
    });
  }

  setStatusAcc() {
    try{
      if(
      profileCtl.profile.value.fullNameMR != "" &&
          profileCtl.profile.value.idCardMR != "" &&
          profileCtl.profile.value.phoneNumberMR != "" &&
          profileCtl.profile.value.careerMR != ""
      ) {
        StatusAcc = "success";
        print(StatusAcc);
      } else {
        StatusAcc = "unsuccess";
      }
    }catch(e){
      print(e);
    }
  }

  setStatusBb() {
    try{
      if(
      profileCtl.profile.value.bankNameMR != "" &&
          profileCtl.profile.value.bookBankNoMR != "" &&
          profileCtl.profile.value.bookBankNameMR != ""
      ){
        StatusBb = "success";
        print(StatusBb);
      } else {
        StatusBb = "unsuccess";
        print(StatusBb);

      }
    }catch(e){
      print(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Scaffold(
            body: Stack(
              children: <Widget>[
                Container(
                  width: _width,
                  height: _height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFE8E6E2),
                        Color(0xFFD9D8D5),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  height: 125.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        const Color(0xFFFFB100).withOpacity(1),
                        const Color(0xFFFFC700).withOpacity(0.7),
                        const Color(0xFFFFC700).withOpacity(0.4),
                        const Color(0xFFFFC700).withOpacity(0),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 13.h),
                      width: _width,
                      height: 150.h,
                      child: Stack(
                        children: [
                          Column(
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 13.w),
                                width: 255.w,
                                height: 80.h,
                                child: Container(
                                  margin: EdgeInsets.only(
                                      top: 20.h,
                                      left: 10.w
                                  ),
                                  width: 245.w,
                                  height: 30.h,
                                  child: Row(
                                    children: [
                                      InkWell(
                                        onTap: (){
                                          Navigator.pushReplacement(context,
                                              MaterialPageRoute(builder: (context) => const HomeNavigator()));
                                        },
                                        child: SizedBox(
                                          height: 30.h,
                                          child: Icon(Icons.arrow_back_ios_new,
                                            color: const Color(0xFF282828),
                                            size: 15.w,
                                          ),
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 15.h,
                                            left: 12.w
                                        ),
                                        width: 175.w,
                                        height: 50.h,
                                        child: Center(
                                          child: RichText(
                                            text: TextSpan(
                                                children: [
                                                  TextSpan(
                                                    text: 'สมัครผู้แนะนำลูกค้า MR',
                                                    style: TextStyle(
                                                      fontWeight: FontWeight.w500,
                                                      fontFamily: 'Prompt-Medium',
                                                      color: const Color(0xFF282828),
                                                      fontSize: 14.w,
                                                    ),
                                                  ),
                                                  TextSpan(
                                                    text: '\nกรุณาใส่รายละเอียดข้อมูลให้ครบถ้วน',
                                                    style: TextStyle(
                                                      fontWeight: FontWeight.w400,
                                                      fontFamily: 'Prompt',
                                                      color: const Color(0xFF664701),
                                                      fontSize: 11.w,
                                                    ),
                                                  ),
                                                ]
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                  top: 30.h,
                                ),
                                child: RichText(
                                  text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: 'กรุณาทำการลงทะเบียนข้อมูล 2 ส่วนด้านล่าง',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt-Medium',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),
                                        ),
                                        TextSpan(
                                          text: '\nเพื่อยืนยันข้อมูลการสมัคร',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 12.w,
                                          ),
                                        ),
                                      ]
                                  ),
                                ),
                              )
                            ],
                          ),
                          Positioned(
                            top: 30.h,
                            right: -10.w,
                            child: Image.asset('assets/image/MR/LadyWhite.png',
                              opacity: const AlwaysStoppedAnimation(.6),
                              width: 137.w,
                              height: 127.h,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          InkWell(
                            onTap: (){
                              Get.to(() => const RegisAccountMRPage());
                            },
                            child: Container(
                              margin: EdgeInsets.only(top: 20.h),
                              width: 323.w,
                              height: 90.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.h),
                                border: Border.all(
                                    width: 1,
                                    color: Colors.white
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.3),
                                    offset: const Offset(1, 1),
                                    blurRadius: 30,
                                  ),
                                ],
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    const Color(0xFFFFFFFF).withOpacity(1),
                                    const Color(0xFFF3F3F3).withOpacity(0.6),
                                  ],
                                ),
                              ),
                              child: Stack(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 10.h,
                                        left: 15.w
                                    ),
                                    child: Image.asset('assets/image/MR/File_dock_duotone.png',
                                      width: 30.h,
                                      height: 30.h,
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 10.h,
                                        left: 50.w
                                    ),
                                    width: 200.w,
                                    height: 40.h,
                                    child: RichText(text: TextSpan(
                                      children:[
                                        TextSpan(
                                          text: 'ข้อมูลสมัครตัวแทนผู้แนะนำ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt-Medium',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),

                                        ),
                                        TextSpan(
                                          text: '\nเป็นข้อมูลประกอบการสมัครเบื้องต้น',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 11.w,
                                          ),
                                        ),
                                      ] ,
                                    ),
                                    ),
                                  ),
                                  StatusAcc != "success"
                                      ? Container(
                                    margin: EdgeInsets.only(
                                        top: 50.h,
                                        left: 50.w
                                    ),
                                    width: 88.w,
                                    height: 24.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(13.h),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.3),
                                          offset: const Offset(1, 1),
                                          blurRadius: 30,
                                        ),
                                      ],
                                      color: const Color(0xFFD9D8D5),
                                    ),
                                    child: Center(
                                      child: Text('ยังไม่ลงทะเบียน',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF895F00),
                                          fontSize: 10.w,
                                        ),
                                      ),
                                    ),
                                  )
                                      : Container(
                                    margin: EdgeInsets.only(
                                        top: 50.h,
                                        left: 50.w
                                    ),
                                    width: 99.w,
                                    height: 24.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(13.h),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.3),
                                          offset: const Offset(1, 1),
                                          blurRadius: 30,
                                        ),
                                      ],
                                      color: const Color(0xFFD9D8D5),
                                    ),
                                    child: Center(
                                      child: Text('ลงทะเบียนเรียบร้อย',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF5AB13B),
                                          fontSize: 10.w,
                                        ),
                                      ),
                                    ),
                                  ),
                                  StatusAcc != "success"
                                      ? Container()
                                      : Padding(
                                    padding: EdgeInsets.only(right: 25.w),
                                    child: Align(
                                      alignment: Alignment.centerRight,
                                      child: Image.asset('assets/image/MR/check_ring_round_duotone.png',
                                        width: 20.w,
                                      ),
                                    ),
                                  )
                                ],
                              ),

                            ),
                          ),
                          InkWell(
                            onTap: (){
                              if(StatusAcc == 'success'){
                                Get.to(() => const RegisBookbankMRPage());
                              }
                              setState(() {
                                selectRegisBb = true;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(top: 13.h),
                              width: 323.w,
                              height: 90.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.h),
                                border: Border.all(
                                    width: 1,
                                    color: Colors.white
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.3),
                                    offset: const Offset(1, 1),
                                    blurRadius: 30,
                                  ),
                                ],
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    const Color(0xFFFFFFFF).withOpacity(1),
                                    const Color(0xFFF3F3F3).withOpacity(0.6),
                                  ],
                                ),
                              ),
                              child: Stack(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 8.h,
                                        left: 15.w
                                    ),
                                    child: Image.asset('assets/image/MR/credit_card_duotone.png',
                                      width: 30.h,
                                      height: 30.h,
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 10.h,
                                        left: 50.w
                                    ),
                                    width: 200.w,
                                    height: 40.h,
                                    child: RichText(text: TextSpan(
                                      children:[
                                        TextSpan(
                                          text: 'ข้อมูลบัญชีธนาคาร',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt-Medium',
                                            color: const Color(0xFF282828),
                                            fontSize: 12.w,
                                          ),

                                        ),
                                        TextSpan(
                                          text: '\nลงทะเบียนสำหรับการรับรางวัลเงินสด',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: const Color(0xFF707070),
                                            fontSize: 11.w,
                                          ),
                                        ),
                                      ] ,
                                    ),
                                    ),
                                  ),
                                  StatusBb != "success"
                                      ? Container(
                                    margin: EdgeInsets.only(
                                        top: 50.h,
                                        left: 50.w
                                    ),
                                    width: 88.w,
                                    height: 24.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(13.h),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.3),
                                          offset: const Offset(1, 1),
                                          blurRadius: 30,
                                        ),
                                      ],
                                      color: const Color(0xFFD9D8D5),
                                    ),
                                    child: Center(
                                      child: Text('ยังไม่ลงทะเบียน',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF895F00),
                                          fontSize: 10.w,
                                        ),
                                      ),
                                    ),
                                  )
                                      : Container(
                                    margin: EdgeInsets.only(
                                        top: 50.h,
                                        left: 50.w
                                    ),
                                    width: 99.w,
                                    height: 24.h,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(13.h),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.3),
                                          offset: const Offset(1, 1),
                                          blurRadius: 30,
                                        ),
                                      ],
                                      color: const Color(0xFFD9D8D5),
                                    ),
                                    child: Center(
                                      child: Text('ลงทะเบียนเรียบร้อย',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFF5AB13B),
                                          fontSize: 10.w,
                                        ),
                                      ),
                                    ),
                                  ),
                                  StatusBb != "success"
                                      ? Container()
                                      : Padding(
                                    padding: EdgeInsets.only(right: 25.w),
                                    child: Align(
                                      alignment: Alignment.centerRight,
                                      child: Image.asset('assets/image/MR/check_ring_round_duotone.png',
                                        width: 20.w,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 27.h,
                          ),
                          Container(
                            child: selectRegisBb != false
                                ? showText()
                                : Container(),
                          ),
                        ],
                      ),
                    ),
                    StatusAcc == "success" && StatusBb == "success"
                        ? Padding(
                      padding: EdgeInsets.only(bottom: 30.h),
                      child: InkWell(
                        onTap: () async {
                          var result = await AppAlert.showConfirm(
                              context,
                              'ยืนยันข้อมูลสมัคร',
                              'คุณต้องการยืนยันข้อมูลการสมัคร\nตัวแทนแนะนำลูกค้า MR ของคุณ\nใช่หรือไม่?',
                              'ตกลง');
                          if(result == true){
                            Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => const RegisSuccessMRPage(),
                            ));
                          }
                        },
                        child: Container(
                            margin: EdgeInsets.only(top: 30.h),
                            width: 316.w,
                            height: 40.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5.h),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black45,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                              color: const Color(0xFF282828),
                            ),
                            child: Center(
                              child: RichText(
                                text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'อีกนิดเดียว',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w300,
                                          fontFamily: 'Prompt',
                                          color: const Color(0xFFFFB100),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                      TextSpan(
                                        text: '..ยืนยันข้อมูล',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w300,
                                          fontFamily: 'Prompt-Medium',
                                          color: const Color(0xFFFFFFFF),
                                          fontSize: 14.w,
                                        ),
                                      ),
                                    ]
                                ),
                              ),
                            )
                        ),
                      ),
                    )
                        : Container(),
                  ],
                ),
              ],
            ),
          );
        }
    );
  }

  showText(){
    if(StatusAcc != "success"){
      return SizedBox(
        width: 323.w,
        child: Row(
          children: [
            Container(
                width: 12.w,
                height: 43.h,
                alignment: Alignment.topLeft,
                child: Image.asset('assets/image/MR/!_icons.png',width: 11.h,)),
            SizedBox(
              width: 8.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('กรุณาเลือกลงทะเบียนตามลำดับรายการ',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Prompt-Medium',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
                Text(' 1. ข้อมูลสมัครตัวแทนผู้แนะนำ',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
                Text(' 2. ข้อมูลบัญชีธนาคาร',
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: const Color(0xFF707070),
                    fontSize: 11.w,
                  ),
                ),
              ],
            )
          ],
        ),
      );
    }
  }

}
