// import 'dart:convert';
//
// import 'package:firebase_analytics/firebase_analytics.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:mapp_prachakij_v3/component/alert.dart';
// import 'package:mapp_prachakij_v3/component/api.dart';
// import 'package:mapp_prachakij_v3/component/loader.dart';
// import 'package:mapp_prachakij_v3/component/secureStorage.dart';
// import 'package:mapp_prachakij_v3/component/url.dart';
// import 'package:mapp_prachakij_v3/component/widget.dart';
// import 'package:mapp_prachakij_v3/controller/center_controller.dart';
// import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
// import 'package:mapp_prachakij_v3/model/license.dart';
// import 'package:mapp_prachakij_v3/model/province.dart';
// import 'package:mapp_prachakij_v3/model/service_model/claim_home.dart';
// import 'package:mapp_prachakij_v3/controller/service/service.dart';
// import 'package:mapp_prachakij_v3/view/home/<USER>';
// import 'package:mapp_prachakij_v3/view/home/<USER>/pick_location.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:multi_image_picker/multi_image_picker.dart';
// import 'package:cached_network_image/cached_network_image.dart';
//
// class ClaimHomePage extends StatefulWidget {
//   const ClaimHomePage({Key? key}) : super(key: key);
//
//   @override
//   State<ClaimHomePage> createState() => _ClaimHomePageState();
// }
//
// class _ClaimHomePageState extends State<ClaimHomePage> {
//   final SecureStorage secureStorage = SecureStorage();
//   final TextEditingController _phoneNumberController = TextEditingController();
//   final TextEditingController _nameCustomerController = TextEditingController();
//   final TextEditingController _idLineController = TextEditingController();
//   final TextEditingController _characterController = TextEditingController();
//   final TextEditingController _numberController = TextEditingController();
//   final TextEditingController _detailController = TextEditingController();
//
//   int page = 1;
//
//   String? carSelect;
//   List<String> carItems = [];
//   bool addLicenseStatus = false;
//
//   String? provinceSelect;
//   ProvinceList? resProvince;
//   List<String> provinceItem = [];
//
//   String? insuranceSelect;
//   List<String> insuranceItems = [];
//
//   carLicenseList? resCarLicense;
//   ResponseAddLicense? resAddLicense;
//
//   bool locationStatus = false;
//   String? locationName;
//   String? locationLatLng;
//
//   List<dynamic> file = [];
//   int _imageMulti = 0;
//   List<Asset> images = <Asset>[];
//
//   bool actionSc = false;
//   Map resSc = {};
//   List<dynamic> scItems = [];
//   String? scSelect;
//
//   ResponseClaimHome? resClaimHome;
//
//   final profileCtl = Get.put(ProfileController());
//
//
//   bool isLoading = true;
//
//   static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     analytics.setCurrentScreen(screenName: "ClaimHome");
//     carItems = [];
//     getData();
//   }
//
//   getData() async {
//     if(profileCtl.token.value != null){
//       _phoneNumberController.text = profileCtl.profile.value.mobile.toString();
//     }
//     await getProvince();
//     await getCarLicense();
//     await getInsurance();
//     await getSe();
//     setState(() {
//       isLoading = false;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//
//     if (isLoading == true) {
//       return AppLoader.loaderWaitPage(context);
//     }
//
//     return Scaffold(
//       body: InkWell(
//         onTap: () {
//           FocusScopeNode currentFocus = FocusScope.of(context);
//           if (!currentFocus.hasPrimaryFocus) {
//             currentFocus.unfocus();
//           }
//         },
//         child: Stack(
//           children: [
//             Container(
//               width: 1.sw,
//               height: 1.sh,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.bottomCenter,
//                   end: Alignment.topCenter,
//                   stops: const [0.2, 0.8],
//                   colors: [
//                     const Color(0xFFF4F4F4).withOpacity(0.85),
//                     const Color(0xFFFFFFFF).withOpacity(0.85),
//                   ],
//                 ),
//               ),
//             ),
//             Container(
//               width: 1.sw,
//               height: 1.sh,
//               decoration: BoxDecoration(
//                 gradient: LinearGradient(
//                   begin: Alignment.bottomCenter,
//                   end: Alignment.topCenter,
//                   stops: const [0, 0.5],
//                   colors: [
//                     const Color(0xFF282828),
//                     const Color(0xFFFFB100).withOpacity(0.20),
//                   ],
//                 ),
//               ),
//             ),
//             Align(
//               alignment: Alignment.bottomLeft,
//               child: Container(
//                 margin: EdgeInsets.only(bottom: 0.01.sh),
//                 child: Row(
//                   children: [
//                     SizedBox(
//                       width: 0.05.sw,
//                     ),
//                     AppWidget.boldTextS(
//                         context,
//                         "ISUZU",
//                         12.sp,
//                         Colors.white,
//                         FontWeight.w700),
//                     AppWidget.boldTextS(
//                         context,
//                         " PRACHAKIJ",
//                         12.sp,
//                         Colors.white,
//                         FontWeight.w400),
//                   ],
//                 ),
//               ),
//             ),
//             // Container(
//             //   margin: EdgeInsets.only(
//             //       bottom: 0.07.sh
//             //   ),
//             //   child: Align(
//             //     alignment: Alignment.bottomCenter,
//             //     child: Image.asset('assets/image/service/pmg/home_pmg.png',width: 0.8.sw,),
//             //   ),
//             // ),
//             Container(
//               width: 1.sw,
//               height: 0.72.sh,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.only(
//                   // topLeft: Radius.circular(0.03.sh),
//                   // topRight: Radius.circular(0.03.sh),
//                   bottomLeft: Radius.circular(0.03.sh),
//                   bottomRight: Radius.circular(0.03.sh),
//                 ),
//                 gradient: LinearGradient(
//                   begin: Alignment.bottomCenter,
//                   end: Alignment.topCenter,
//                   stops: const [0, 0.5],
//                   colors: [
//                     const Color(0xFFF4F4F4).withOpacity(0.85),
//                     const Color(0xFFFFFFFF).withOpacity(0.85),
//                   ],
//                 ),
//               ),
//             ),
//             page == 1
//                 ? buildPage1()
//                 : page == 2
//                 ? buildPage2()
//                 : buildPage3()
//           ],
//         ),
//       ),
//     );
//   }
//
//   buildPage1(){
//     return SingleChildScrollView(
//       child: Container(
//         margin: EdgeInsets.only(
//           left: 0.05.sw,
//           right: 0.05.sw,
//           top: 0.02.sh,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.end,
//               children: [
//                 AppWidget.boldTextS(
//                     context,
//                     "หมายเหตุ : ",
//                     18.sp,
//                     Colors.red,
//                     FontWeight.w400),
//                 Expanded(child: AppWidget.normalText(
//                     context,
//                     "กรุณากรอกข้อมูล และอัพโหลดเอกสาร",
//                     17.sp,
//                     const Color(0xFF000000),
//                     FontWeight.w400),)
//               ],
//             ),
//             AppWidget.normalText(
//                 context,
//                 "ด้านล่าง ให้ครบถ้วนทุกรายการ",
//                 16.sp,
//                 const Color(0xFF000000),
//                 FontWeight.w400),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "รถของคุณ",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               child: selectCar(context, carItems, "รถของคุณ"),
//             ),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             addLicenseStatus == true
//                 ?
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 _inputAddLicense(
//                     "พยัญชนะ",
//                     TextInputType.text,
//                     _characterController
//                 ),
//                 _inputNumberAddLicense(
//                     "หมายเลข",
//                     TextInputType.number,
//                     _numberController
//                 ),
//                 selectProvince(
//                     context,
//                     provinceItem
//                 ),
//                 InkWell(
//                   onTap: (){
//                     addLicense();
//                   },
//                   child: Container(
//                     width: 0.28.sw,
//                     height: 0.045.sh,
//                     decoration: BoxDecoration(
//                       color: _characterController.text !=
//                           '' &&
//                           _numberController
//                               .text !=
//                               '' &&
//                           provinceSelect != null
//                           ? const Color(0xFFFFB100)
//                           : Colors.white,
//                       borderRadius: BorderRadius.all(
//                           Radius.circular(
//                               0.03.sw
//                           )
//                       ),
//                       boxShadow: [
//                         BoxShadow(
//                           color: const Color(0xFF000000).withOpacity(0.05),
//                           blurRadius: 3,
//                           offset: const Offset(0, 2),
//                         )
//                       ],
//                     ),
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         _characterController.text != ''&&
//                             _numberController.text != '' &&
//                             provinceSelect != null
//                             ?  AppWidget.boldText(
//                             context,
//                             'บันทึกทะเบียนรถ',
//                             13.sp,
//                             Colors.white,
//                             FontWeight.w400)
//                             : AppWidget.boldText(
//                             context,
//                             'เพิ่มทะเบียนรถ',
//                             13.sp,
//                             const Color(0xFF707070).withOpacity(0.5),
//                             FontWeight.w400),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             )
//                 : Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 SizedBox(
//                   width: 0.28.sw,
//                   child: InkWell(
//                     onTap: () {
//                       setState(() {
//                         addLicenseStatus = true;
//                       });
//                     },
//                     child: Container(
//                       width: 0.28.sw,
//                       height: 0.045.sh,
//                       padding: EdgeInsets.symmetric(
//                         horizontal:
//                         0.02.sw,
//                       ),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.all(
//                             Radius.circular(
//                                 0.03.sw
//                             )
//                         ),
//                         boxShadow: [
//                           BoxShadow(
//                             color: const Color(0xFF000000).withOpacity(0.05),
//                             blurRadius: 3,
//                             offset: const Offset(0, 2),
//                           )
//                         ],
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.end,
//                         children: [
//                           AppWidget.boldText(
//                               context,
//                               "เพิ่มทะเบียนรถ",
//                               13.sp,
//                               const Color(0xFF707070).withOpacity(0.5),
//                               FontWeight.w400),
//                           const Icon(Icons.add,color: Color(0xFFFFB100),size: 14,)
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "ชื่อ-นามสกุล : ผู้ติดต่อ",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               height: 0.05.sh,
//               padding: EdgeInsets.only(
//                 left: 0.03.sw,
//                 right: 0.03.sw,
//               ),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(
//                   Radius.circular(
//                       0.03.sw
//                   ),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: const Color(0xFF000000).withOpacity(0.05),
//                     blurRadius: 3,
//                     offset: const Offset(0, 2),
//                   )
//                 ],
//                 color: Colors.white,
//               ),
//               child: TextField(
//                 inputFormatters: [
//                   FilteringTextInputFormatter.allow(
//                     AppService.thaiAZ100RegExp()
//                   )
//                 ],
//                 controller: _nameCustomerController,
//                 // onChanged: checkForm(),
//                 style: TextStyle(
//                   fontFamily: 'Prompt-Medium',
//                   fontSize: 14.sp,
//                   color: Colors.black,
//                   letterSpacing: 0.2,
//                 ),
//                 // keyboardType: TextInputType.number,
//                 decoration: InputDecoration(
//                   border: InputBorder.none,
//                   hintText: 'กรุณาใส่ชื่อ-นามสกุล : ผู้ติดต่อ',
//                   hintStyle: TextStyle(
//                     fontSize: 14.sp,
//                     color: const Color(0xFF707070).withOpacity(0.5),
//                     fontFamily: 'Prompt',
//                     letterSpacing: 0.2,
//                     shadows: <Shadow>[
//                       Shadow(
//                         offset: const Offset(0, 0),
//                         blurRadius: 0,
//                         color: const Color(0xFF000000).withOpacity(0),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "บริษัทประกัน",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               child: selectInsurance(context, insuranceItems, "บริษัทประกัน"),
//             ),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Container(),
//                 TextButton(
//                   onPressed: (){
//                     setState(() {
//                       page = 2;
//                     });
//                   },
//                   child: Text('ถัดไป',
//                     style: TextStyle(
//                         color: Colors.black,
//                         decoration: TextDecoration.underline,
//                         fontFamily: 'Prompt-Medium',
//                         fontSize: 18.sp,
//                         fontWeight: FontWeight.w400
//                     ),
//                   ),
//                 ),
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   buildPage2(){
//     return SingleChildScrollView(
//       child: Container(
//         margin: EdgeInsets.only(
//           left: 0.05.sw,
//           right: 0.05.sw,
//           top: 0.02.sh,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.end,
//               mainAxisAlignment: MainAxisAlignment.start,
//               children: [
//                 AppWidget.boldTextS(
//                     context,
//                     "หมายเหตุ : ",
//                     18.sp,
//                     Colors.red,
//                     FontWeight.w400),
//                 Expanded(child: AppWidget.normalText(
//                     context,
//                     "กรุณากรอกข้อมูล และอัพโหลดเอกสาร",
//                     17.sp,
//                     const Color(0xFF000000),
//                     FontWeight.w400),)
//               ],
//             ),
//             AppWidget.normalText(
//                 context,
//                 "ด้านล่าง ให้ครบถ้วนทุกรายการ",
//                 16.sp,
//                 const Color(0xFF000000),
//                 FontWeight.w400),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "ตำแหน่งที่ตั้งให้บริการ",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             InkWell(
//               onTap: ()async{
//                 await Permission.locationWhenInUse.request();
//                 List<String> result = await Navigator.push(
//                   context,
//                   MaterialPageRoute(
//                       builder: (context) => const PickLocationPage()),
//                 );
//                 if (result != null){
//                   setState(() {
//                     locationName = result[0];
//                     locationLatLng = result[1];
//                     locationStatus = true;
//                   });
//                 } else {
//                   //ไม่ได้เลือกตำแหน่ง
//                   setState(() {
//                     locationName = null;
//                     locationLatLng = null;
//                     locationStatus = false;
//                   });
//                 }
//               },
//               child: locationStatus == true
//                   ? Container(
//                 width: 1.sw,
//                 height: 0.05.sh,
//                 // color: Colors.red,
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.all(
//                       Radius.circular(
//                           0.03.sw
//                       )
//                   ),
//                   boxShadow: [
//                     BoxShadow(
//                       color: const Color(0xFF000000).withOpacity(0.05),
//                       blurRadius: 3,
//                       offset: const Offset(0, 2),
//                     )
//                   ],
//                 ),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Icon(Icons.my_location, color: const Color(0xFF707070), size: 0.055.sw,),
//                     SizedBox(
//                       width: 0.02.sw,
//                     ),
//                     AppWidget.normalTextS(
//                         context,
//                         locationName,
//                         16.sp,
//                         const Color(0xFF707070),
//                         FontWeight.w400),
//                   ],
//                 ),
//               )
//                   : Container(
//                 width: 1.sw,
//                 height: 0.05.sh,
//                 // color: Colors.red,
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.all(
//                       Radius.circular(
//                           0.03.sw
//                       )
//                   ),
//                   boxShadow: [
//                     BoxShadow(
//                       color: const Color(0xFF000000).withOpacity(0.05),
//                       blurRadius: 3,
//                       offset: const Offset(0, 2),
//                     )
//                   ],
//                 ),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Icon(Icons.my_location, color: const Color(0xFF707070), size: 0.055.sw,),
//                     SizedBox(
//                       width: 0.02.sw,
//                     ),
//                     AppWidget.normalTextS(
//                         context,
//                         "ปักหมุดตำแหน่งที่ตั้งของคุณ",
//                         16.sp,
//                         const Color(0xFF707070),
//                         FontWeight.w400),
//                   ],
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(alignment: Alignment.center,
//               child: AppWidget.normalTextS(
//                   context,
//                   "กรณีปักหมุดไม่ได้กรุณาพิมพ์รายละเอียดด้านล่าง",
//                   12.sp,
//                   const Color(0xFFFF3B30),
//                   FontWeight.w400),
//             ),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "รายละเอียดเพิ่มเติม",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               height: 0.1.sh,
//               padding: EdgeInsets.only(
//                 left: 0.03.sw,
//                 right: 0.03.sw,
//               ),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(
//                   Radius.circular(
//                       0.03.sw
//                   ),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: const Color(0xFF000000).withOpacity(0.05),
//                     blurRadius: 3,
//                     offset: const Offset(0, 2),
//                   )
//                 ],
//                 color: Colors.white,
//               ),
//               child: TextField(
//                 inputFormatters: [
//                   FilteringTextInputFormatter.deny(
//                     AppService.denyEmoji()
//                   ),
//                 ],
//                 controller: _detailController,
//                 // onChanged: checkForm(),
//                 maxLines: 2,
//                 style: TextStyle(
//                   fontFamily: 'Prompt-Medium',
//                   fontSize: 14.sp,
//                   color: Colors.black,
//                   letterSpacing: 0.2,
//                 ),
//                 // keyboardType: TextInputType.number,
//                 decoration: InputDecoration(
//                   border: InputBorder.none,
//                   hintText: 'พิมพ์รายละเอียดเพิ่มเติม',
//                   hintStyle: TextStyle(
//                     fontSize: 14.sp,
//                     color: const Color(0xFF707070).withOpacity(0.5),
//                     fontFamily: 'Prompt',
//                     letterSpacing: 0.2,
//                     shadows: <Shadow>[
//                       Shadow(
//                         offset: const Offset(0, 0),
//                         blurRadius: 0,
//                         color: const Color(0xFF000000).withOpacity(0),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "เบอร์โทรติดต่อ",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               height: 0.05.sh,
//               padding: EdgeInsets.only(
//                 left: 0.03.sw,
//                 right: 0.03.sw,
//               ),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(
//                   Radius.circular(
//                       0.03.sw
//                   ),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: const Color(0xFF000000).withOpacity(0.05),
//                     blurRadius: 3,
//                     offset: const Offset(0, 2),
//                   )
//                 ],
//                 color: Colors.white,
//               ),
//               child: TextField(
//                 inputFormatters: [
//                   FilteringTextInputFormatter.allow(
//                     RegExp(r'^[0-9]{0,10}$'),
//                   ),
//                 ],
//                 controller: _phoneNumberController,
//                 // onChanged: checkForm(),
//                 style: TextStyle(
//                   fontFamily: 'Prompt-Medium',
//                   fontSize: 14.sp,
//                   color: Colors.black,
//                   letterSpacing: 0.2,
//                 ),
//                 keyboardType: TextInputType.number,
//                 decoration: InputDecoration(
//                   border: InputBorder.none,
//                   hintText: 'กรุณาใส่เบอร์โทรศัพท์',
//                   hintStyle: TextStyle(
//                     fontSize: 14.sp,
//                     color: const Color(0xFF707070).withOpacity(0.5),
//                     fontFamily: 'Prompt',
//                     letterSpacing: 0.2,
//                     shadows: <Shadow>[
//                       Shadow(
//                         offset: const Offset(0, 0),
//                         blurRadius: 0,
//                         color: const Color(0xFF000000).withOpacity(0),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             AppWidget.boldTextS(
//                 context,
//                 "ID Line",
//                 16.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             SizedBox(
//               height: 0.01.sh,
//             ),
//             Container(
//               height: 0.05.sh,
//               padding: EdgeInsets.only(
//                 left: 0.03.sw,
//                 right: 0.03.sw,
//               ),
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.all(
//                   Radius.circular(
//                       0.03.sw
//                   ),
//                 ),
//                 boxShadow: [
//                   BoxShadow(
//                     color: const Color(0xFF000000).withOpacity(0.05),
//                     blurRadius: 3,
//                     offset: const Offset(0, 2),
//                   )
//                 ],
//                 color: Colors.white,
//               ),
//               child: TextField(
//                 inputFormatters: [
//                   FilteringTextInputFormatter.deny(
//                       AppService.denyEmoji()
//                   )
//                 ],
//                 controller: _idLineController,
//                 // onChanged: checkForm(),
//                 style: TextStyle(
//                   fontFamily: 'Prompt-Medium',
//                   fontSize: 14.sp,
//                   color: Colors.black,
//                   letterSpacing: 0.2,
//                 ),
//                 // keyboardType: TextInputType.number,
//                 decoration: InputDecoration(
//                   border: InputBorder.none,
//                   hintText: 'กรุณาใส่ ID Line',
//                   hintStyle: TextStyle(
//                     fontSize: 14.sp,
//                     color: const Color(0xFF707070).withOpacity(0.5),
//                     fontFamily: 'Prompt',
//                     letterSpacing: 0.2,
//                     shadows: <Shadow>[
//                       Shadow(
//                         offset: const Offset(0, 0),
//                         blurRadius: 0,
//                         color: const Color(0xFF000000).withOpacity(0),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//             SizedBox(
//               height: 0.02.sh,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 TextButton(
//                   onPressed: (){
//                     setState(() {
//                       page = 1;
//                     });
//                   },
//                   child: Text('ก่อนหน้า',
//                     style: TextStyle(
//                         color: Colors.black,
//                         decoration: TextDecoration.underline,
//                         fontFamily: 'Prompt-Medium',
//                         fontSize: 18.sp,
//                         fontWeight: FontWeight.w400
//                     ),
//                   ),
//                 ),
//                 TextButton(
//                   onPressed: (){
//                     setState(() {
//                       page = 3;
//                     });
//                   },
//                   child: Text('ถัดไป',
//                     style: TextStyle(
//                         color: Colors.black,
//                         decoration: TextDecoration.underline,
//                         fontFamily: 'Prompt-Medium',
//                         fontSize: 18.sp,
//                         fontWeight: FontWeight.w400
//                     ),
//                   ),
//                 ),
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   buildPage3(){
//     return SingleChildScrollView(
//       child: Container(
//         margin: EdgeInsets.only(
//           left: 0.05.sw,
//           right: 0.05.sw,
//           top: 0.02.sh,
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.end,
//               mainAxisAlignment: MainAxisAlignment.start,
//               children: [
//                 AppWidget.boldTextS(
//                     context,
//                     "หมายเหตุ : ",
//                     18.sp,
//                     Colors.red,
//                     FontWeight.w400),
//                 Expanded(child: AppWidget.normalText(
//                     context,
//                     "กรุณากรอกข้อมูล และอัพโหลดเอกสาร",
//                     17.sp,
//                     const Color(0xFF000000),
//                     FontWeight.w400),)
//               ],
//             ),
//             AppWidget.normalText(
//                 context,
//                 "ด้านล่าง ให้ครบถ้วนทุกรายการ",
//                 16.sp,
//                 const Color(0xFF000000),
//                 FontWeight.w400),
//             SizedBox(
//               width: 1.sw,
//               child: Column(
//                 children: [
//                   SizedBox(
//                     height: 0.01.sh,
//                   ),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       AppWidget.boldTextS(
//                           context,
//                           "กรุณาอัพโหลดรูปถ่าย ",
//                           16.sp,
//                           Colors.black,
//                           FontWeight.w600),
//                       AppWidget.boldTextS(
//                           context,
//                           "ความเสียหายของรถ",
//                           16.sp,
//                           const Color(0xFFFFB100),
//                           FontWeight.w600),
//                     ],
//                   ),
//                   AppWidget.normalText(
//                       context,
//                       "รูปชิ้นส่วนที่เสียหาย (ตามรายการที่แจ้งเคลมซ่อม) อย่างชัดเจน",
//                       16.sp,
//                       const Color(0xFF707070),
//                       FontWeight.w400),
//                   AppWidget.normalText(
//                       context,
//                       "*กรุณากดเลือกรูปถ่ายตามรายการทั้งหมดให้ครบ ในครั้งเดียว",
//                       16.sp,
//                       Colors.red,
//                       FontWeight.w400),
//                   SizedBox(
//                     height: 0.01.sh,
//                   ),
//                   file.isEmpty
//                       ? InkWell(
//                     onTap: (){
//                       uploadMulti();
//                     },
//                     child: Container(
//                       width: 0.4.sw,
//                       height: 0.05.sh,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(0.03.sh),
//                           topRight: Radius.circular(0.03.sh),
//                           bottomLeft: Radius.circular(0.03.sh),
//                           bottomRight: Radius.circular(0.03.sh),
//                         ),
//                         boxShadow: [
//                           BoxShadow(
//                             color: const Color(0xFF000000).withOpacity(0.1),
//                             blurRadius: 5,
//                             offset: const Offset(0, 2),
//                           )
//                         ],
//                         color: const Color(0xFF333333),
//                       ),
//                       child: Center(
//                         child: AppWidget.boldTextS(
//                             context,
//                             "อัพโหลด",
//                             16.sp,
//                             Colors.white,
//                             FontWeight.w400),
//                       ),
//                     ),
//                   )
//                       : Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       InkWell(
//                         onTap: (){
//                           setState(() {
//                             _imageMulti = 0;
//                           });
//                           showImageMultiClaim();
//                         },
//                         child: Container(
//                             width: 0.05.sh,
//                             height: 0.05.sh,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.only(
//                                 topLeft: Radius.circular(0.03.sh),
//                                 topRight: Radius.circular(0.03.sh),
//                                 bottomLeft: Radius.circular(0.03.sh),
//                                 bottomRight: Radius.circular(0.03.sh),
//                               ),
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: const Color(0xFF000000).withOpacity(0.1),
//                                   blurRadius: 5,
//                                   offset: const Offset(0, 2),
//                                 )
//                               ],
//                               color: const Color(0xFF333333),
//                             ),
//                             child: Image.asset('assets/image/service/pmg/preview-btn.png')
//                         ),
//                       ),
//                       SizedBox(width: 0.02.sw,),
//                       Container(
//                         width: 0.4.sw,
//                         height: 0.05.sh,
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(0.03.sh),
//                             topRight: Radius.circular(0.03.sh),
//                             bottomLeft: Radius.circular(0.03.sh),
//                             bottomRight: Radius.circular(0.03.sh),
//                           ),
//                           boxShadow: [
//                             BoxShadow(
//                               color: const Color(0xFF000000).withOpacity(0.1),
//                               blurRadius: 5,
//                               offset: const Offset(0, 2),
//                             )
//                           ],
//                           border: Border.all(
//                               width: 2,
//                               color: Colors.black
//                           ),
//                           color: const Color(0xFFFFB100),
//                         ),
//                         child: Center(
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 AppWidget.boldTextS(
//                                     context,
//                                     "อัพโหลด",
//                                     16.sp,
//                                     Colors.white,
//                                     FontWeight.w400),
//                                 const Icon(
//                                   Icons.done,
//                                   color: Colors.white,
//                                   size: 24.0,
//                                 ),
//                               ],
//                             )
//                         ),
//                       ),
//                       SizedBox(width: 0.02.sw,),
//                       InkWell(
//                         onTap: (){
//                           uploadMulti();
//                         },
//                         child: AppWidget.boldTextS(
//                             context,
//                             "แก้ไข",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       )
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             SizedBox(
//               height: 0.04.sh,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 TextButton(
//                   onPressed: (){
//                     setState(() {
//                       page = 2;
//                     });
//                   },
//                   child: Text('ก่อนหน้า',
//                     style: TextStyle(
//                         color: Colors.black,
//                         decoration: TextDecoration.underline,
//                         fontFamily: 'Prompt-Medium',
//                         fontSize: 18.sp,
//                         fontWeight: FontWeight.w400
//                     ),
//                   ),
//                 ),
//                 Row(
//                   children: [
//                         insuranceSelect != "" &&
//                         carSelect != "" &&
//                         _phoneNumberController.text != "" &&
//                         file.isNotEmpty
//                         ?
//                     InkWell(
//                       onTap: (){
//                         print('ยกเลิก');
//                         // cancelClaim();
//                       },
//                       child: Center(
//                         child: AppWidget.boldTextS(
//                             context,
//                             "ยกเลิก",
//                             18.sp,
//                             const Color(0xFF707070),
//                             FontWeight.w400),
//                       ),
//                     )
//                         : Container(),
//                     SizedBox(width: 0.02.sw,),
//                     // fileOne != "" &&
//                         insuranceSelect != "" &&
//                         carSelect != "" &&
//                         _nameCustomerController.text != "" &&
//                         _phoneNumberController.text != "" &&
//                         file.isNotEmpty
//                         ? InkWell(
//                       onTap: (){
//                         print('ยืนยันข้อมูล');
//                         scDialog(context);
//                       },
//                       child: Container(
//                         width: 0.3.sw,
//                         height: 0.05.sh,
//                         decoration: BoxDecoration(
//                           borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(0.03.sh),
//                             topRight: Radius.circular(0.03.sh),
//                             bottomLeft: Radius.circular(0.03.sh),
//                             bottomRight: Radius.circular(0.03.sh),
//                           ),
//                           boxShadow: const [
//                             BoxShadow(
//                                 color: Colors.black, spreadRadius: 2),
//                           ],
//                           gradient: const LinearGradient(
//                             begin: Alignment.bottomCenter,
//                             end: Alignment.topCenter,
//                             stops: [0, 1.0],
//                             colors: [
//                               Color(0xFF282828),
//                               Color(0xFF333333)
//                             ],
//                           ),
//                         ),
//                         child: Center(
//                           child: AppWidget.boldTextS(
//                               context,
//                               "ยืนยันข้อมูล",
//                               16.sp,
//                               Colors.white,
//                               FontWeight.w400),
//                         ),
//                       ),
//                     )
//                         : Container(),
//                   ],
//                 )
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   selectCar(context, items, title) {
//     return InkWell(
//       onTap: () {
//         showCupertinoModalPopup(
//           context: context,
//           builder: (context){
//             int selectTempIndex;
//             if (carSelect != null) {
//               selectTempIndex = items.indexOf(carSelect);
//             } else {
//               selectTempIndex = 0;
//             }
//             return Column(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 Container(
//                   decoration: const BoxDecoration(
//                     color: Color(0xffffffff),
//                     border: Border(
//                       bottom: BorderSide(
//                         color: Color(0xff999999),
//                         width: 0.0,
//                       ),
//                     ),
//                   ),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       CupertinoButton(
//                         onPressed: (){
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ยกเลิก",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                       CupertinoButton(
//                         onPressed: (){
//                           setState(() {
//                             carSelect = items[selectTempIndex];
//                           });
//
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ตกลง",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Container(
//                   height: 0.2.sh,
//                   color: Colors.white,
//                   child: CupertinoPicker(
//                     scrollController: FixedExtentScrollController(
//                       initialItem: selectTempIndex,
//                     ),
//                     itemExtent: 50,
//                     useMagnifier: true,
//                     backgroundColor: Colors.white,
//                     onSelectedItemChanged: (index) {
//                       setState(() {
//                         selectTempIndex = index;
//                       });
//                     },
//                     children: List.generate(items.length, (index) {
//                       return Center(
//                         child: AppWidget.boldText(
//                             context,
//                             items[index],
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       );
//                     }),
//                   ),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//       child: Container(
//         width: 1.sw,
//         height: 0.05.sh,
//         padding: EdgeInsets.symmetric(
//           horizontal: 0.03.sw,
//         ),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(
//               0.03.sw
//           ),
//           boxShadow: [
//             BoxShadow(
//               color: const Color(0xFF000000).withOpacity(0.05),
//               blurRadius: 3,
//               offset: const Offset(0, 2),
//             )
//           ],
//           color: Colors.white,
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             carSelect == null
//                 ? AppWidget.normalText(
//                 context,
//                 "เลือกหมายเลขทะเบียน",
//                 14.sp,
//                 const Color(0xFF707070).withOpacity(0.5),
//                 FontWeight.w400)
//                 : AppWidget.boldText(
//                 context,
//                 carSelect,
//                 14.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             const Icon(Icons.arrow_drop_down_rounded,
//                 color: Color(0xFFFFB100)),
//           ],
//         ),
//       ),
//     );
//   }
//
//   selectInsurance(context, items, title) {
//     return InkWell(
//       onTap: () {
//         showCupertinoModalPopup(
//           context: context,
//           builder: (context){
//             int selectTempIndex;
//             if (insuranceSelect != null) {
//               selectTempIndex = items.indexOf(insuranceSelect);
//             } else {
//               selectTempIndex = 0;
//             }
//             return Column(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 Container(
//                   decoration: const BoxDecoration(
//                     color: Color(0xffffffff),
//                     border: Border(
//                       bottom: BorderSide(
//                         color: Color(0xff999999),
//                         width: 0.0,
//                       ),
//                     ),
//                   ),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       CupertinoButton(
//                         onPressed: (){
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ยกเลิก",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                       CupertinoButton(
//                         onPressed: (){
//                           setState(() {
//                             insuranceSelect = items[selectTempIndex];
//                           });
//
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ตกลง",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Container(
//                   height: 0.2.sh,
//                   color: Colors.white,
//                   child: CupertinoPicker(
//                     scrollController: FixedExtentScrollController(
//                       initialItem: selectTempIndex,
//                     ),
//                     itemExtent: 50,
//                     useMagnifier: true,
//                     backgroundColor: Colors.white,
//                     onSelectedItemChanged: (index) {
//                       setState(() {
//                         selectTempIndex = index;
//                       });
//                     },
//                     children: List.generate(items.length, (index) {
//                       return Center(
//                         child: AppWidget.boldText(
//                             context,
//                             items[index],
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       );
//                     }),
//                   ),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//       child: Container(
//         width: 1.sw,
//         height: 0.05.sh,
//         padding: EdgeInsets.symmetric(
//           horizontal: 0.03.sw,
//         ),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(
//               0.03.sw
//           ),
//           boxShadow: [
//             BoxShadow(
//               color: const Color(0xFF000000).withOpacity(0.05),
//               blurRadius: 3,
//               offset: const Offset(0, 2),
//             )
//           ],
//           color: Colors.white,
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             insuranceSelect == null
//                 ? AppWidget.normalText(
//                 context,
//                 "เลือกบริษัทประกัน",
//                 14.sp,
//                 const Color(0xFF707070).withOpacity(0.5),
//                 FontWeight.w400)
//                 : AppWidget.boldText(
//                 context,
//                 insuranceSelect,
//                 14.sp,
//                 Colors.black,
//                 FontWeight.w600),
//             const Icon(Icons.arrow_drop_down_rounded,
//                 color: Color(0xFFFFB100)),
//           ],
//         ),
//       ),
//     );
//   }
//
//   selectProvince(context, items) {
//     return InkWell(
//       onTap: () {
//         showCupertinoModalPopup(
//           context: context,
//           builder: (context){
//             int selectTempIndex;
//             if (provinceSelect != null) {
//               selectTempIndex = items.indexOf(provinceSelect);
//             } else {
//               selectTempIndex = 0;
//             }
//             return Column(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 Container(
//                   decoration: const BoxDecoration(
//                     color: Color(0xffffffff),
//                     border: Border(
//                       bottom: BorderSide(
//                         color: Color(0xff999999),
//                         width: 0.0,
//                       ),
//                     ),
//                   ),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       CupertinoButton(
//                         onPressed: (){
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ยกเลิก",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                       CupertinoButton(
//                         onPressed: (){
//                           setState(() {
//                             provinceSelect = items[selectTempIndex];
//                           });
//
//                           Navigator.pop(context);
//                         },
//                         padding: const EdgeInsets.symmetric(
//                           horizontal: 20,
//                           vertical: 10,
//                         ),
//                         child: AppWidget.boldText(
//                             context,
//                             "ตกลง",
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       ),
//                     ],
//                   ),
//                 ),
//                 Container(
//                   height: 0.2.sh,
//                   color: Colors.white,
//                   child: CupertinoPicker(
//                     scrollController: FixedExtentScrollController(
//                       initialItem: selectTempIndex,
//                     ),
//                     itemExtent: 50,
//                     useMagnifier: true,
//                     backgroundColor: Colors.white,
//                     onSelectedItemChanged: (index) {
//                       setState(() {
//                         selectTempIndex = index;
//                       });
//                     },
//                     children: List.generate(items.length, (index) {
//                       return Center(
//                         child: AppWidget.boldText(
//                             context,
//                             items[index],
//                             16.sp,
//                             Colors.black,
//                             FontWeight.w600),
//                       );
//                     }),
//                   ),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//       child: Container(
//         width: 0.18.sw,
//         height: 0.045.sh,
//         decoration: BoxDecoration(
//           color: Colors.white,
//           border: Border.all(
//             width: 1,
//             color: const Color(0xFFFFB100),
//           ),
//           borderRadius: BorderRadius.all(
//               Radius.circular(
//                   0.03.sw
//               )
//           ),
//           boxShadow: [
//             BoxShadow(
//               color: const Color(0xFF000000).withOpacity(0.05),
//               blurRadius: 3,
//               offset: const Offset(0, 2),
//             )
//           ],
//         ),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             provinceSelect == null
//                 ? AppWidget.normalText(
//                 context,
//                 "จังหวัด",
//                 14.sp,
//                 const Color(0xFF707070).withOpacity(0.5),
//                 FontWeight.w400)
//                 : SizedBox(
//               width: 0.15.sw,
//               // height: 0.045.sh,
//               child: Text(provinceSelect!,
//                 textAlign: TextAlign.center,
//                 maxLines: 1,
//                 overflow: TextOverflow.ellipsis,
//                 style: TextStyle(
//                   fontFamily: 'Prompt',
//                   fontSize: 14.sp,
//                   color: const Color(0xFF707070),
//                   fontWeight: FontWeight.w400,
//                   letterSpacing: 0.2,
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   _inputAddLicense(hint, type, controller){
//     return Container(
//       width: 0.18.sw,
//       height: 0.045.sh,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         border: Border.all(
//           width: 1,
//           color: const Color(0xFFFFB100),
//         ),
//         borderRadius: BorderRadius.all(
//             Radius.circular(
//                 0.03.sw
//             )
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: const Color(0xFF000000).withOpacity(0.05),
//             blurRadius: 3,
//             offset: const Offset(0, 2),
//           )
//         ],
//       ),
//       child: TextField(
//         inputFormatters: [
//           FilteringTextInputFormatter.allow(
//             AppService.numberAZThai4RegExp(),
//           ),
//         ],
//         textAlign: TextAlign.center,
//         controller: controller,
//         style: TextStyle(
//           fontFamily: 'Prompt',
//           fontSize: 14.sp,
//           color: const Color(0xFF707070),
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.2,
//         ),
//         keyboardType: type,
//         decoration: InputDecoration(
//           hintText: hint,
//           hintStyle: TextStyle(
//             fontFamily: 'Prompt',
//             fontSize: 14.sp,
//             color: const Color(0xFF707070).withOpacity(0.5),
//             fontWeight: FontWeight.w400,
//             letterSpacing: 0.2,
//           ),
//           border: InputBorder.none,
//         ),
//       ),
//     );
//   }
//
//   _inputNumberAddLicense(hint, type, controller){
//     return Container(
//       width: 0.18.sw,
//       height: 0.045.sh,
//       decoration: BoxDecoration(
//         color: Colors.white,
//         border: Border.all(
//           width: 1,
//           color: const Color(0xFFFFB100),
//         ),
//         borderRadius: BorderRadius.all(
//             Radius.circular(
//                 0.03.sw
//             )
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: const Color(0xFF000000).withOpacity(0.05),
//             blurRadius: 3,
//             offset: const Offset(0, 2),
//           )
//         ],
//       ),
//       child: TextField(
//         inputFormatters: [
//           FilteringTextInputFormatter.allow(
//             AppService.numberAZThai4RegExp(),
//           ),
//         ],
//         textAlign: TextAlign.center,
//         controller: controller,
//         style: TextStyle(
//           fontFamily: 'Prompt',
//           fontSize: 14.sp,
//           color: const Color(0xFF707070),
//           fontWeight: FontWeight.w400,
//           letterSpacing: 0.2,
//         ),
//         keyboardType: type,
//         decoration: InputDecoration(
//           hintText: hint,
//           hintStyle: TextStyle(
//             fontFamily: 'Prompt',
//             fontSize: 14.sp,
//             color: const Color(0xFF707070).withOpacity(0.5),
//             fontWeight: FontWeight.w400,
//             letterSpacing: 0.2,
//           ),
//           border: InputBorder.none,
//         ),
//       ),
//     );
//   }
//
//   addLicense() async {
//     AppLoader.loader(context);
//     resAddLicense = await CenterController.addLicense(context, profileCtl.profile.value.mobile, _characterController.text, _numberController.text, provinceSelect);
//     if(resAddLicense!.status == 200){
//       AppLoader.dismiss(context);
//       AppAlert.showNewAccept(context, 'Success', 'เพิ่มรถเรียบร้อย', 'ตกลง');
//       getCarLicense();
//       setState(() {
//         addLicenseStatus = false;
//         _characterController.text = '';
//         _numberController.text = '';
//         provinceSelect = null;
//       });
//     } else {
//       AppLoader.dismiss(context);
//       AppAlert.showNewAccept(
//           context, 'กรอกข้อมูลไม่ครบ', 'กรอกข้อมูลไม่ครบ', 'ตกลง');
//     }
//   }
//
//   getCarLicense() async {
//     carItems = [];
//     resCarLicense = await CenterController.getLicense(context, profileCtl.profile.value.mobile);
//     if (resCarLicense != null && resCarLicense!.data != null) {
//       carItems.addAll(resCarLicense!.data!.map((e) => e.carLicense.toString()));
//     }
//   }
//
//   getProvince() async {
//     resProvince = await CenterController.getProvince(context);
//     if (resProvince != null && resProvince!.data != null) {
//       for (int i = 0; i < resProvince!.data!.length; i++) {
//         provinceItem.add(resProvince!.data![i].nameTh.toString());
//       }
//     }
//   }
//
//   getInsurance() async {
//     try {
//       final response = await AppApi.get(AppUrl.getInsurance);
//       int status = response['status'];
//       if (status == 200) {
//         List<String> items = [];
//         for (var i = 0; i < response['result'].length; i++) {
//           var insurance = response['result'][i]['name_insurance'];
//           items.add(insurance);
//         }
//         setState(() {
//           insuranceItems = items;
//         });
//       } else {
//         AppLoader.dismiss(context);
//         AppService.sendError(response.toString(), 'getInsurance in profile');
//       }
//     } catch (e) {
//       AppLoader.dismiss(context);
//       AppService.sendError(e.toString(), 'getInsurance in profile');
//     }
//   }
//
//   uploadMulti() async {
//     try {
//       AppLoader.loader(context);
//       List<Asset> resultList = <Asset>[];
//       String error = 'No Error Detected';
//       List<dynamic> listFile = [];
//       PermissionStatus status = await AppService.accessMediaLibrary(context);
//       if (status.isDenied || status.isPermanentlyDenied) {
//         await AppAlert.showNewAccept(context, 'ไม่สามารถเข้าถึงไฟล์ได้',
//             'กรุณาเปิดการเข้าถึงไฟล์', 'ตกลง');
//         openAppSettings();
//       } else {
//         try {
//           resultList = await MultiImagePicker.pickImages(
//             maxImages: 10,
//             enableCamera: true,
//             selectedAssets: images,
//             cupertinoOptions: const CupertinoOptions(takePhotoIcon: "chat"),
//             materialOptions: const MaterialOptions(
//               actionBarColor: "#abcdef",
//               actionBarTitle: "เลือกรูป",
//               allViewTitle: "รูปทั้งหมด",
//               useDetailsView: false,
//               selectCircleStrokeColor: "#000000",
//             ),
//           );
//         } on Exception catch (e) {
//           error = e.toString();
//         }
//         if (!mounted) return;
//
//         for (Asset asset in resultList) {
//           final ByteData byteData = await asset.getByteData(quality: 75);
//           final List<int> imageData = byteData.buffer.asUint8List();
//           final String base64Image = base64Encode(imageData);
//           final imgUrl = await AppService.uploadPhotoToS3(
//               base64Image, "MappPMS/ClaimOnline");
//           listFile.add(imgUrl);
//         }
//         setState(() {
//           file = listFile;
//         });
//         AppLoader.dismiss(context);
//       }
//     } catch (e) {
//       AppAlert.showError(context, 'Upload รูปภาพไม่สำเร็จ', 'ตกลง');
//       AppService.sendError(
//           'upload photo to s3 error', 'uploadSlip in part_payment');
//       AppLoader.dismiss(context);
//     }
//   }
//
//   showImageMultiClaim() {
//     showDialog(
//       barrierDismissible: true,
//       context: context,
//       builder: (BuildContext context) {
//         return Center(
//           child: AlertDialog(
//             backgroundColor: Colors.white.withOpacity(0.9),
//             contentPadding: EdgeInsets.zero,
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.all(
//                 Radius.circular(
//                   0.05.sh,
//                 ),
//               ),
//               side: const BorderSide(
//                 width: 1.5,
//                 color: Colors.black,
//               ),
//             ),
//             title: Column(
//               children: <Widget>[
//                 Container(
//                   margin: EdgeInsets.only(
//                     top: 0.02.sh,
//                     bottom: 0.02.sh,
//                   ),
//                   child: Text(
//                     'รูปภาพที่อัพโหลด',
//                     style: TextStyle(
//                       fontFamily: 'Prompt',
//                       color: const Color(0xFF282828),
//                       fontSize: 20.sp,
//                       letterSpacing: 0.4,
//                       shadows: <Shadow>[
//                         Shadow(
//                           offset: const Offset(0, 1),
//                           blurRadius: 1.0,
//                           color: const Color(0xFF000000).withOpacity(0.15),
//                         ),
//                       ],
//                     ),
//                     textAlign: TextAlign.center,
//                   ),
//                 ),
//               ],
//             ),
//             content: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: <Widget>[
//                 Stack(
//                   children: <Widget>[
//                     CachedNetworkImage(
//                       imageUrl: file[_imageMulti],
//                       imageBuilder: (context, imageProvider) => Container(
//                         width: 0.8.sw,
//                         height: 0.4.sh,
//                         decoration: BoxDecoration(
//                         image: DecorationImage(
//                           image: imageProvider,
//                           fit: BoxFit.fitWidth,
//                         ),
//                       ),
//                       ),
//                       fit: BoxFit.cover,
//                       placeholder: (context, url) => SizedBox(
//                         width: 0.2.sh,
//                         height: 0.2.sh,
//                         child: const Center(
//                           child: CircularProgressIndicator(
//                             color: Colors.orange,
//                           ),
//                         ),
//                       ),
//                       errorWidget: (context, url, error) => Container(
//                         width: 0.2.sh,
//                         height: 0.2.sh,
//                         decoration: BoxDecoration(
//                           boxShadow: [
//                             BoxShadow(
//                               color: const Color(0xFF000000).withOpacity(0.1),
//                               offset: const Offset(0, 3),
//                               blurRadius: 3.0,
//                             ),
//                           ],
//                         ),
//                         child: const Center(
//                           child: Icon(
//                             Icons.error,
//                             color: Color(0xFFFFB100),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Container(
//                       alignment: Alignment.center,
//                       margin: EdgeInsets.only(
//                           top: 0.2.sh,
//                           bottom: 0.02.sh,
//                           left: 0.02.sw,
//                           right: 0.02.sw
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 if (_imageMulti == 0) {
//                                   _imageMulti = file.length - 1;
//                                 } else {
//                                   _imageMulti = _imageMulti - 1;
//                                 }
//                               });
//                               Navigator.pop(context);
//                               showImageMultiClaim();
//                             },
//                             child: Image.asset(
//                                 'assets/image/service/pmg/back-btn.png',
//                                 width: 0.1.sw
//                             ),
//                           ),
//                           GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 if (_imageMulti == file.length - 1) {
//                                   _imageMulti = 0;
//                                 } else {
//                                   _imageMulti = _imageMulti + 1;
//                                 }
//                               });
//                               Navigator.pop(context);
//                               showImageMultiClaim();
//                             },
//                             child: Image.asset(
//                                 'assets/image/service/pmg/next-btn.png',
//                                 width: 0.1.sw
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//                 Container(
//                   margin: EdgeInsets.only(
//                     top: 0.02.sh,
//                     bottom: 0.02.sh,
//                   ),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: <Widget>[
//                       GestureDetector(
//                         onTap: () {
//                           Navigator.of(context).pop(true);
//                         },
//                         child: SizedBox(
//                           width: 0.3.sw,
//                           height: 0.05.sh,
//                           child: Container(
//                             alignment: Alignment.center,
//                             decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.all(
//                                   Radius.circular(
//                                       0.08.sw
//                                   ),
//                                 ),
//                                 boxShadow: [
//                                   BoxShadow(
//                                     color: const Color(0xFF000000).withOpacity(0.1),
//                                     blurRadius: 5,
//                                     offset: const Offset(0, 2),
//                                   )
//                                 ],
//                                 color: const Color(0xFF333333)),
//                             child: Text(
//                               "ปิดหน้านี้",
//                               style: TextStyle(
//                                 fontFamily: "Prompt-Medium",
//                                 fontSize: 16.sp,
//                                 letterSpacing: 0.4,
//                                 color: Colors.white,
//                               ),
//                             ),
//                           ),
//                         ),
//                       )
//                     ],
//                   ),
//                 )
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }
//
//   scDialog(context) async {
//     return showDialog(
//         barrierDismissible: true,
//         context: context,
//         builder: (BuildContext context) {
//           return Center(
//             child: AlertDialog(
//               backgroundColor: Colors.white.withOpacity(0.95),
//               titlePadding: EdgeInsets.fromLTRB(
//                   0.025.sw,
//                   0.05.sh,
//                   0.025.sw,
//                   0.0.sh
//               ),
//               contentPadding: EdgeInsets.fromLTRB(
//                   0.025.sw,
//                   0.02.sh,
//                   0.025.sw,
//                   0.0.sh
//               ),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.all(
//                   Radius.circular(0.1.sw),
//                 ),
//                 side: BorderSide(
//                   width: 1.w,
//                   color: const Color(0xFF000000),
//                 ),
//               ),
//               title: Image.asset(
//                 'assets/icon/contact_icon.png',
//                 height: 0.05.sh,
//               ),
//               content: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Text("แจ้งเคลมออนไลน์",
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                       fontFamily: 'Prompt-Medium',
//                       fontSize: 22.sp,
//                       color: const Color(0xFF000000),
//                       fontWeight: FontWeight.w500,
//                       letterSpacing: 0.2,
//                     ),
//                   ),
//                   SizedBox(
//                     height: 0.005.sh,
//                   ),
//                   Text("สะดวก รวดเร็ว ง่ายนิดเดียว",
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                       fontFamily: 'Prompt',
//                       fontSize: 16.sp,
//                       color: const Color(0xFF282828),
//                       fontWeight: FontWeight.w500,
//                       letterSpacing: 0.2,
//                     ),
//                   ),
//                   SizedBox(
//                     height: 0.01.sh,
//                   ),
//                   Text("กรุณาเลือกทีมงานที่ต้องการ",
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                       fontFamily: 'Prompt',
//                       fontSize: 16.sp,
//                       color: const Color(0xFF282828),
//                       fontWeight: FontWeight.w500,
//                       letterSpacing: 0.2,
//                     ),
//                   ),
//                   SizedBox(
//                     height: 0.01.sh,
//                   ),
//                   actionSc
//                       ? Container(
//                     margin: EdgeInsets.only(
//                       left: 0.05.sw,
//                       right: 0.05.sw,
//                     ),
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.all(
//                         Radius.circular(
//                           0.05.sw,
//                         ),
//                       ),
//                       color: Colors.white,
//                       boxShadow: const [
//                         BoxShadow(
//                             color: Color(0xFF282828), spreadRadius: 0.5),
//                       ],
//                     ),
//                     height: 0.4.sh,
//                     child: Stack(
//                       children: [
//                         Container(
//                           margin: EdgeInsets.only(
//                             top: 0.06.sh,
//                           ),
//                           child: SingleChildScrollView(
//                             child: Column(
//                               children: [
//                                 for (var i in scItems)
//                                   GestureDetector(
//                                     onTap: () {
//                                       setState(() {
//                                         resSc = i;
//                                         actionSc = false;
//                                       });
//                                       Navigator.pop(context);
//                                       scDialog(context);
//                                     },
//                                     child: Row(
//                                       children: [
//                                         Container(
//                                           margin: EdgeInsets.symmetric(
//                                               vertical: 0.02.sw
//                                           ),
//                                           child: Row(
//                                             children: [
//                                               Container(
//                                                 padding: EdgeInsets.only(
//                                                     left: 0.02.sw
//                                                 ),
//                                                 child: CachedNetworkImage(
//                                                   imageUrl: i['C_picture'],
//                                                   imageBuilder: (context,
//                                                       imageProvider) =>
//                                                       Container(
//                                                         width: 0.06.sh,
//                                                         height: 0.06.sh,
//                                                         decoration:
//                                                         BoxDecoration(
//                                                           shape:
//                                                           BoxShape.circle,
//                                                           border: Border.all(
//                                                               color: const Color(0xFFFFB100),
//                                                               width: 1
//                                                           ),
//                                                           boxShadow: [
//                                                             BoxShadow(
//                                                               color: const Color(0xFF000000).withOpacity(0.1),
//                                                               offset: const Offset(0, 3),
//                                                               blurRadius: 3.0,
//                                                             ),
//                                                           ],
//                                                           image:
//                                                           DecorationImage(
//                                                             image:
//                                                             imageProvider,
//                                                             fit: BoxFit.cover,
//                                                           ),
//                                                         ),
//                                                       ),
//                                                   fit: BoxFit.cover,
//                                                   placeholder:
//                                                       (context, url) =>
//                                                       SizedBox(
//                                                         width: 0.05.sh,
//                                                         height: 0.05.sh,
//                                                         child: const Center(
//                                                           child:
//                                                           CircularProgressIndicator(
//                                                             color: Colors.orange,
//                                                           ),
//                                                         ),
//                                                       ),
//                                                   errorWidget: (context,
//                                                       url, error) =>
//                                                       Container(
//                                                         width: 0.05.sh,
//                                                         height: 0.05.sh,
//                                                         decoration:
//                                                         BoxDecoration(
//                                                           shape:
//                                                           BoxShape.circle,
//                                                           border: Border.all(
//                                                               color: const Color(
//                                                                   0xFFFFB100),
//                                                               width: 1
//                                                           ),
//                                                           boxShadow: [
//                                                             BoxShadow(
//                                                               color: const Color(
//                                                                   0xFF000000)
//                                                                   .withOpacity(
//                                                                   0.1),
//                                                               offset: const Offset(
//                                                                   0, 3),
//                                                               blurRadius: 3.0,
//                                                             ),
//                                                           ],
//                                                         ),
//                                                         child: const Center(
//                                                           child: Icon(
//                                                             Icons.error,
//                                                             color: Color(
//                                                                 0xFFFFB100),
//                                                           ),
//                                                         ),
//                                                       ),
//                                                 ),
//                                               ),
//                                               Container(
//                                                   padding: EdgeInsets.only(
//                                                     left: 0.02.sw,
//                                                   ),
//                                                   child: AppWidget.normalText(
//                                                       context,
//                                                       i['C_name'].toString(),
//                                                       18.sp,
//                                                       Colors.black,
//                                                       FontWeight.w400)
//                                               )
//                                             ],
//                                           ),
//                                         )
//                                       ],
//                                     ),
//                                   )
//                               ],
//                             ),
//                           ),
//                         ),
//                         Row(
//                           children: [
//                             GestureDetector(
//                               onTap: () {
//                                 setState(() {
//                                   actionSc = false;
//                                 });
//                                 Navigator.pop(context);
//                                 scDialog(context);
//                               },
//                               child: Container(
//                                 margin: EdgeInsets.only(
//                                   top: 0.01.sh,
//                                   left: 0.05.sw,
//                                   right: 0.05.sw,
//                                 ),
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                   children: [
//                                     Container(
//                                         padding: EdgeInsets.only(
//                                           left: 0.04.sw,
//                                         ),
//                                         child: AppWidget.boldTextS(
//                                             context,
//                                             "กดเลือก",
//                                             18.sp,
//                                             Colors.black,
//                                             FontWeight.w500)
//                                     ),
//                                     Container(
//                                         margin: EdgeInsets.only(
//                                           left: 0.2.sw,
//                                         ),
//                                         child: const Icon(Icons.expand_more,
//                                             color: Colors.black, size: 24.0))
//                                   ],
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   )
//                       : resSc.isEmpty
//                       ? GestureDetector(
//                     onTap: () {
//                       setState(() {
//                         actionSc = true;
//                       });
//                       Navigator.pop(context);
//                       scDialog(context);
//                     },
//                     child: Container(
//                       margin: EdgeInsets.only(
//                         left: 0.05.sw,
//                         right: 0.05.sw,
//                       ),
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(0.5.sh),
//                         color: Colors.white,
//                         boxShadow: const [
//                           BoxShadow(
//                               color: Colors.black, spreadRadius: 1),
//                         ],
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Container(
//                             padding: EdgeInsets.only(
//                               top: 0.015.sh,
//                               bottom: 0.015.sh,
//                               left: 0.04.sw,
//                             ),
//                             child: AppWidget.boldTextS(
//                                 context,
//                                 "กดเลือก",
//                                 18.sp,
//                                 Colors.black,
//                                 FontWeight.w500),
//                           ),
//                           Container(
//                               margin: EdgeInsets.only(
//                                 right: 0.04.sw,
//                               ),
//                               child: const Icon(Icons.expand_more,
//                                   color: Colors.black, size: 24.0))
//                         ],
//                       ),
//                     ),
//                   )
//                       : GestureDetector(
//                     onTap: () {
//                       setState(() {
//                         actionSc = true;
//                       });
//                       Navigator.pop(context);
//                       scDialog(context);
//                     },
//                     child: Container(
//                       // width: 0.6.sw,
//                       // height: 0.15.sh,
//                       margin: EdgeInsets.only(
//                         left: 0.05.sw,
//                         right: 0.05.sw,
//                       ),
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(60),
//                         color: const Color(0xFFFFB100),
//                         boxShadow: const [
//                           BoxShadow(
//                               color: Colors.black, spreadRadius: 1),
//                         ],
//                       ),
//                       child: Container(
//                         margin: EdgeInsets.symmetric(
//                           vertical: 0.02.sw,
//                         ),
//                         child: Row(
//                           children: [
//                             Container(
//                               margin: EdgeInsets.only(
//                                   left: 0.01.sw),
//                               child: CachedNetworkImage(
//                                 imageUrl: resSc['C_picture'],
//                                 imageBuilder:
//                                     (context, imageProvider) =>
//                                     Container(
//                                       width: 0.1.sw,
//                                       height: 0.1.sw,
//                                       decoration: BoxDecoration(
//                                         shape: BoxShape.circle,
//                                         border: Border.all(
//                                             color: const Color(0xFFFFB100),
//                                             width: 1
//                                         ),
//                                         boxShadow: [
//                                           BoxShadow(
//                                             color: const Color(0xFF000000)
//                                                 .withOpacity(0.1),
//                                             offset: const Offset(0, 3),
//                                             blurRadius: 3.0,
//                                           ),
//                                         ],
//                                         image: DecorationImage(
//                                           image: imageProvider,
//                                           fit: BoxFit.cover,
//                                         ),
//                                       ),
//                                     ),
//                                 fit: BoxFit.cover,
//                                 placeholder: (context, url) =>
//                                     SizedBox(
//                                       width: 0.2.sw,
//                                       height: 0.2.sw,
//                                       child: const Center(
//                                         child:
//                                         CircularProgressIndicator(
//                                           color: Colors.orange,
//                                         ),
//                                       ),
//                                     ),
//                                 errorWidget: (context, url, error) =>
//                                     Container(
//                                       width: 0.2.sw,
//                                       height: 0.2.sw,
//                                       decoration: BoxDecoration(
//                                         shape: BoxShape.circle,
//                                         border: Border.all(
//                                             color: const Color(0xFFFFB100),
//                                             width: 1
//                                         ),
//                                         boxShadow: [
//                                           BoxShadow(
//                                             color: const Color(0xFF000000)
//                                                 .withOpacity(0.1),
//                                             offset: const Offset(0, 3),
//                                             blurRadius: 3.0,
//                                           ),
//                                         ],
//                                       ),
//                                       child: const Center(
//                                         child: Icon(
//                                           Icons.error,
//                                           color: Color(0xFFFFB100),
//                                         ),
//                                       ),
//                                     ),
//                               ),
//                             ),
//                             SizedBox(
//                               width: 0.02.sw,
//                             ),
//                             Container(
//                               width: 0.45.sw,
//                               child: Row(
//                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Container(
//                                       child: AppWidget.normalText(
//                                           context,
//                                           resSc['C_name'],
//                                           18.sp,
//                                           Colors.black,
//                                           FontWeight.w400)
//                                   ),
//                                   const Icon(
//                                     Icons.expand_more,
//                                     color: Color(0xFF383842),
//                                   )
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                   SizedBox(
//                     height: 0.04.sh,
//                   ),
//                   Container(
//                     margin: EdgeInsets.only(
//                         left: 0.05.sw,
//                         right: 0.05.sw
//                     ),
//                     child: Row(
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         InkWell(
//                           onTap: (){
//                             setState(() {
//                               resSc = {};
//                               scSelect = "";
//                               actionSc = false;
//                             });
//                             Navigator.pop(context);
//                             // saveClaim();
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             width: 0.25.sw,
//                             height: 0.04.sh,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.all(
//                                 Radius.circular(
//                                     1.sw
//                                 ),
//                               ),
//                               border: Border.all(
//                                 width: 2,
//                               ),
//                               color: const Color(0xFF282828),
//                             ),
//                             child: Text("ไม่เลือก",
//                               textAlign: TextAlign.center,
//                               style: TextStyle(
//                                 fontFamily: 'Prompt-Medium',
//                                 fontSize: 16.sp,
//                                 color: const Color(0xFFFFFFFF),
//                                 fontWeight: FontWeight.w500,
//                                 letterSpacing: 0.2,
//                                 shadows: <Shadow>[
//                                   Shadow(
//                                     offset: const Offset(0, 1),
//                                     blurRadius: 1.0,
//                                     color: const Color(0xFF000000).withOpacity(0.15),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                         InkWell(
//                           onTap: ()async{
//                             scSelect = resSc['C_name'];
//                             Navigator.pop(context);
//                             // saveClaim();
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             width: 0.3.sw,
//                             height: 0.04.sh,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.all(
//                                 Radius.circular(
//                                     1.sw
//                                 ),
//                               ),
//                               color: const Color(0xFFFFB100),
//                             ),
//                             child: Text("ดำเนินการต่อ",
//                               textAlign: TextAlign.center,
//                               style: TextStyle(
//                                 fontFamily: 'Prompt-Medium',
//                                 fontSize: 16.sp,
//                                 color: const Color(0xFFFFFFFF),
//                                 fontWeight: FontWeight.w500,
//                                 letterSpacing: 0.2,
//                                 shadows: <Shadow>[
//                                   Shadow(
//                                     offset: const Offset(0, 1),
//                                     blurRadius: 1.0,
//                                     color: const Color(0xFF000000).withOpacity(0.15),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                   SizedBox(
//                     height: 0.04.sh,
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }
//     );
//   }
//
//   getSe() async {
//     try {
//       final response = await AppApi.get(AppUrl.getPersonnelPaintBody);
//       int status = response['status'];
//       if (status == 200) {
//         setState(() {
//           scItems = response['result'];
//         });
//       } else {
//         AppLoader.dismiss(context);
//         AppService.sendError(response.toString(), 'getInsurance in profile');
//       }
//     } catch (e) {
//       AppLoader.dismiss(context);
//       AppService.sendError(e.toString(), 'getInsurance in profile');
//     }
//   }
//
//   cancelClaim() async {
//     try {
//       var resAlert =
//       await AppAlert.showConfirm(context, 'ยกเลิก','คุณต้องการยกเลิกการทำรายการใช่หรือไม่','ตกลง');
//       if (resAlert == true) {
//         Get.offAll(() => const HomeNavigator());
//       }
//     } catch (e) {
//       AppAlert.showError(context, 'ยกเลิกไม่สำเร็จ', 'ตกลง');
//     }
//   }
//
//   saveClaim() async {
//     var resAlert = await AppAlert.showConfirm(context, "ยืนยันข้อมูลเรียบร้อย", "", "ตกลง");
//     if(resAlert == true){
//       AppLoader.loader(context);
//       resClaimHome = await CenterController.createClaimHome(
//           context, _nameCustomerController.text, locationName, locationLatLng, file, _detailController.text,
//           _phoneNumberController.text, _idLineController.text, profileCtl.profile.value.mobile, profileCtl.profile.value.id, carSelect, insuranceSelect, scSelect);
//       if(resClaimHome!.status == 200){
//         AppLoader.dismiss(context);
//         Get.offAll(() => const HomeNavigator());
//       } else {
//         AppLoader.dismiss(context);
//       }
//     }
//   }
//
// }
