import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_Order_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/add_location.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/history.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/tab_pmg.dart';

class OrderPmg extends StatefulWidget {
  const OrderPmg({super.key});

  @override
  State<OrderPmg> createState() => _OrderPmgState();
}

class _OrderPmgState extends State<OrderPmg> {
  SparepartController ahlaiCtrl = Get.find<SparepartController>();
  ProvinceController provinceCtrl = Get.find<ProvinceController>();
  final sparepartOrderCtl = Get.find<SparepartOrderController>();

  var formattedPrice =
      NumberFormat.currency(locale: 'en_US', symbol: '', decimalDigits: 0);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        body: ColorfulSafeArea(
          color: Colors.black,
          child: Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                ),
              ),
              Column(
                children: [
                  _buildAppBar(),
                  _buildBody(),
                  _buildFooter(),
                ],
              ),
              // Floating loading indicator
              if (provinceCtrl.isLoading.value)
                Positioned.fill(
                  child: Container(
                    color: Colors.black54, // Semi-transparent background
                    child: Center(
                      child: CircularProgressIndicator(
                        color: Colors.orange,
                      ), // Loading spinner
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.15,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border:
                        Border.all(width: 1, color: const Color(0x88BCBCBC))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          Container(
            child: const Text(
              "ยืนยันการสั่งสินค้า",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2B1710),
              ),
            ),
          ),
          Container(
              width: MediaQuery.of(context).size.width * 0.15,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              child: Container(
                width: 36,
                height: 36,
              ))
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.only(top: 20),
        child: SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.05,
                    right: MediaQuery.of(context).size.width * 0.05,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        margin: EdgeInsets.only(
                          left: MediaQuery.of(context).size.width * 0.05,
                        ),
                        alignment: Alignment.centerLeft,
                        child: const Text(
                          "ที่อยู่จัดส่ง",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2B1710),
                          ),
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: 20,
                        margin: const EdgeInsets.only(
                          left: 10,
                          right: 10,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: List.generate(
                              (MediaQuery.of(context).size.width / 8).floor(),
                              (index) {
                            return const Text(
                              "-",
                              style: TextStyle(
                                  fontSize: 12, color: Color(0xFFBCBCBC)),
                            );
                          }),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Get.to(() => const AddLocation());
                        },
                        child: GetBuilder<ProvinceController>(
                            builder: (provinceCtrl) {
                            return Container(
                              width: MediaQuery.of(context).size.width,
                              height: 50,
                              margin: const EdgeInsets.only(
                                left: 10,
                                right: 10,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0x26FFB100),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Container(
                                margin: const EdgeInsets.only(
                                  left: 15,
                                  right: 15,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      children: [
                                        SvgPicture.asset(
                                            "assets/icon/sparePart/Location.svg"),
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        Container(
                                          width: MediaQuery.of(context).size.width *
                                              0.65,
                                          child: Text(
                                            provinceCtrl.showSelectedNOW,
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF2B1710),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Icon(
                                      Icons.arrow_forward_ios,
                                      size: 14,
                                      color: Color(0xFF2B1710),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                Obx(() => Container(
                  margin: EdgeInsets.only(
                    left: MediaQuery.of(context).size.width * 0.05,
                    right: MediaQuery.of(context).size.width * 0.05,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width,
                        margin: const EdgeInsets.only(
                          top: 10,
                          left: 10,
                          right: 10,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "รายการสินค้า",
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2B1710),
                              ),
                            ),
                            Text(
                              "${ahlaiCtrl.bagProduction.where((item) => item?.checked == true).length} รายการ",
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2B1710),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: 20,
                        margin: const EdgeInsets.only(
                          left: 10,
                          right: 10,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: List.generate(
                              (MediaQuery.of(context).size.width / 8).floor(),
                                  (index) {
                                return const Text(
                                  "-",
                                  style: TextStyle(
                                      fontSize: 12, color: Color(0xFFBCBCBC)),
                                );
                              }),
                        ),
                      ),
                      Column(
                        children: List.generate(ahlaiCtrl.bagProduction.length,
                                (index) {
                              if(ahlaiCtrl.bagProduction[index]!.checked! == false){
                                return Container();
                              }
                              var useData = ahlaiCtrl.product[
                              "${ahlaiCtrl.bagProduction[index]!.type}"]![
                              "${ahlaiCtrl.bagProduction[index]!.subtype}"]
                              ?[ahlaiCtrl.bagProduction[index]!.index];
                              return Container(
                                width: MediaQuery.of(context).size.width,
                                height: 100,
                                decoration: BoxDecoration(
                                  // color: Colors.white,
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Row(
                                  children: [
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        gradient: const LinearGradient(
                                          begin: Alignment(0.00, -1.00),
                                          end: Alignment(0, 1),
                                          colors: [
                                            Color(0xFFE8E6E2),
                                            Color(0xFFD9D8D5)
                                          ],
                                        ),
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: CachedNetworkImage(
                                          imageUrl: useData!['upload']![0].toString(),
                                          fit: BoxFit.cover,
                                          // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                                          placeholder: (context, url) => const SizedBox(
                                            width: 50,
                                            height: 50,
                                            child: Center(
                                              child: CircularProgressIndicator(color: Colors.orange),
                                            ),
                                          ),
                                          errorWidget: (context, url, error) => Container(
                                              width: 63,
                                              height: 63,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(20),
                                                border: Border.all(
                                                  width: 1,
                                                  color: const Color(0xFFE8E6E2),
                                                ),
                                              ),
                                              child: SvgPicture.asset("assets/icon/sparePart/clock.svg")),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width:
                                      MediaQuery.of(context).size.width - 180,
                                      height: 80,
                                      padding: const EdgeInsets.only(left: 10),
                                      child: Column(
                                        mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "${ahlaiCtrl.bagProduction[index]!.name}",
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF707070),
                                            ),
                                          ),
                                          Text(
                                            "จำนวน ${ahlaiCtrl.bagProduction[index]!.amount}",
                                            style: const TextStyle(
                                              fontSize: 13,
                                              fontWeight: FontWeight.w400,
                                              color: Color(0xFF707070),
                                            ),
                                          ),
                                          Text(
                                            "฿ ${formattedPrice.format((double.parse(useData?['sale_price']!).floor()) * (ahlaiCtrl.bagProduction[index]!.amount as num? ?? 0))}",
                                            style: const TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF895F00),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                  ],
                                ),
                              );
                            }),
                      ),
                    ],
                  ),
                )),
              ],
            )),
      ),
    );
  }

  Widget _buildFooter() {
    var totalPrice = ahlaiCtrl.bagProduction.fold(0, (previousValue, element) {
      // Ensure element is not null
      if (element != null && element.checked == true) {
        var amount = element.amount! as num? ?? 0;
        var price = double.parse(ahlaiCtrl.product[element.type]?[element.subtype]
        ?[element.index]?["sale_price"] ?? '0'); // Fallback to '0' in case price is null

        return previousValue + (amount * price).toInt();
      }

      // If element is null or unchecked, return previous value
      return previousValue;
    });
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.12,
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "ยอดรวม",
                  textAlign: TextAlign.end,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF282828),
                  ),
                ),
                Text(
                  "฿ ${formattedPrice.format(totalPrice)}",
                  textAlign: TextAlign.end,
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF895F00),
                  ),
                ),
              ],
            ),
            InkWell(
              onTap: () async {

                var complete =  await ahlaiCtrl.insertOrder(context);

                if(complete) {
                  await thankYouPopup(context);
                }
                /// Order
                ///
                ///
                // await thankyouPopup(context);
                // if(confirm) {
                //   await ahlaiCtrl.insertOrder();
                //   Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const TabPMG()));
                // }else{
                //   await ahlaiCtrl.insertOrder();
                //   Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const OrderHistory()));
                // }
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.35,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: const Color(0xFFFFB100),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0xDDFFB100),
                      blurRadius: 10,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: const Center(
                  child: Text(
                    "ยืนยันสั่งสินค้า",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF000000),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future thankYouPopup(context){
    return showDialog(
        barrierColor: Colors.transparent,
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return Material(
            color: Colors.transparent,
            child: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: const Color(0xB2FFB100),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "ขอบคุณที่ใช้บริการ",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2B1710),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      const Text(
                        "เราได้รับข้อมูลการสั่งสินค้าของคุณเรียบร้อย\nกรุณารอการติดต่อกลับเพื่อยืนยันอีกครั้ง\nภายใน 15-30 นาที",
                        maxLines: 3,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 15,
                          color: Color(0xFF2B1710),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Image.asset(
                          "assets/icon/sparePart/MascotWithEngine.png",
                        width: 140,
                        height: 100,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      InkWell(
                        onTap: () async {
                          await sparepartOrderCtl.getSparepartOrder();
                          // Get.to(() => const TabPMG());
                          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const TabPMG()));
                          // Get.off(const TabPMG());
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.85,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0, 0.3),
                              colors: [Color(0xAA000000), Color(0xFF000000)],
                            ),
                          ),
                          alignment: Alignment.center,
                          child: const Text(
                            "กลับไปหน้าสั่งสินค้า",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFFFFFFF),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      InkWell(
                        onTap: () async {
                          // Get.offAll(() => const OrderHistory());
                          await sparepartOrderCtl.getSparepartOrder();
                          // Get.to(() => const OrderHistory());
                          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const OrderHistory()));
                          // Get.off(OrderHistory());
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.85,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              width: 1,
                              color: const Color(0xFF000000),
                            ),
                          ),
                          alignment: Alignment.center,
                          child: const Text(
                            "ติดตามรายการสั่งสินค้า",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF000000),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }
}
