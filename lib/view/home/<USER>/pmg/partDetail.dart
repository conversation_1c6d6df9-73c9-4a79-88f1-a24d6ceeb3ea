import 'package:cached_network_image/cached_network_image.dart';
import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/bag_pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/order_pmg.dart';

class Partdetail extends StatefulWidget {
  const Partdetail({super.key});

  @override
  State<Partdetail> createState() => _PartdetailState();
}

class _PartdetailState extends State<Partdetail> {
  late FToast fToast;
  SparepartController ahlaiCtrl = Get.find<SparepartController>();
  var formattedPrice = NumberFormat.currency(locale: 'en_US', symbol: '',decimalDigits: 0);

  var useData;
  var activateImg = 0;

  @override
  void initState() {
    super.initState();
    fToast = FToast();
    fToast.init(context);
    useData = ahlaiCtrl.product["${ahlaiCtrl.showDetail!.type}"][
        "${ahlaiCtrl.showDetail!.subtype}"][ahlaiCtrl.showDetail!.index];
  }

  _showToast() {
    fToast.showToast(
      child: Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: 70.0,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: const Color(0xBB1A1818),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset("assets/icon/sparePart/check_ring_round.svg"),
              const SizedBox(
                width: 10,
              ),
              const Text(
                "เพิ่มลงถุงสินค้าแล้ว",
                style: TextStyle(
                  fontSize: 16.0,
                  color: Colors.white,
                ),
              ),
            ],
          )),
      gravity: ToastGravity.CENTER,
      toastDuration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     ahlaiCtrl.getData();
      //   },
      //   backgroundColor: Colors.white,
      //   child: SvgPicture.asset(
      //     "assets/icon/sparePart/bag.svg",
      //     color: const Color(0xFF000000),
      //   ),
      // ),
      body: ColorfulSafeArea(
        color: Colors.black,
        child: GetBuilder<SparepartController>(builder: (ctrl) {
          return Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                ),
              ),
              Column(
                children: [
                  _buildAppBar(),
                  _buildBody(),
                  _buildFooter()
                ],
              )
            ],
          );
        }),
      ),
    );
  }

  Widget _buildAppBar() {
    return Obx(() => Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.2,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius:  BorderRadius.circular(15),
                    border:
                    Border.all(width: 1, color: const Color(0x88BCBCBC))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          const Row(
            children: [
              Text(
                "รายละเอียดสินค้า",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2B1710),
                ),
              ),
            ],
          ),
          InkWell(
            onTap: () {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const BagPmg()));
            },
            child: Container(
              width: MediaQuery.of(context).size.width * 0.2,
              height: 50,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              alignment: Alignment.centerRight,
              child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SvgPicture.asset(
                      "assets/icon/sparePart/bag.svg",
                      color: const Color(0xFF000000),
                    ),
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.1,
                      height: 50,
                    ),
                    Positioned(
                      right: 0,
                      top: 10,
                      child: ahlaiCtrl.bagProduction.length == 0
                          ? SizedBox()
                          : Container(
                        width: 20,
                        height: 15,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.0),
                          gradient: const LinearGradient(
                            begin: Alignment(0.00, -1.00),
                            end: Alignment(0, 1),
                            colors: [
                              Color(0xFFFFC700),
                              Color(0xFFFFB100),
                              Color(0xFFFF9900)
                            ],
                          ),
                          boxShadow: const [
                            BoxShadow(
                              color: Color(0x80FF9900),
                              offset: Offset(0, 2),
                              blurRadius: 6,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            "${ahlaiCtrl.bagProduction.length}",
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    )
                  ]),
            ),
          )
        ],
      ),
    ));
  }

  Widget _buildBody() {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(
          left: MediaQuery.of(context).size.width * 0.05,
          right: MediaQuery.of(context).size.width * 0.05,
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            children: [
              const SizedBox(
                height: 10,
              ),
              Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: MediaQuery.of(context).size.width * 0.9,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.0),
                  gradient: const LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.0),
                  child: CachedNetworkImage(
                    imageUrl: "${useData['upload']![activateImg]}",
                    fit: BoxFit.cover,
                    // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                    placeholder: (context, url) => const SizedBox(
                      width: 50,
                      height: 50,
                      child: Center(
                        child: CircularProgressIndicator(color: Colors.orange),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                        width: 63,
                        height: 63,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            width: 1,
                            color: const Color(0xFFE8E6E2),
                          ),
                        ),
                        child: SvgPicture.asset("assets/icon/sparePart/no-image.svg"),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 70,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: List.generate(useData['upload']!.length, (index) {
                      return Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                                activateImg = index;
                                setState(() {});
                            },
                            child: Container(
                              width: 70,
                              height: 70,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15.0),
                                color: Colors.white,
                                /// make condition here
                                // gradient: const LinearGradient(
                                //   begin: Alignment(0.00, -1.00),
                                //   end: Alignment(0, 1),
                                //   colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                                // ),
                              ),
                              // child: Center(
                              //   child: SizedBox(
                              //     width: 20,
                              //     height: 20,
                              //     child: SvgPicture.asset(
                              //       "assets/icon/sparePart/no-image.svg",
                              //       color: const Color(0x88000000),
                              //     ),
                              //   ),
                              // ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10.0),
                                child: CachedNetworkImage(
                                  imageUrl: "${useData['upload']![index]}",
                                  fit: BoxFit.cover,
                                  // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                                  placeholder: (context, url) => const SizedBox(
                                    width: 50,
                                    height: 50,
                                    child: Center(
                                      child: CircularProgressIndicator(color: Colors.orange),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) => Container(
                                      width: 63,
                                      height: 63,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          width: 1,
                                          color: const Color(0xFFE8E6E2),
                                        ),
                                      ),
                                      child: SvgPicture.asset("assets/icon/sparePart/no-image.svg"),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                        ],
                      );
                    }),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 8,
                  ),
                  Text(
                    "${useData['product_name']!}",
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF282828),
                    ),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  Text(
                    "฿ ${formattedPrice.format(double.parse(useData['sale_price']!))}",
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF895F00),
                    ),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  const Divider(
                    color: Color(0xFFBCBCBC),
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  const Text(
                    "รายละเอียดสินค้า",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF282828),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    "${useData['property']}",
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF707070),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 50,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 100,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          InkWell(
            onTap: () async {
              await ahlaiCtrl.addBagThenPushToOrder(
                  ahlaiCtrl.showDetail!.type!,
                  ahlaiCtrl.showDetail!.subtype!,
                  ahlaiCtrl.showDetail!.index!,
                  ahlaiCtrl.showDetail!.name!
              );

              Get.to(() => const OrderPmg());
            },
            child: Container(
              width: MediaQuery.of(context).size.width * 0.45,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.0),
                color: const Color(0xFFFFB100),
                boxShadow: [
                  const BoxShadow(
                    color: Color(0xFFFFB100),
                    offset: Offset(0, 3),
                    blurRadius: 6,
                  ),
                ],
              ),
              child: const Center(
                child: Text(
                  "สั่งสินค้า",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () async {
              final checker = await ahlaiCtrl.addBag(
                  ahlaiCtrl.showDetail!.type!,
                  ahlaiCtrl.showDetail!.subtype!,
                  ahlaiCtrl.showDetail!.index!,
                  ahlaiCtrl.showDetail!.stock!,
                  ahlaiCtrl.showDetail!.name!);
              if(checker){
                _showToast();
              }
            },
            child: Container(
              width: MediaQuery.of(context).size.width * 0.45,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15.0),
                color: const Color(0x22707070),
              ),
              child: const Center(
                child: Text(
                  "เพิ่มลงถุงสินค้า",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
