import 'package:cached_network_image/cached_network_image.dart';
import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/order_pmg.dart';

class BagPmg extends StatefulWidget {
  const BagPmg({super.key});

  @override
  State<BagPmg> createState() => _BagPmgState();
}

class _BagPmgState extends State<BagPmg> {
  late FToast fToast;
  SparepartController ahlaiCtrl = Get.put(SparepartController());
  var formattedPrice =
      NumberFormat.currency(locale: 'en_US', symbol: '', decimalDigits: 0);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fToast = FToast();
    fToast.init(context);
    ahlaiCtrl.countCheckboxFunc();
  }

  _showToast() {
    fToast.showToast(
      child: Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: 70.0,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: const Color(0xBB1A1818),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset("assets/icon/sparePart/Alert1.svg"),
              const SizedBox(
                width: 10,
              ),
              const Text(
                "ยังไม่ได้เลือกรายการสินค้า",
                style: TextStyle(
                  fontSize: 16.0,
                  color: Colors.white,
                ),
              ),
            ],
          )),
      gravity: ToastGravity.CENTER,
      toastDuration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ColorfulSafeArea(
        color: Colors.black,
        child: GetBuilder<SparepartController>(
            builder: (ctrl) {
          return Stack(
            children: [
              Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                ),
              ),
              Column(
                children: [
                  _buildAppBar(),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Obx(() => InkWell(
                        onTap: () async {
                          if (ahlaiCtrl.countCheckbox == 0) {
                            // Fluttertoast.showToast(
                            //     msg: "ไม่ได้เลือกรายการที่จะลบ",
                            //     backgroundColor: const Color(0x88707070),
                            //     textColor: Colors.white,
                            //     fontSize: 13.0,
                            //   gravity: ToastGravity.CENTER,
                            // );
                            _showToast();
                            return;
                          } else {
                            var confirm = await AppAlert.showNewConfirm(
                                context,
                                "ลบสินค้า",
                                "คุณต้องการจะลบสินค้า\n${ahlaiCtrl.countCheckbox} รายการ ใช่หรือไม่?",
                                "ลบ",
                                "ยกเลิก");
                            if (confirm) {
                              // check checked checkbox
                              var checkedList = ahlaiCtrl.bagProduction
                                  .where((element) => element!.checked == true)
                                  .toList();
                              // call remove
                              checkedList.forEach((element) async {
                                await ahlaiCtrl.removeBag(element!);

                              });
                              ctrl.countCheckboxFunc();
                              setState(() {});
                            }
                          }
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.4,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            gradient: const LinearGradient(
                              begin: Alignment(0.00, -1.00),
                              end: Alignment(0, 1),
                              colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                            ),
                            border: Border.all(
                                width: 1, color: const Color(0x88707070)),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              SizedBox(
                                width: 15,
                                height: 15,
                                child: Image.asset(
                                    "assets/image/notification/notification_trash.png"),
                              ),
                              Text(
                                "ลบทั้งหมด (${ahlaiCtrl.countCheckbox}/${ahlaiCtrl.bagProduction.length})",
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF282828),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.05,
                      )
                    ],
                  ),
                  Obx(() => _buildBody()),
                  Obx(() => _buildFooter(context)),
                  // _buildFooter(context)
                ],
              )
            ],
          );
        }),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.15,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border:
                    Border.all(width: 1, color: const Color(0x88BCBCBC))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          Container(
            child: const Text(
              "ถุงสินค้า",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2B1710),
              ),
            ),
          ),
          Container(
              width: MediaQuery.of(context).size.width * 0.15,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              child: const SizedBox(
                width: 36,
                height: 36,
              ))
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.only(top: 20),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            children: ahlaiCtrl.bagProduction.length == 0
                ? [
                    const Center(
                      child: Text("ไม่มีสินค้าในถุงสินค้า",
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF282828))),
                    )
                  ]
                : List.generate(ahlaiCtrl.bagProduction.length, (index) {
                    var itemRow = ahlaiCtrl.bagProduction[index]!;
                    var useData = ahlaiCtrl.product["${itemRow.type}"]
                        ["${itemRow.subtype}"][itemRow.index];
                    return Column(
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width,
                          height: 100,
                          margin: EdgeInsets.only(
                            left: MediaQuery.of(context).size.width * 0.05,
                            right: MediaQuery.of(context).size.width * 0.05,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            children: [
                              Container(
                                height: 80,
                                alignment: Alignment.topCenter,
                                child: Transform.translate(
                                  offset: const Offset(0, -10),
                                  child: Checkbox(
                                    splashRadius: 1,
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    side: const BorderSide(
                                      color: Color(0xFFBCBCBC),
                                      width: 1,
                                    ),
                                    activeColor: const Color(0xFFFFB100),
                                    value:
                                        ahlaiCtrl.bagProduction[index]!.checked,
                                    onChanged: (value) async {
                                      ahlaiCtrl.bagProduction[index]!.checked =
                                          value;
                                      ahlaiCtrl.update();
                                      ahlaiCtrl.countCheckboxFunc();
                                      ahlaiCtrl.changeCheckAll(
                                          ahlaiCtrl.countCheckbox ==
                                              ahlaiCtrl.bagProduction.length);
                                    },
                                  ),
                                ),
                              ),
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15),
                                  gradient: const LinearGradient(
                                    begin: Alignment(0.00, -1.00),
                                    end: Alignment(0, 1),
                                    colors: [
                                      Color(0xFFE8E6E2),
                                      Color(0xFFD9D8D5)
                                    ],
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(15),
                                  child: CachedNetworkImage(
                                    imageUrl: useData!['upload']![0].toString(),
                                    fit: BoxFit.cover,
                                    // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                                    placeholder: (context, url) =>
                                        const SizedBox(
                                      width: 50,
                                      height: 50,
                                      child: Center(
                                        child: CircularProgressIndicator(
                                            color: Colors.orange),
                                      ),
                                    ),
                                    errorWidget: (context, url, error) =>
                                        Container(
                                            width: 63,
                                            height: 63,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                              border: Border.all(
                                                width: 1,
                                                color: const Color(0xFFE8E6E2),
                                              ),
                                            ),
                                            child: SvgPicture.asset(
                                                "assets/icon/sparePart/clock.svg")),
                                  ),
                                ),
                              ),
                              Container(
                                width: MediaQuery.of(context).size.width - 180,
                                height: 80,
                                padding: const EdgeInsets.only(left: 10),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.45,
                                          child: Text(
                                            "${ahlaiCtrl.bagProduction[index]!.name}\n${ahlaiCtrl.bagProduction[index]!.subtype}",
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: const TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF707070),
                                            ),
                                          ),
                                        ),
                                        InkWell(
                                          onTap: () async {
                                            print("remove");
                                            await ahlaiCtrl.removeBag(ahlaiCtrl
                                                .bagProduction[index]!);
                                            setState(() {});
                                          },
                                          child: Container(
                                            width: 20,
                                            height: 20,
                                            decoration: const BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Colors.red),
                                            child: const Icon(
                                              Icons.close,
                                              size: 14,
                                              color: Color(0xFFFFFFFF),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          "฿ ${formattedPrice.format(double.parse(useData?['sale_price']!).floor())}",
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w500,
                                            color: Color(0xFF895F00),
                                          ),
                                        ),
                                        Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.27,
                                          height: 35,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              border: Border.all(
                                                  width: 1,
                                                  color:
                                                      const Color(0x22282828))),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            children: [
                                              InkWell(
                                                onTap: () async {
                                                  await ahlaiCtrl.minusOne(
                                                      ahlaiCtrl.bagProduction[
                                                          index]!);
                                                },
                                                child: const SizedBox(
                                                  width: 20,
                                                  height: 20,
                                                  child: Icon(
                                                    Icons.remove,
                                                    size: 14,
                                                    color: Color(0xFF282828),
                                                  ),
                                                ),
                                              ),
                                              const VerticalDivider(
                                                color: Color(0xFFBCBCBC),
                                                thickness: 1,
                                                indent: 5,
                                                endIndent: 5,
                                              ),
                                              SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: Center(
                                                  child: Text(
                                                    "${ahlaiCtrl.bagProduction[index]!.amount!}",
                                                    style: const TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: Color(0xFF282828),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const VerticalDivider(
                                                color: Color(0xFFBCBCBC),
                                                thickness: 1,
                                                indent: 5,
                                                endIndent: 5,
                                              ),
                                              InkWell(
                                                onTap: () async {
                                                  await ahlaiCtrl.plusOne(
                                                      ahlaiCtrl.bagProduction[
                                                          index]!);
                                                },
                                                child: const SizedBox(
                                                  width: 20,
                                                  height: 20,
                                                  child: Icon(
                                                    Icons.add,
                                                    size: 14,
                                                    color: Color(0xFF282828),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    );
                  }),
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(context) {
    // var totalPrice = ahlaiCtrl.bagProduction.fold(0, (previousValue, element) => previousValue + element["amount"] * ahlaiCtrl.mockData["${element["type"]}"]!["${element["subtype"]}"]![element["index"]]["price"]);
    var totalPrice = ahlaiCtrl.bagProduction.fold(0, (previousValue, element) {
      // Ensure element is not null
      if (element != null && element.checked == true) {
        var amount = element.amount! as num? ?? 0;
        var price = double.parse(ahlaiCtrl.product[element.type]?[element.subtype]
        ?[element.index]?["sale_price"] ?? '0'); // Fallback to '0' in case price is null

        return previousValue + (amount * price).toInt();
      }

      // If element is null or unchecked, return previous value
      return previousValue;
    });


    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.12,
      color: Colors.white,
      child: Column(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.02,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.33,
                height: MediaQuery.of(context).size.height * 0.1,
                alignment: Alignment.topCenter,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Transform.translate(
                      offset: const Offset(0, -12),
                      child: Checkbox(
                          value: ahlaiCtrl.checkAll,
                          // splashRadius: 1,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                          side: const BorderSide(
                            color: Color(0xFFBCBCBC),
                            width: 2,
                            strokeAlign: 1.0,
                          ),
                          activeColor: const Color(0xFFFFB100),
                          onChanged: (value) {
                            ahlaiCtrl.changeCheckAll(value!);
                            ahlaiCtrl.bagProduction.forEach((element) {
                              element!.checked = value;
                            });
                            ahlaiCtrl.update();
                            ahlaiCtrl.countCheckboxFunc();
                          }),
                    ),
                    const Text(
                      "เลือกทั้งหมด",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF282828),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.64,
                height: MediaQuery.of(context).size.height * 0.1,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.25,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Text(
                            "ยอดรวม",
                            textAlign: TextAlign.end,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF282828),
                            ),
                          ),
                          Text(
                            "฿ ${formattedPrice.format(totalPrice)}",
                            textAlign: TextAlign.end,
                            style: const TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF895F00),
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        if (ahlaiCtrl.countCheckbox == 0) {
                          _showToast();
                        } else {
                          var confirm = await AppAlert.showNewConfirm(
                              context,
                              "ยืนยันสั่งซื้อสินค้า",
                              "คุณต้องการจะสั่งซื้อสินค้า\n${ahlaiCtrl.countCheckbox} รายการ ใช่หรือไม่?",
                              "ยืนยัน",
                              "ยกเลิก");
                          if (confirm) {
                            Get.to(() => const OrderPmg());
                          }
                        }
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.3 + 20,
                        height: 50,
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.3,
                          height: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: const Color(0xFFFFB100),
                            boxShadow: const [
                              BoxShadow(
                                color: Color(0xDDFFB100),
                                blurRadius: 10,
                                offset: Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              "สั่งสินค้า (${ahlaiCtrl.countCheckbox})",
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF000000),
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
