import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/save_activity.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/pmg_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/claim_history.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/claim_home.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/claim_online.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';

class PMGPage extends StatefulWidget {
  const PMGPage({Key? key}) : super(key: key);

  @override
  State<PMGPage> createState() => _PMGPageState();
}

class _PMGPageState extends State<PMGPage> with TickerProviderStateMixin {
  final SecureStorage secureStorage = SecureStorage();

  final pmgCtl = Get.put(PMGController());
  final profileCtl = Get.put(ProfileController());
  final saveActivityCtl = Get.put(SaveActivityController());

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "TalkWithPMG");
    // getPMG();
  }

  getPMG() async {
    if(pmgCtl.memberPMGList.data!.isEmpty){
      await pmgCtl.getMemberPMG();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Obx(() {
        if(pmgCtl.isLoading.value){
          return AppLoader.loaderWaitPage(context);
        } else {
          return Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                  ),
                ),
              ),
              Positioned(
                  top: Get.width < 500 ? 60 : 80,
                  right: Get.width < 500 ? 18 : 54,
                  child: Image.asset("assets/image/service/menu_fix.png",width: 61,height: 65,)),
              Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(
                      top: Get.width < 500 ? 60 : 80,
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(15),
                                  topLeft: Radius.circular(15),
                                  bottomRight: Radius.circular(15),
                                  bottomLeft: Radius.circular(15),
                                ),
                                border: Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                            child: const Icon(
                              Icons.arrow_back_ios_new,
                              size: 18,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            AppWidget.boldTextS(context, "ศูนย์ซ่อมสี", Get.width < 500 ? 16 : 18, const Color(0xFF2B1710),
                                FontWeight.w500),
                            AppWidget.normalTextS(context, "และตัวถัง", Get.width < 500 ? 16 : 18, const Color(0xFF664701),
                                FontWeight.w500),
                          ],
                        ),
                        const SizedBox(
                          width: 36,
                          height: 36,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 10,),
                  Container(
                    margin: EdgeInsets.only(
                      top: 10,
                      left: Get.width < 500 ? 18 : 54,
                      right: Get.width < 500 ? 18 : 54,
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 3,
                          height: 3,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color(0xFF664701),
                          ),
                        ),
                        AppWidget.boldTextS(context, " เลือกทีมงานที่ปรึกษา ", Get.width < 500 ? 12 : 14, const Color(0xFF2B1710),
                            FontWeight.w500),
                        AppWidget.normalTextS(context, "เพื่อสอบถามงานบริการ", Get.width < 500 ? 12 : 14, const Color(0xFF664701),
                            FontWeight.w400),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  buildTabBarView()
                ],
              ),
            ],
          );
        }
      })
    );
  }

  buildTabBarView(){
    return Expanded(
      child: TabBarView(
        controller: pmgCtl.pmgTabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: pmgCtl.memberPMGList.data!.length,
              itemBuilder: (BuildContext context, int index) {
                return Column(
                  children: [
                    buildListMember(context,
                        pmgCtl.memberPMGList.data![index].picture,
                        pmgCtl.memberPMGList.data![index].name,
                        pmgCtl.memberPMGList.data![index].team,
                        pmgCtl.memberPMGList.data![index].phone,
                        pmgCtl.memberPMGList.data![index].lineId),

                    const SizedBox(
                        height: 12,
                    ),
                  ],
                );
              }
          ),
          // const ClaimOnlinePage(),
          // const ClaimHomePage(),
          // const ClaimHistoryPage(),
        ],
      ),
    );
  }

  buildListMember(context, picture, name, team, phone, lineId){
    return Container(
      width: Get.height,
      height: 87,
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      padding: const EdgeInsets.only(
          left: 10,
          right: 10,
          top: 12,
          bottom: 12
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFF3F3F3),
            Color(0xFFFFFFFF),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4), // changes position of shadow
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                width: 63,
                height: 63,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CachedNetworkImage(
                    imageUrl: picture,
                    fit: BoxFit.cover, // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                    placeholder: (context, url) => const SizedBox(
                      width: 50,
                      height: 50,
                      child: Center(
                        child: CircularProgressIndicator(color: Colors.orange),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                        width: 63,
                        height: 63,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            width: 1,
                            color: const Color(0xFFE8E6E2),
                          ),
                        ),
                        child: Image.asset('assets/image/mascot2.png')),
                  ),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppWidget.boldText(context, name, 12, const Color(0xFF2B1710),
                      FontWeight.w500),
                  const SizedBox(
                    height: 8,
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                      left: 10,
                      right: 10,
                      top: 5,
                      bottom: 5,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                    child: AppWidget.normalText(context, team, 11, const Color(0xFF282828),
                        FontWeight.w400),
                  )
                ],
              ),
            ],
          ),
          Row(
            children: [
              InkWell(
                onTap: (){
                  if(profileCtl.token.value != null){
                    saveActivityCtl.saveActivity("ติดต่อศูนย์ซ่อมสีและตัวถัง","phone",name);
                    AppService.callPhone(phone);
                  } else {
                    AppWidget.showDialogPageSlide(context, const LoginPage());
                  }
                },
                child: Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(width: 1,color: const Color(0xFFE8E6E2))
                  ),
                  child: Image.asset('assets/image/service/phone.png'),
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              InkWell(
                onTap: (){
                  if(profileCtl.token.value != null){
                    saveActivityCtl.saveActivity("ติดต่อศูนย์ซ่อมสีและตัวถัง","line",name);
                    AppService.launchUrl("http://line.me/ti/p/~$lineId");
                  } else {
                    AppWidget.showDialogPageSlide(context, const LoginPage());
                  }
                },
                child: Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(width: 1,color: const Color(0xFFE8E6E2))
                  ),
                  child: Image.asset('assets/image/service/line.png'),
                ),
              ),
            ],
          )

        ],
      ),
    );
  }
}
