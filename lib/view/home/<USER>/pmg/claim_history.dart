import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/center_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/service_model/claim_history.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ClaimHistoryPage extends StatefulWidget {
  const ClaimHistoryPage({Key? key}) : super(key: key);

  @override
  State<ClaimHistoryPage> createState() => _ClaimHistoryPageState();
}

class _ClaimHistoryPageState extends State<ClaimHistoryPage> {
  final SecureStorage secureStorage = SecureStorage();

  List<dynamic> claimItems = [];
  ClaimHistoryList? resClaimHistory;
  int numPage = 0;

  String page = 'list';
  Map claim = {};

  final profileCtl = Get.put(ProfileController());

  bool isLoading = true;

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "ClaimHistory");
    getData();
  }

  getData() async {
    await getClaimHistory();
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return Scaffold(
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          children: [
            Container(
              width: 1.sw,
              height: 1.sh,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: const [0.2, 0.8],
                  colors: [
                    const Color(0xFFF4F4F4).withOpacity(0.85),
                    const Color(0xFFFFFFFF).withOpacity(0.85),
                  ],
                ),
              ),
            ),
            Container(
              width: 1.sw,
              height: 1.sh,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: const [0, 0.5],
                  colors: [
                    const Color(0xFF282828),
                    const Color(0xFFFFB100).withOpacity(0.20),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomLeft,
              child: Container(
                margin: EdgeInsets.only(bottom: 0.01.sh),
                child: Row(
                  children: [
                    SizedBox(
                      width: 0.05.sw,
                    ),
                    AppWidget.boldTextS(
                        context,
                        "ISUZU",
                        12.sp,
                        Colors.white,
                        FontWeight.w700),
                    AppWidget.boldTextS(
                        context,
                        " PRACHAKIJ",
                        12.sp,
                        Colors.white,
                        FontWeight.w400),
                  ],
                ),
              ),
            ),
            // Container(
            //   margin: EdgeInsets.only(
            //       bottom: 0.07.sh
            //   ),
            //   child: Align(
            //     alignment: Alignment.bottomCenter,
            //     child: Image.asset('assets/image/service/pmg/home_pmg.png',width: 0.8.sw,),
            //   ),
            // ),
            Container(
              width: 1.sw,
              height: 0.72.sh,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  // topLeft: Radius.circular(0.03.sh),
                  // topRight: Radius.circular(0.03.sh),
                  bottomLeft: Radius.circular(0.03.sh),
                  bottomRight: Radius.circular(0.03.sh),
                ),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: const [0, 0.5],
                  colors: [
                    const Color(0xFFF4F4F4).withOpacity(0.85),
                    const Color(0xFFFFFFFF).withOpacity(0.85),
                  ],
                ),
              ),
            ),
            page == "list"
            ? resClaimHistory!.data!.isEmpty 
                ? buildContent() 
                : buildShowListview()
            : buildShowDetail(context)
            // : buildShowDetail(context),
          ],
        ),
      ),
    );
  }

  buildContent(){
    return Container(
      width: 1.sw,
      height: 0.07.sh,
      // color: Colors.red,
      margin: EdgeInsets.only(
        top: 0.02.sh,
        left: 0.05.sw,
        right: 0.05.sw,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(0.03.sh),
          topRight: Radius.circular(0.03.sh),
          bottomLeft: Radius.circular(0.03.sh),
          bottomRight: Radius.circular(0.03.sh),
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0, 0.5],
          colors: [
            const Color(0xFFFFFFFF).withOpacity(0.9),
            const Color(0xFFFFFFFF),
          ],
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0xFFD0D0D0),
            blurRadius: 0.1,
          ),
        ],
      ),
      child: Align(
        alignment: Alignment.center,
        child: AppWidget.boldTextS(
            context,
            "ไม่พบประวัติการเคลม",
            20.sp,
            Colors.black,
            FontWeight.w400),
      ),
    );
  }

  buildShowListview(){
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(
            left: 0.05.sw,
            right: 0.05.sw
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppWidget.boldTextS(
                  context,
                  "รายการแจ้งเคลม",
                  20.sp,
                  Colors.black,
                  FontWeight.w400),
              Row(
                children: [
                  AppWidget.boldTextS(
                      context,
                      resClaimHistory!.data!.length.toString(),
                      20.sp,
                      const Color(0xFFD09000),
                      FontWeight.w400),
                  AppWidget.boldTextS(
                      context,
                      " รายการ",
                      20.sp,
                      Colors.black,
                      FontWeight.w400),
                ],
              )
            ],
          ),
        ),
        SizedBox(
          width: 1.sw,
          height: 0.65.sh,
          child: buildListview(context),
        )
      ],
    );
  }

  buildListview(context){
    return ListView.builder(
      padding: EdgeInsets.zero,
        itemCount: resClaimHistory!.data!.length,
        itemBuilder: (context, index) =>
            InkWell(
                onTap: (){
                  setState(() {
                    page = 'detail';
                    // claim = item;
                    claim = {
                      "date" : resClaimHistory!.data![index].createTime.toString(),
                      "job" : resClaimHistory!.data![index].jobNote,
                      "car_reg" : resClaimHistory!.data![index].carReg,
                      "job_id" : resClaimHistory!.data![index].jobId,
                      "appraisers_Name" : resClaimHistory!.data![index].appraisersName,
                      "status" : resClaimHistory!.data![index].appStatus,
                      "isr_code" : resClaimHistory!.data![index].isrCode,
                    };
                  });
                },
                child: Container(
                    width: 1.sw,
                    height: 0.1.sh,
                    // color: Colors.red,
                    margin: EdgeInsets.only(
                      top: 0.02.sh,
                      left: 0.05.sw,
                      right: 0.05.sw,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(0.03.sh),
                        topRight: Radius.circular(0.03.sh),
                        bottomLeft: Radius.circular(0.03.sh),
                        bottomRight: Radius.circular(0.03.sh),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0, 0.5],
                        colors: [
                          const Color(0xFFFFFFFF).withOpacity(0.9),
                          const Color(0xFFFFFFFF),
                        ],
                      ),
                      boxShadow: const [
                        BoxShadow(
                          color: Color(0xFFD0D0D0),
                          blurRadius: 0.1,
                        ),
                      ],
                    ),
                    child: Container(
                      // color: Colors.red,
                      margin: EdgeInsets.only(
                          left: 0.05.sw,
                          right: 0.05.sw
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            alignment: Alignment.centerLeft,
                            child: AppWidget.normalText(
                                context,
                                AppService.dateThaiDate(resClaimHistory!.data![index].createTime.toString()),
                                16.sp,
                                const Color(0xFFD09000),
                                FontWeight.w400),
                          ),
                          Row(
                            children: [
                              SvgPicture.string(
                                '<svg viewBox="113.0 467.0 42.0 47.0" ><defs><linearGradient id="gradient" x1="0.5" y1="0.0" x2="0.5" y2="1.0"><stop offset="0.0" stop-color="#80000000" stop-opacity="0.5" /><stop offset="1.0" stop-color="#ff141322"  /></linearGradient></defs><path transform="translate(108.5, 465.5)" d="M 37.16666412353516 22.64999961853027 L 13.83333301544189 22.64999961853027 L 13.83333301544189 27.35000038146973 L 37.16666412353516 27.35000038146973 L 37.16666412353516 22.64999961853027 Z M 41.83333206176758 6.200000286102295 L 39.5 6.200000286102295 L 39.5 1.50000011920929 L 34.83333206176758 1.50000011920929 L 34.83333206176758 6.200000286102295 L 16.16666793823242 6.200000286102295 L 16.16666793823242 1.50000011920929 L 11.5 1.50000011920929 L 11.5 6.200000286102295 L 9.166666030883789 6.200000286102295 C 6.576666355133057 6.200000286102295 4.523333549499512 8.314999580383301 4.523333549499512 10.89999961853027 L 4.5 43.80000305175781 C 4.5 46.38500213623047 6.576666355133057 48.50000381469727 9.166666030883789 48.50000381469727 L 41.83333206176758 48.50000381469727 C 44.39999771118164 48.50000381469727 46.49999618530273 46.38500213623047 46.49999618530273 43.80000305175781 L 46.49999618530273 10.89999961853027 C 46.49999618530273 8.314999580383301 44.39999771118164 6.200000286102295 41.83333206176758 6.200000286102295 Z M 41.83333206176758 43.80000305175781 L 9.166666030883789 43.80000305175781 L 9.166666030883789 17.95000076293945 L 41.83333206176758 17.95000076293945 L 41.83333206176758 43.80000305175781 Z M 30.16666603088379 32.05000305175781 L 13.83333301544189 32.05000305175781 L 13.83333301544189 36.75000381469727 L 30.16666603088379 36.75000381469727 L 30.16666603088379 32.05000305175781 Z" fill="url(#gradient)" stroke="none" stroke-width="1" stroke-miterlimit="4" stroke-linecap="butt" /></svg>',
                                allowDrawingOutsideViewBox: true,
                                fit: BoxFit.fill,
                                width: 0.05.sw,
                              ),
                              SizedBox(
                                width: 0.75.sw,
                                // color: Colors.red,
                                child: Text(
                                  " เคลมซ่อม : ${resClaimHistory!.data![index].jobNote.toString()}",
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: const Color(0xFF000000),
                                    fontFamily: 'Prompt',
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16.sp,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    )
                )
            ),
    );
  }

  buildShowDetail(context){
    return Container(
      margin: EdgeInsets.only(
        left: 0.05.sw,
        right: 0.05.sw
      ),
      child: Column(
        children: [
          AppWidget.boldTextS(
              context,
              "รายละเอียดข้อมูลงานเคลม",
              20.sp,
              Colors.black,
              FontWeight.w400),
          SizedBox(height: 0.05.sh,),
          Container(
            width: 1.sw,
            padding: EdgeInsets.only(
              bottom: 0.02.sh
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(0.03.sh),
                topRight: Radius.circular(0.03.sh),
                bottomLeft: Radius.circular(0.03.sh),
                bottomRight: Radius.circular(0.03.sh),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0, 0.5],
                colors: [
                  const Color(0xFFFFFFFF).withOpacity(0.9),
                  const Color(0xFFFFFFFF),
                ],
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0xFFD0D0D0),
                  blurRadius: 0.1,
                ),
              ],
            ),
            child: Container(
              margin: EdgeInsets.only(
                left: 0.05.sw,
                right: 0.05.sw,
                top: 0.02.sh,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppWidget.boldTextS(
                      context,
                      AppService.dateThaiDate(claim["date"]),
                      16.sp,
                      const Color(0xFFD09000),
                      FontWeight.w400),
                  SizedBox(
                    height: 0.01.sh,
                  ),
                  AppWidget.boldTextS(
                      context,
                      'เลขทะเบียนรถ : ${claim["car_reg"]}',
                      16.sp,
                      const Color(0xFF000000),
                      FontWeight.w400),
                  Row(
                    children: [
                      AppWidget.boldTextS(
                          context,
                          "เลขใบสั่งเคลม : ",
                          16.sp,
                          const Color(0xFF000000),
                          FontWeight.w400),
                      AppWidget.boldTextS(
                          context,
                          claim["job_id"],
                          16.sp,
                          const Color(0xFFD09000),
                          FontWeight.w400),
                    ],
                  ),
                  Row(
                    children: [
                      AppWidget.boldTextS(
                          context,
                          "ที่ปรึกษา : ",
                          16.sp,
                          const Color(0xFF000000),
                          FontWeight.w400),
                      AppWidget.boldTextS(
                          context,
                          claim["appraisers_Name"],
                          16.sp,
                          const Color(0xFFD09000),
                          FontWeight.w400),
                    ],
                  ),
                  SizedBox(
                    height: 0.01.sh,
                  ),
                  Container(
                    alignment: Alignment.topLeft,
                    child: RichText(
                      text: TextSpan(
                        text: 'รายการแจ้งเคลม : ',
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontFamily: 'Prompt-Medium',
                          color: Colors.red,
                          letterSpacing: 0.2,
                          fontWeight: FontWeight.w400,
                          shadows: <Shadow>[
                            Shadow(
                              offset: const Offset(0, 1),
                              blurRadius: 5.0,
                              color: const Color(0xFF000000).withOpacity(0.1),
                            ),
                          ],
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text: '${claim['job']}',
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 15.sp,
                              color: Colors.black,
                              letterSpacing: 0.2,
                              fontWeight: FontWeight.w400,
                              shadows: <Shadow>[
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: 5.0,
                                  color: const Color(0xFF000000).withOpacity(0.1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 0.01.sh,
                  ),
                  Row(
                    children: [
                      AppWidget.boldTextS(
                          context,
                          "สถานะงานเคลม : ",
                          16.sp,
                          const Color(0xFF000000),
                          FontWeight.w400),
                      AppWidget.boldTextS(
                          context,
                          claim["status"],
                          16.sp,
                          const Color(0xFFD09000),
                          FontWeight.w400),
                    ],
                  ),
                  Row(
                    children: [
                      AppWidget.boldTextS(
                          context,
                          "การชำระเงิน : ",
                          16.sp,
                          const Color(0xFF000000),
                          FontWeight.w400),
                      AppWidget.boldTextS(
                          context,
                          claim["isr_code"] != ""
                              ? "เคลมประกัน"
                              : "จ่ายชำระเอง",
                          16.sp,
                          const Color(0xFFD09000),
                          FontWeight.w400),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 0.05.sh,
          ),
          Container(
            margin: EdgeInsets.only(
                left: 0.02.sw,
                right: 0.02.sw
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: (){
                   setState(() {
                     page = 'list';
                   });
                  },
                  child: AppWidget.boldTextS(
                      context,
                      "ย้อนกลับ",
                      16.sp,
                      const Color(0xFF000000),
                      FontWeight.w400),
                ),
                Container()
              ],
            ),
          )
        ],
      ),
    );
  }

  getClaimHistory() async {
    resClaimHistory = await CenterController.getClaimHistory(context, profileCtl.profile.value.mobile);
    setState(() {
      numPage = resClaimHistory!.data!.length;
    });
  }

}
