import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/paymentTime_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_Order_controller.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

class PaymentState extends StatefulWidget {
  const PaymentState({super.key});

  @override
  State<PaymentState> createState() => _PaymentStateState();
}

class _PaymentStateState extends State<PaymentState> {

  late FToast fToast;
  final sparepartOrderCtl = Get.find<SparepartOrderController>();
  final webviewCtl = Get.find<WebViewLikePointController>();
  var formattedPrice = NumberFormat.currency(locale: 'en_US', symbol: '',decimalDigits: 0);


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fToast = FToast();
    fToast.init(context);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();

  }

  _showToast() {
    fToast.showToast(
      child: Container(
          width: MediaQuery.of(context).size.width * 0.6,
          height: 70.0,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            color: const Color(0xBB1A1818),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset("assets/icon/sparePart/check_ring_round.svg"),
              const SizedBox(
                width: 10,
              ),
              const Text(
                "บันทึก QR code แล้ว",
                style: TextStyle(
                  fontSize: 16.0,
                  color: Colors.white,
                ),
              ),
            ],
          )),
      gravity: ToastGravity.CENTER,
      toastDuration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GetBuilder<PaymentTimer>(
        builder: (countTime) {
          try{
            return Container(
              color: const Color(0x66000000),
              alignment: Alignment.center,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.95,
                height: MediaQuery.of(context).size.height * 0.85,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: countTime.isLonding.value ?
                const Center(
                  child: CircularProgressIndicator(color: Colors.orange),
                ) : Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 8,
                    ),
                    const Text(
                      "QR Code สำหรับการชำระเงิน",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const Text(
                      "บันทึกคิวอาร์โค้ดนี้ เพื่อนำไปใช้สำหรับโอนชำระเงิน",
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF895F00),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const Divider(
                      color: Color(0xFFBCBCBC),
                      thickness: 1,
                    ),
                    Container(
                        width: MediaQuery.of(context).size.width * 0.8,
                        color: Colors.white,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text("ยอดชำระเงินทั้งหมด",
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF2B1710),
                                  ),
                                ),
                                Row(
                                  children: [
                                    Text(
                                      "${formattedPrice.format(countTime.totalPay)}",
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF895F00),
                                      ),
                                    ),
                                    const Text(
                                      " บาท",
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF2B1710),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            Container(
                              height: MediaQuery.of(context).size.width * 0.8,
                              decoration: BoxDecoration(
                                // color: Colors.brown,
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: PrettyQrView.data(
                                data: '${sparepartOrderCtl.listOrder[countTime.updateIndexShow]["qr_data"]}',
                                decoration: const PrettyQrDecoration(
                                  shape: PrettyQrSmoothSymbol(
                                    roundFactor: 0.3,
                                  ),
                                  image: PrettyQrDecorationImage(
                                    image: AssetImage('assets/icon/icon.png'),
                                  ),
                                ),
                              ),
                              // child: QrImageView(
                              //   data: '${countTime.qrData}',
                              //   version: QrVersions.auto,
                              //   size: MediaQuery.of(context).size.width * 0.8,
                              //   embeddedImage: AssetImage('assets/icon/icon.png'),
                              //   embeddedImageStyle: QrEmbeddedImageStyle(
                              //     size: Size(40, 40),
                              //   ),
                              // ),
                            )
                          ],
                        )
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const Text(
                      "บริษัท ประชากิจมอเตอร์เซลส์ จำกัด",
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF895F00),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const Divider(
                      color: Color(0xFFBCBCBC),
                      thickness: 1,
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("กรุณาชำระภายใน",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                              Text(
                                (int.parse(sparepartOrderCtl.listOrder[countTime.updateIndexShow]["qr_code_expire"] ?? "0")  - countTime.start) < 0 ? '00:00':'${((int.parse(sparepartOrderCtl.listOrder[countTime.updateIndexShow]["qr_code_expire"] ?? 0) - countTime.start) ~/ 60000).toString().padLeft(2, '0')}:${(((int.parse(sparepartOrderCtl.listOrder[countTime.updateIndexShow]["qr_code_expire"] ?? '0') - countTime.start) % 60000) ~/ 1000).toString().padLeft(2, '0')}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFFEB2227),
                                ),
                              ),
                              Text("หมดเวลา ${DateFormat('dd/MM/yyyy HH:mm').format(
                                  DateTime.fromMillisecondsSinceEpoch((int.parse(sparepartOrderCtl.listOrder[countTime.updateIndexShow]["qr_code_expire"])))
                              )}",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              InkWell(
                                onTap: () async {
                                  print(countTime.qrData);
                                  await countTime.saveByteDataAsPng(countTime.qrData);
                                  _showToast();
                                },
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  decoration: const BoxDecoration(
                                    color: Color(0xFFE8E6E2),
                                    shape: BoxShape.circle,
                                  ),
                                  alignment: Alignment.center,
                                  child: Container(
                                    width: 30,
                                    height: 30,
                                    child: SvgPicture.asset(
                                      'assets/icon/sparePart/download_icon.svg',
                                      width: 30,
                                      height: 30,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Text("บันทึก")
                            ],
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    const Divider(
                      color: Color(0xFFBCBCBC),
                      thickness: 1,
                    ),
                    Expanded(
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.8,
                        child: const SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "ขั้นตอนการชำระเงิน",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Text(
                                "- คลิกปุ่มบันทึกหรือแคปหน้าจอ เพื่อบันทึกคิวอาร์โค้ดนี้\n- เปิดแอปพลิเคชันธนาคารบนอุปกรณ์ของท่าน\n- ชำระเงินจากเมนู สแกนจ่าย ผ่าน QR code\n- หลังจากชำระเงินเสร็จสิ้น กรุณากลับไปตรวจสอบ สถานะการชำระเงินในแอพประชากิจฯ อีกครั้ง หากสถานะยังไม่อัปเดต กรุณาติดต่อทีมงาน",
                                maxLines: 6,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF707070),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }catch(e){
            Navigator.pop(context);
            return const Center(
              child: CircularProgressIndicator(color: Colors.orange),
            );
          }
        }
      ),
    );
  }
}
