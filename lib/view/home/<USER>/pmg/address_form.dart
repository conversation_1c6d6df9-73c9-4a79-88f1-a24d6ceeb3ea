import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/appointment_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/editAddress_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';

class AddressForm extends StatefulWidget {
  const AddressForm({super.key});

  @override
  State<AddressForm> createState() => _AddressFormState();
}

class _AddressFormState extends State<AddressForm> {

  // var appointmentCtl = Get.put(AppointmentController());
  var editAddress = Get.put(EditAddressController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: GetBuilder<ProvinceController>(
          init: ProvinceController(),
          builder: (provinceCtrl) {
          return GetBuilder<EditAddressController>(
            builder: (editAddress) {
              return Scaffold(
                floatingActionButton: InkWell(
                  onTap: () {
                    provinceCtrl.switcher(editAddress.addressController.text, editAddress.nameController.text);
                    Get.back();
                  },
                  child: Container(
                    margin: EdgeInsets.all(10),
                    color: Colors.white,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.9,
                      height: 50,
                      // margin: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        gradient: const LinearGradient(
                          begin: Alignment(0.00, -1.00),
                          end: Alignment(0, 1),
                          colors: [Color(0xB2000000), Color(0xFF000000)],
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        "บันทึก",
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFFFFB100),
                        ),
                      ),
                    ),
                  ),
                ),
                body: ColorfulSafeArea(
                  color: Colors.black,
                  child: Stack(
                    children: [
                      Container(
                        height: MediaQuery.of(context).size.height,
                        width: MediaQuery.of(context).size.width,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment(0.00, -1.00),
                            end: Alignment(0, 1),
                            colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          _buildAppBar(),
                          Expanded(
                              child: SingleChildScrollView(
                                  child: _buildForm(),
                              ),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              );
            }
          );
        }
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.15,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border:
                    Border.all(width: 1, color: const Color(0x88BCBCBC))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          Container(
            child: const Text(
              "ที่อยู่ใหม่",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2B1710),
              ),
            ),
          ),
          Container(
              width: MediaQuery.of(context).size.width * 0.15,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              child: Container(
                width: 36,
                height: 36,
              ))
        ],
      ),
    );
  }

  Widget _buildForm(){
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height * 0.8,
      margin: EdgeInsets.only(
        top: 20,
        left: MediaQuery.of(context).size.width * 0.05,
        right: MediaQuery.of(context).size.width * 0.05,
      ),
      child: Column( 
        children: [
          _buildInputField("ชื่อ นามสกุล", editAddress.nameController),
          _buildInputField("บ้านเลขที่ / หมู่ / ถนน", editAddress.addressController),
          _buildInputDropdown("จังหวัด"),
          _buildInputDropdown("อำเภอ"),
          _buildInputDropdown("ตำบล"),
          _buildInputField("รหัสไปรษณีย์", editAddress.zipcodeController),
        ],
      ),
    );
  }

  Widget _buildInputField(String title, TextEditingController ctrl){
    return Container(
      width: MediaQuery.of(context).size.width * 0.9,
      margin: EdgeInsets.only(
        top: 10,
        left: 10,
        right: 10,
      ),
      child: Column(
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 20,
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF707070),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 30,
            child: TextField(
              controller: ctrl,
              decoration: InputDecoration(
                hintText: title,
                hintStyle: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFFBCBCBC),
                ),
                contentPadding: EdgeInsets.only(
                  bottom: 15,
                ),
                // focusColor: const Color(0xFFFFB100),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Color(0xFFBCBCBC),
                  ),
                ),
                focusedBorder: const UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Color(0xFFFFB100),
                  ),
                ),
                // hoverColor: const Color(0xFFFFB100),
              ),
              cursorColor: const Color(0xFFFFB100),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputDropdown(String title){
    return GetBuilder<ProvinceController>(
        builder: (provinceCtrl) {
        return Container(
          width: MediaQuery.of(context).size.width * 0.9,
          margin: EdgeInsets.only(
            top: 10,
            left: 10,
            right: 10,
          ),
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: 20,
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF707070),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(
                height: 10,
              ),
              Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: 30,
                  child: title == "จังหวัด" ? dropdown(context, provinceCtrl.listProvinces, provinceCtrl.provinceSelect, "กรอก${title}", "province")
                      : title == "อำเภอ" ? dropdown(context, provinceCtrl.listAmphurs, provinceCtrl.amphurSelect, "กรอก${title}", "amphur")
                      : dropdown(context, provinceCtrl.listTumbols, provinceCtrl.tumbolSelect, "กรอก${title}", "tumbol"),
              ),
            ],
          ),
        );
      }
    );
  }

  dropdown(context, items, selected, showText, type) {
    return GetBuilder<ProvinceController>(
        builder: (provinceCtrl) {
        return GestureDetector(
          onTap: () {
            // button.value = true;
            showCupertinoModalPopup(
              context: context,
              builder: (context) {
                int selectTempIndex;
                if (selected != "") {
                  selectTempIndex = items.indexOf(selected);
                } else {
                  selectTempIndex = 0;
                }
                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    Container(
                      decoration: const BoxDecoration(
                        color: Color(0xffffffff),
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xff999999),
                            width: 0.0,
                          ),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          CupertinoButton(
                            onPressed: () {
                              // button.value = false;
                              Get.back();
                              FocusScope.of(context).unfocus();
                            },
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            child: Text(
                              'ยกเลิก',
                              style: TextStyle(
                                fontFamily: 'Prompt',
                                color: const Color(0xFF282828),
                                fontSize:  Get.width < 500 ? 14 : 16,
                              ),
                            ),
                          ),
                          CupertinoButton(
                            onPressed: () {},
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            child: Text(
                              'เลือกรายการ',
                              style: TextStyle(
                                fontFamily: 'Prompt-Medium',
                                color: const Color(0xFF282828),
                                fontSize:  Get.width < 500 ? 14 : 16,
                              ),
                            ),
                          ),
                          CupertinoButton(
                            onPressed: () async {
                              if (type == "province") {
                                await provinceCtrl.keepSelected(type, items[selectTempIndex]);
                              } else if (type == "amphur") {
                                await provinceCtrl.keepSelected(type, items[selectTempIndex]);
                              } else {
                                await provinceCtrl.keepSelected(type, items[selectTempIndex]);
                              }
                              // button.value = false;
                              editAddress.zipcodeController.text = provinceCtrl.zipcodeSelect;
                              setState(() {});

                              Get.back();
                              FocusScope.of(context).unfocus();
                            },
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 10,
                            ),
                            child: Text(
                              'ตกลง',
                              style: TextStyle(
                                fontFamily: 'Prompt-Medium',
                                color: const Color(0xFF282828),
                                fontSize:  Get.width < 500 ? 14 : 16,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    Container(
                      height: 300,
                      color: CupertinoColors.white,
                      child: CupertinoPicker(
                        scrollController: FixedExtentScrollController(
                          initialItem: selectTempIndex,
                        ),
                        itemExtent: 50,
                        useMagnifier: true,
                        backgroundColor: CupertinoColors.white,
                        onSelectedItemChanged: (index) {
                          setState(() {
                            selectTempIndex = index;
                          });
                        },
                        children: List.generate(items.length, (index) {
                          return Center(
                            child: Text(
                              items[index],
                              style: TextStyle(
                                fontFamily: 'Prompt',
                                fontSize:  Get.width < 500 ? 14 : 16,
                                color: const Color(0xFF282828),
                              ),
                            ),
                          );
                        }),
                      ),
                    )
                  ],
                );
              },
            ).then((value) {
              // button.value = false;
            });
          },
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 20,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: const Color(0xFFBCBCBC),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  selected == "" ? showText : selected,
                  style: TextStyle(
                    fontFamily: 'Prompt',
                    fontSize:  Get.width < 500 ? 14 : 16,
                    color: selected == "" ? const Color(0xFFBCBCBC) : const Color(0xFF282828),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                // AppWidget.normalText(
                //     context,
                //     provinceCtrl.provinceSelect.value == "" ? showText : provinceCtrl.provinceSelect.value,
                //     Get.width < 500 ? 14 : 16,
                //     provinceCtrl.provinceSelect.value == "" ? const Color(0xFFBCBCBC) : const Color(0xFF282828),
                //     FontWeight.w400),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 18,
                  color: Color(0xFFBCBCBC),
                ),
              ],
            ),
          ),
        );
      }
    );
  }

}
