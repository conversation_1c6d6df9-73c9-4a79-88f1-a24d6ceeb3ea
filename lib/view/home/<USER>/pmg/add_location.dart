import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/editAddress_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/address_form.dart';

class AddLocation extends StatefulWidget {
  const AddLocation({super.key});

  @override
  State<AddLocation> createState() => _AddLocationState();
}

class _AddLocationState extends State<AddLocation> {

  ProvinceController provinceCtrl = Get.find<ProvinceController>();
  EditAddressController editAddressCtrl = Get.put(EditAddressController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: InkWell(
        onTap: ()  {
          provinceCtrl.setDefultAddress();
          Get.back();
        },
        child: Container(
          margin: EdgeInsets.all(10),
          color: Colors.white,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: 50,
            // margin: EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              gradient: const LinearGradient(
                begin: Alignment(0.00, -1.00),
                end: Alignment(0, 1),
                colors: [Color(0xB2000000), Color(0xFF000000)],
              ),
            ),
            alignment: Alignment.center,
            child: Text(
              "บันทึก",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFFFFB100),
              ),
            ),
          ),
        ),
      ),
      body: ColorfulSafeArea(
        color: Colors.black,
        child: Stack(
          children: [
            Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(0.00, -1.00),
                  end: Alignment(0, 1),
                  colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                ),
              ),
            ),
            Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            provinceCtrl.changeAddressID(-1);
                            provinceCtrl.changeCheckInsert(false);
                            Get.to(() => const AddressForm());
                          },
                          child: Container(
                            width: MediaQuery.of(context).size.width * 0.9,
                            height: MediaQuery.of(context).size.height * 0.07,
                            decoration: BoxDecoration(
                              color: Color(0x0D282828),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset("assets/icon/sparePart/circle_add.svg"),
                                SizedBox(
                                  width: 10,
                                ),
                                const Text(
                                  "เพิ่มที่อยู่",
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFF282828),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 20,
                        ),
                        _buildListLocation(),
                      ],
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.15,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border:
                    Border.all(width: 1, color: const Color(0x88BCBCBC))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          Container(
            child: const Text(
              "เลือกที่อยู่",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF2B1710),
              ),
            ),
          ),
          Container(
              width: MediaQuery.of(context).size.width * 0.15,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              child: Container(
                width: 36,
                height: 36,
              ))
        ],
      ),
    );
  }

  Widget _buildListLocation() {
    return GetBuilder<ProvinceController>(
        builder: (provinceCtrl) {
          try{
            return Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                  margin: EdgeInsets.only(
                    top: 20,
                    bottom: 20,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            width: MediaQuery.of(context).size.width * 0.1,
                            alignment: Alignment.topCenter,
                            child: SizedBox(
                              height: 20,
                              width: 20,
                              child: Checkbox(
                                  value: provinceCtrl.useLocation,
                                  // splashRadius: 1,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  side: const BorderSide(
                                    color: Color(0xFFBCBCBC),
                                    width: 2,
                                    strokeAlign: 1.0,
                                  ),
                                  activeColor: const Color(0xFFFFB100),
                                  onChanged: (value){
                                    provinceCtrl.checked(-1, false );
                                  }),
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              provinceCtrl.listAddress.data!.every((item) => item.type_default == 0) ? Container(
                                width: 70,
                                height: 20,
                                decoration: BoxDecoration(
                                  color: const Color(0x33FFB100),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  "ค่าเริ่มต้น",
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFF895F00),
                                  ),
                                ),
                              ) : Container(),
                              const Text(
                                "รับสินค้าที่อีซูซุประชากิจฯ",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF282828),
                                ),
                              ),
                            ],
                          )

                        ],
                      ),
                      Column(
                        children: List.generate(provinceCtrl.listAddress.data!.length, (index) {

                          return Column(
                            children: [
                              Container(
                                width: MediaQuery.of(context).size.width,
                                height: 20,
                                margin: const EdgeInsets.only(
                                  left: 10,
                                  right: 10,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: List.generate(
                                      (MediaQuery.of(context).size.width / 8).floor(),
                                          (index) {
                                        return const Text(
                                          "-",
                                          style: TextStyle(
                                              fontSize: 12, color: Color(0xFFBCBCBC)),
                                        );
                                      }),
                                ),
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: MediaQuery.of(context).size.width * 0.1,
                                        alignment: Alignment.topCenter,
                                        child: SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: Checkbox(
                                              value: provinceCtrl.isSelected[index],
                                              // splashRadius: 1,
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(5),
                                              ),
                                              side: const BorderSide(
                                                color: Color(0xFFBCBCBC),
                                                width: 2,
                                                strokeAlign: 1.0,
                                              ),
                                              activeColor: const Color(0xFFFFB100),
                                              onChanged: (value){
                                                print("click");
                                                provinceCtrl.checked(index, value!);
                                              }),
                                        ),
                                      ),
                                      Container(
                                        width: MediaQuery.of(context).size.width * 0.5,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            provinceCtrl.listAddress.data![index].type_default == 1 ? Container(
                                              width: 70,
                                              height: 20,
                                              decoration: BoxDecoration(
                                                color: const Color(0x33FFB100),
                                                borderRadius: BorderRadius.circular(5),
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                "ค่าเริ่มต้น",
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  color: Color(0xFF895F00),
                                                ),
                                              ),
                                            ) : Container(),
                                            Text(
                                              "${provinceCtrl.listAddress.data![index].fullname}",
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Color(0xFF282828),
                                              ),
                                            ),
                                            Text(
                                              "${provinceCtrl.listAddress.data![index].address_line} ${provinceCtrl.listAddress.data![index].sub_district} ${provinceCtrl.listAddress.data![index].district} ${provinceCtrl.listAddress.data![index].province} ${provinceCtrl.listAddress.data![index].postal_code}",
                                              maxLines: 2,
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Color(0xFF707070),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  InkWell(
                                    onTap: () {
                                      provinceCtrl.preInputForEdit(provinceCtrl.listAddress.data![index].province, provinceCtrl.listAddress.data![index].district, provinceCtrl.listAddress.data![index].sub_district, provinceCtrl.listAddress.data![index].postal_code);
                                      editAddressCtrl.preInputForEdit(provinceCtrl.listAddress.data![index].fullname, provinceCtrl.listAddress.data![index].address_line, provinceCtrl.listAddress.data![index].postal_code);
                                      provinceCtrl.changeAddressID(provinceCtrl.listAddress.data![index].address_id ?? -1);
                                      provinceCtrl.changeCheckInsert(true);
                                      Get.to(() => const AddressForm());

                                    },
                                    child: Container(
                                      width: MediaQuery.of(context).size.width * 0.2,
                                      height: 40,
                                      alignment: Alignment.topCenter,
                                      child: Text("แก้ไข",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFF895F00),
                                          )),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );

                        }),
                      ),
                    ],
                  )
              ),
            );
          }catch(e){
            return Center(
              child: CircularProgressIndicator(
                color: Colors.orange,
              ), // Loading spinner
            );
          }
      }
    );
  }
}
