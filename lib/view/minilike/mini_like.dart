import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class MiniLikePage extends StatefulWidget {
  const MiniLikePage({Key? key}) : super(key: key);

  @override
  State<MiniLikePage> createState() => _MiniLikePageState();
}

class _MiniLikePageState extends State<MiniLikePage> {
  String lang = 'th';
  bool? _isOpen = false;
  final profileCtl = Get.put(ProfileController());
  Map dataForMiniLikeWallet = {};
  bool canOpen = false;

  var _currIndex = 0;

  @override
  void initState() {
    super.initState();
    settingDataLikePoint();
  }

  settingDataLikePoint() async {
    var phone = await profileCtl.profile.value.mobile;
    var firstname = await profileCtl.profile.value.firstname;
    var lastname = await profileCtl.profile.value.lastname;
    if (phone != null && firstname != null && lastname != null) {
      dataForMiniLikeWallet = {
        "phoneNumber":  AppService.phoneToPhoneCode(phone),
        "firstname": firstname,
        "lastname": lastname,
        "application": "PMS"
      };
      canOpen = true;
      _isOpen = true;
    } else {
      canOpen = false;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (canOpen == false) {
      return const Center(
        child: Text('ไม่สามารถเปิดได้'),
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          children: [
            ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
                  child: Container(
                    width: 1.sw,
                    height: 1.sh,
                    padding: EdgeInsets.zero,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0, 0.01, 0.5283, 1.0],
                        colors: [
                          Color(0xFF1D2532),
                          Color(0xFF1A212C),
                          Color(0xFF1D2532),
                          Color(0xFF151C26),
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 0.07.sh,
                        ),
                        Container(
                          margin: EdgeInsets.only(
                              left: 0.05.sw
                          ),
                          width: 45.w,
                          height: 45.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(25)),
                            border: Border.all(
                                width: 1,
                                color: Colors.white.withOpacity(0.3)
                            ),
                            color: Colors.black,
                          ),
                          child: IconButton(
                            icon: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 500),
                                transitionBuilder: (child, anim) => RotationTransition(
                                  turns:
                                  Tween<double>(begin: 1, end: 2).animate(anim),
                                  child: FadeTransition(opacity: anim, child: child),
                                ),
                                child: _currIndex == 0
                                    ? const Icon(CupertinoIcons.arrow_2_circlepath,color: Colors.white, key: ValueKey('icon1'))
                                    : const Icon(
                                  CupertinoIcons.arrow_2_circlepath,
                                  color: Colors.white,
                                  key: ValueKey('icon2'),
                                )),
                            onPressed: () {
                              setState(() {
                                _currIndex = _currIndex == 0 ? 1 : 0;
                                _isOpen = true;
                              });
                            },
                          ),
                        ),
                        SizedBox(
                          height: 0.3.sh,
                        ),
                        Center(child: Image.asset('assets/image/minilike/logo.png', width: 120.w)),
                        SizedBox(
                          height: 0.37.sh,
                        ),
                        InkWell(
                          onTap: (){
                            Get.back();
                          },
                          child: Center(
                            child: Container(
                              width: 317.w,
                              height: 50.h,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(Radius.circular(37)),
                                border: Border.all(
                                    width: 1,
                                    color: Colors.white.withOpacity(0.3)
                                ),
                                color: const Color(0xFF1D2532),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    spreadRadius: 0,
                                    blurRadius: 10,
                                    offset: const Offset(0, 4), // changes position of shadow
                                  ),
                                ],
                              ),
                              child: AppWidget.normalText(
                                context,
                                "ปิดหน้านี้",
                                14.sp,
                                const Color(0xFFE8E6E2),
                                FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
            ),
          ],
        ),
      ),
    )
    ;
    // return Scaffold(
    //   backgroundColor: Colors.transparent,
    //   body: InkWell(
    //     onTap: () {
    //       FocusScopeNode currentFocus = FocusScope.of(context);
    //       if (!currentFocus.hasPrimaryFocus) {
    //         currentFocus.unfocus();
    //       }
    //     },
    //     child: Stack(
    //       children: [
    //         ClipRRect(
    //             child: BackdropFilter(
    //               filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
    //               child: Container(
    //                 width: 1.sw,
    //                 height: 1.sh,
    //                 padding: EdgeInsets.zero,
    //                 decoration: const BoxDecoration(
    //                   gradient: LinearGradient(
    //                     begin: Alignment.topCenter,
    //                     end: Alignment.bottomCenter,
    //                     stops: [0, 0.01, 0.5283, 1.0],
    //                     colors: [
    //                       Color(0xFF1D2532),
    //                       Color(0xFF1A212C),
    //                       Color(0xFF1D2532),
    //                       Color(0xFF151C26),
    //                     ],
    //                   ),
    //                 ),
    //                 child: Column(
    //                   crossAxisAlignment: CrossAxisAlignment.start,
    //                   children: [
    //                     SizedBox(
    //                       height: 0.07.sh,
    //                     ),
    //                     InkWell(
    //                       onTap: (){
    //                         // setState(() {
    //                         //   _isOpen = true;
    //                         // });
    //                       },
    //                       child: Container(
    //                         margin: EdgeInsets.only(
    //                             left: 0.05.sw
    //                         ),
    //                         width: 45.w,
    //                         height: 45.h,
    //                         alignment: Alignment.center,
    //                         decoration: BoxDecoration(
    //                           borderRadius: const BorderRadius.all(Radius.circular(25)),
    //                           border: Border.all(
    //                               width: 1,
    //                               color: Colors.white.withOpacity(0.3)
    //                           ),
    //                           color: Colors.black,
    //                         ),
    //                         // child: const Icon(
    //                         //   CupertinoIcons.arrow_2_circlepath,
    //                         //   color: Color(0xFFBCBCBC),
    //                         // ),
    //                         child: IconButton(
    //                           icon: AnimatedSwitcher(
    //                               duration: const Duration(milliseconds: 300),
    //                               transitionBuilder: (child, anim) => RotationTransition(
    //                                 turns: child.key == ValueKey('icon1')
    //                                     ? Tween<double>(begin: 1, end: 1).animate(anim)
    //                                     : Tween<double>(begin: 0.75, end: 1).animate(anim),
    //                                 child: FadeTransition(opacity: anim, child: child),
    //                               ),
    //                               child: _currIndex == 0
    //                                   ? Icon(CupertinoIcons.arrow_2_circlepath, key: const ValueKey('icon1'))
    //                                   : Icon(
    //                                 CupertinoIcons.arrow_2_circlepath,
    //                                 key: const ValueKey('icon2'),
    //                               )),
    //                           onPressed: () {
    //                             setState(() {
    //                               _currIndex = _currIndex == 0 ? 1 : 0;
    //                             });
    //                           },
    //                         ),
    //                       ),
    //                     ),
    //                     SizedBox(
    //                       height: 0.3.sh,
    //                     ),
    //                     Center(child: Image.asset('assets/image/minilike/logo.png', width: 120.w)),
    //                     SizedBox(
    //                       height: 0.37.sh,
    //                     ),
    //                     InkWell(
    //                       onTap: (){
    //                         Get.back();
    //                       },
    //                       child: Center(
    //                         child: Container(
    //                           width: 317.w,
    //                           height: 50.h,
    //                           alignment: Alignment.center,
    //                           decoration: BoxDecoration(
    //                             borderRadius: const BorderRadius.all(Radius.circular(37)),
    //                             border: Border.all(
    //                                 width: 1,
    //                                 color: Colors.white.withOpacity(0.3)
    //                             ),
    //                             color: const Color(0xFF1D2532),
    //                             boxShadow: [
    //                               BoxShadow(
    //                                 color: Colors.black.withOpacity(0.1),
    //                                 spreadRadius: 0,
    //                                 blurRadius: 10,
    //                                 offset: const Offset(0, 4), // changes position of shadow
    //                               ),
    //                             ],
    //                           ),
    //                           child: AppWidget.normalText(
    //                             context,
    //                             "ปิดหน้านี้",
    //                             14.sp,
    //                             Color(0xFFE8E6E2),
    //                             FontWeight.w400,
    //                           ),
    //                         ),
    //                       ),
    //                     ),
    //                   ],
    //                 ),
    //               ),
    //             )
    //         ),
    //       ],
    //     ),
    //   ),
    // );
  }
}
