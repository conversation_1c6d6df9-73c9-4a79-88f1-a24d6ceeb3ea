import 'dart:async';
import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';

import 'package:pin_input_text_field/pin_input_text_field.dart';
import 'package:timer_count_down/timer_count_down.dart';

class VerifyPage extends StatefulWidget {
  const VerifyPage({Key? key}) : super(key: key);

  @override
  State<VerifyPage> createState() => _VerifyPageState();
}

class _VerifyPageState extends State<VerifyPage> {
  Timer? _timer;
  int _start = 0;
  RxBool showSendAgain = false.obs;

  final loginCtl = Get.put(LoginController());
  final registerCtl = Get.put(RegisterController());

  void startTimer() {
    try {
      _start = 60;
      const oneSec = Duration(seconds: 1);
      _timer = Timer.periodic(
        oneSec,
        (Timer timer) => setState(
          () {
            if (_start < 1) {
              timer.cancel();
            } else {
              _start = _start - 1;
            }
          },
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Verify");
    _checkUrlAndSetTypeMenu();
  }

  void _checkUrlAndSetTypeMenu() {
    final Uri uri = Uri.base;
    print("Current URL: $uri");
    print("Current path: ${uri.path}");
    print("Query parameters: ${uri.queryParameters}");

    // Check if URL contains specific ref query parameter
    if (uri.path.contains('/register') &&
        uri.queryParameters.containsKey('ref')) {
      print("Setting typeMenu to 'register' based on URL with ref parameter");
      loginCtl.typeMenu.value = 'register';
    } else {
      // For regular URL (https://www.prachakij.com/) set to login
      print("Setting typeMenu to 'login' for regular URL");
      loginCtl.typeMenu.value = 'login';
    }
    print("Final typeMenu value: ${loginCtl.typeMenu.value}");
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Material(
          color: Colors.transparent,
          child: Stack(
            children: [
              ClipRRect(
                borderRadius:
                    BorderRadius.circular(10), // Optional for rounded corners
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                      sigmaX: 5.0, sigmaY: 5.0), // Adjust values
                  child: Container(
                    width: Get.width,
                    height: Get.height,
                    color: Colors.white
                        .withOpacity(0.09), // Semi-transparent overlay
                  ),
                ),
              ),
              Container(
                width: Get.width,
                height: Get.height,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    // stops: const [0.0, 0.5, 1.0],
                    colors: [
                      const Color(0xFFFFC700).withOpacity(0.7),
                      const Color(0xFFFFC700),
                      // const Color(0xFFFF9900).withOpacity(0.5),
                    ],
                  ),
                ),
              ),
              Container(
                width: Get.width,
                height: Get.height,
                color: Colors.white.withOpacity(0.3),
              ),
              Positioned(
                  left: 30,
                  top: 100,
                  child: Image.asset(
                    'assets/image/logo.png',
                    width: 104,
                  )),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: Get.width,
                  height: Get.height * 0.6,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.0, 0.5, 1.0],
                      colors: [
                        Color(0xFFFFFFFF),
                        Color(0xFFFFFFFF),
                        Color(0xFFECECEC)
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF664701).withOpacity(1),
                        offset:
                            const Offset(0, -2), // changes position of shadow
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 30,
                      ),
                      Container(
                        width: Get.width,
                        margin: const EdgeInsets.only(
                          left: 20,
                          right: 20,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppWidget.normalText(context, "ใส่รหัสยืนยัน OTP",
                                18, const Color(0xFF282828), FontWeight.w400),
                            const SizedBox(
                              height: 10,
                            ),
                            AppWidget.normalText(
                                context,
                                "กรุณาใส่รหัสยืนยันบัญชีใช้งาน 6 หลัก เราได้ส่งรหัสไปที่",
                                12,
                                const Color(0xFF895F00),
                                FontWeight.w400),
                            const SizedBox(
                              height: 10,
                            ),
                            AppWidget.boldText(
                                context,
                                loginCtl.phoneTextController.text,
                                12,
                                const Color(0xFF664701),
                                FontWeight.w600),
                            const SizedBox(
                              height: 10,
                            ),
                            AppWidget.normalText(
                                context,
                                // "รหัสอ้างอิง : ${loginCtl.responseSendOTP.result!.result!.refCode}",
                                "รหัสอ้างอิง : ${loginCtl.responseSendCode.refCode}",
                                12,
                                const Color(0xFFBCBCBC),
                                FontWeight.normal),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 30,
                      ),
                      Container(
                          margin: const EdgeInsets.only(
                            left: 80,
                            right: 80,
                          ),
                          width: Get.width,
                          height: 50,
                          child: PinInputTextField(
                            pinLength: 6,
                            decoration: UnderlineDecoration(
                              textStyle: TextStyle(
                                color: const Color(0xFF241F35),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt',
                                letterSpacing: 0.3,
                                shadows: <Shadow>[
                                  Shadow(
                                    offset: const Offset(0, 1),
                                    blurRadius: 1.0,
                                    color: const Color(0xFF000000)
                                        .withOpacity(0.15),
                                  ),
                                ],
                              ),
                              colorBuilder: PinListenColorBuilder(
                                const Color(0xFFE6E6E6),
                                const Color(0xFFE6E6E6).withOpacity(0.7),
                              ),
                            ),
                            controller: loginCtl.pinTextController,
                            textInputAction: TextInputAction.go,
                            enabled: true,
                            autoFocus: true,
                            keyboardType: TextInputType.number,
                            onSubmit: (pin) async {
                              if (pin.length == 6) {
                                print("Attempting OTP Verification.");
                                print(
                                    "Current typeMenu: ${loginCtl.typeMenu.value}");
                                await handleOtpVerification(context, pin);
                              }
                            },
                            onChanged: (pin) async {
                              if (pin.length == 6) {
                                print("Attempting OTP Verification on change.");
                                print(
                                    "Current typeMenu: ${loginCtl.typeMenu.value}");
                                await handleOtpVerification(context, pin);
                              }
                            },
                            // onSubmit: (pin) async {
                            //   if (pin.length == 6) {
                            //     try {
                            //       //TODO :: CHECK OTP FROM LOGIN
                            //       if (loginCtl.typeMenu.value == "login") {
                            //         bool success =
                            //         await loginCtl.newCheckOTP(context);
                            //         loginCtl.pinTextController.text = "";
                            //
                            //         // เวลา log เวลาตรวจสอบ
                            //         print("Login OTP check result: $success");
                            //
                            //         // ถ้า OTP ต้อง ให้นำทาง HomeNavigator โดยตรง
                            //         if (success) {
                            //           // ใช้ try-catch เวลาป้องโทษข้อพลาด
                            //           try {
                            //             print(
                            //                 "Navigating to HomeNavigator after successful OTP verification");
                            //             // นำทาง HomeNavigator โดยตรง
                            //             Navigator.of(context)
                            //                 .pushAndRemoveUntil(
                            //                 MaterialPageRoute(
                            //                     builder: (context) =>
                            //                     const HomeNavigator()),
                            //                     (route) => false);
                            //           } catch (e) {
                            //             print("Navigation error: $e");
                            //             // ถ้าข้อพลาด ลองใช้ Get
                            //             Get.offAll(() => const HomeNavigator());
                            //           }
                            //         }
                            //
                            //         //TODO :: CHECK OTP FROM REGISTER
                            //       } else if (loginCtl.typeMenu.value ==
                            //           "register") {
                            //         bool success =
                            //         await registerCtl.newCheckOTP(context);
                            //         loginCtl.pinTextController.text = "";
                            //
                            //         // เวลา log เวลาตรวจสอบ
                            //         print(
                            //             "Register OTP check result: $success");
                            //
                            //         // ถ้า OTP ต้อง ให้นำทาง HomeNavigator โดยตรง
                            //         if (success) {
                            //           // ใช้ try-catch เวลาป้องโทษข้อพลาด
                            //           try {
                            //             print(
                            //                 "Navigating to HomeNavigator after successful registration");
                            //             // นำทาง HomeNavigator โดยตรง
                            //             Navigator.of(context)
                            //                 .pushAndRemoveUntil(
                            //                 MaterialPageRoute(
                            //                     builder: (context) =>
                            //                     const HomeNavigator()),
                            //                     (route) => false);
                            //           } catch (e) {
                            //             print("Navigation error: $e");
                            //             // ถ้าข้อพลาด ลองใช้ Get
                            //             Get.offAll(() => const HomeNavigator());
                            //           }
                            //         }
                            //       }
                            //     } catch (e) {
                            //       print("Error handling OTP verification: $e");
                            //       // แสดงข้อความแจ้งให้ทราบ
                            //       ScaffoldMessenger.of(context).showSnackBar(
                            //           SnackBar(
                            //               content:
                            //               Text("ข้อพลาด ลองใหม่ครั้ง")));
                            //     }
                            //   }
                            //   // if (pin.length == 6) {
                            //   //   try {
                            //   //     //TODO :: CHECK OTP FROM LOGIN
                            //   //     if (loginCtl.typeMenu.value == "login") {
                            //   //       await loginCtl.newCheckOTP(context);
                            //   //       loginCtl.pinTextController.text = "";
                            //   //       //TODO :: CHECK OTP FROM REGISTER
                            //   //     } else if (loginCtl.typeMenu.value ==
                            //   //         "register") {
                            //   //       await registerCtl.newCheckOTP(context);
                            //   //       loginCtl.pinTextController.text = "";
                            //   //     }
                            //   //   } catch (e) {
                            //   //     print("Error in onSubmit: $e");
                            //   //   }
                            //   // }
                            // },
                            // onChanged: (pin) async {
                            //   if (pin.length == 6) {
                            //     try {
                            //       //TODO :: CHECK OTP FROM LOGIN
                            //       if (loginCtl.typeMenu.value == "login") {
                            //         bool success =
                            //             await loginCtl.newCheckOTP(context);
                            //         loginCtl.pinTextController.text = "";
                            //
                            //         // เวลา log เวลาตรวจสอบ
                            //         print("Login OTP check result: $success");
                            //
                            //         // ถ้า OTP ต้อง ให้นำทาง HomeNavigator โดยตรง
                            //         if (success) {
                            //           // ใช้ try-catch เวลาป้องโทษข้อพลาด
                            //           try {
                            //             print(
                            //                 "Navigating to HomeNavigator after successful OTP verification");
                            //             // นำทาง HomeNavigator โดยตรง
                            //             Navigator.of(context)
                            //                 .pushAndRemoveUntil(
                            //                     MaterialPageRoute(
                            //                         builder: (context) =>
                            //                             const HomeNavigator()),
                            //                     (route) => false);
                            //           } catch (e) {
                            //             print("Navigation error: $e");
                            //             // ถ้าข้อพลาด ลองใช้ Get
                            //             Get.offAll(() => const HomeNavigator());
                            //           }
                            //         }
                            //
                            //         //TODO :: CHECK OTP FROM REGISTER
                            //       } else if (loginCtl.typeMenu.value ==
                            //           "register") {
                            //         bool success =
                            //             await registerCtl.newCheckOTP(context);
                            //         loginCtl.pinTextController.text = "";
                            //
                            //         // เวลา log เวลาตรวจสอบ
                            //         print(
                            //             "Register OTP check result: $success");
                            //
                            //         // ถ้า OTP ต้อง ให้นำทาง HomeNavigator โดยตรง
                            //         if (success) {
                            //           // ใช้ try-catch เวลาป้องโทษข้อพลาด
                            //           try {
                            //             print(
                            //                 "Navigating to HomeNavigator after successful registration");
                            //             // นำทาง HomeNavigator โดยตรง
                            //             Navigator.of(context)
                            //                 .pushAndRemoveUntil(
                            //                     MaterialPageRoute(
                            //                         builder: (context) =>
                            //                             const HomeNavigator()),
                            //                     (route) => false);
                            //           } catch (e) {
                            //             print("Navigation error: $e");
                            //             // ถ้าข้อพลาด ลองใช้ Get
                            //             Get.offAll(() => const HomeNavigator());
                            //           }
                            //         }
                            //       }
                            //     } catch (e) {
                            //       print("Error handling OTP verification: $e");
                            //       // แสดงข้อความแจ้งให้ทราบ
                            //       ScaffoldMessenger.of(context).showSnackBar(
                            //           SnackBar(
                            //               content:
                            //                   Text("ข้อพลาด ลองใหม่ครั้ง")));
                            //     }
                            //   }
                            // },
                          )),
                      const SizedBox(
                        height: 40,
                      ),
                      showSendAgain.value == true
                          ? InkWell(
                              onTap: () async {
                                if (kDebugMode) {
                                  print('ส่งรหัสอีกครั้ง');
                                }
                                var status = await loginCtl.newSendOTP(context);
                                if (status == true) {
                                  setState(() {
                                    loginCtl.pinTextController.text = "";
                                    showSendAgain.value = false;
                                    startTimer();
                                  });
                                }
                              },
                              child: Container(
                                margin: const EdgeInsets.only(
                                  left: 20,
                                  right: 20,
                                ),
                                alignment: Alignment.center,
                                child: RichText(
                                  text: const TextSpan(
                                    text: 'ไม่ได้รับรหัสยืนยัน? ',
                                    style: TextStyle(
                                      fontFamily: 'Prompt',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: Color(0xFF555555),
                                    ),
                                    children: <TextSpan>[
                                      TextSpan(
                                        text: 'ส่งรหัสอีกครั้ง',
                                        style: TextStyle(
                                          fontFamily: 'Prompt-Medium',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: Color(0xFFFFB100),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Align(
                              alignment: Alignment.center,
                              child: Countdown(
                                seconds: 60,
                                build: (_, double time) => Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    AppWidget.normalText(
                                        context,
                                        "ขอรหัสอีกครั้งภายใน ",
                                        14,
                                        const Color(0xFF707070),
                                        FontWeight.w400),
                                    AppWidget.normalText(
                                        context,
                                        "${time.ceil()}",
                                        14,
                                        const Color(0xFFFFB100),
                                        FontWeight.w400),
                                  ],
                                ),
                                onFinished: () {
                                  setState(() {
                                    showSendAgain.value = true;
                                  });
                                },
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  Future<void> handleOtpVerification(BuildContext context, String pin) async {
    print("Handling OTP Verification: Starting Function");
    // print("Current typeMenu: ${loginCtl.typeMenu.value}");
    // print("PIN received: $pin");

    try {
      // ✅ ตั้งค่า pinTextController ด้วยค่า pin ที่ได้รับ แทนการ clear
      loginCtl.pinTextController.text = pin;
      // print("Updated pinTextController: ${loginCtl.pinTextController.text}");

      bool success = false;
      if (loginCtl.typeMenu.value == "login") {
        print("Attempting OTP verification for login...");
        success = await loginCtl.newCheckOTP(context);
        // print("Login OTP check result: $success");
        // LoginController จะจัดการการนำทางเอง ไม่ต้องนำทางที่นี่
      } else if (loginCtl.typeMenu.value == "register") {
        print("Attempting OTP verification for registration...");
        success = await registerCtl.newCheckOTP(context);
        // print("Register OTP check result: $success");
        // RegisterController จะจัดการการนำทางเอง ไม่ต้องนำทางที่นี่
      } else {
        // print("Unknown typeMenu value: ${loginCtl.typeMenu.value}");
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //       content: Text("เกิดข้อผิดพลาดในการระบุประเภทการเข้าสู่ระบบ")),
        // );
        return;
      }

      if (!success) {
        print("OTP Verification did not succeed.");
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text("รหัส OTP ไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง")),
        // );
      }
    } catch (e) {
      // print("Error in OTP submission: ${e.toString()}");
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(content: Text("เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง")),
      // );
    }
    print("Handling OTP Verification: End of Function");
  }
}
