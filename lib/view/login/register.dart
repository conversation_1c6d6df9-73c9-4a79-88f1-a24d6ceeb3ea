
import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/view/referral/scan_qr_refCode.dart';
import 'package:pinput/pinput.dart';

class RegisterPage extends StatefulWidget {
  RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final registerCtl = Get.put(RegisterController());
  final loginCtl = Get.put(LoginController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  String buttonText = "ดำเนินการต่อ"; // ข้อความปุ่มเริ่มต้น
  bool isLoading = false; // กำหนดสถานะโหลด
  bool isError = false; // ตรวจสอบว่ามีข้อผิดพลาดไหม
  String errorMessage = ""; // ข้อความผิดพลาด

  final mrCtl = Get.put(ReferralMRController());
  void _onSubmit() {
    String inputCode = registerCtl.refTextController.text.trim();
    String checkCode = "018814";

    if (inputCode.isNotEmpty) {
      if (isLoading == false) {  // ✅ แก้ไขการตรวจสอบ isLoading
        setState(() {
          isLoading = true;
          isError = false;
        });
        // print("isLoading: $isLoading");
        // print("isError: $isError");
      }

      // Future.delayed(Duration(seconds: 1), () {  // ✅ ใช้ milliseconds แทน microseconds
      //   if (inputCode != checkCode) { // ❌ รหัสผิด
      //     setState(() {
      //       isLoading = false;
      //       isError = true;
      //       buttonText = "ดำเนินการต่อ";
      //       errorMessage = "รหัสผู้แนะนำไม่ถูกต้อง กรุณาลองใหม่"; // ✅ ปรับข้อความให้เข้าใจง่ายขึ้น
      //     });
      //   } else { // ✅ รหัสถูกต้อง
      //     setState(() {
      //       isLoading = false;
      //       isError = false;
      //     });
      //
      //     Get.snackbar(
      //       "สำเร็จ",
      //       "รหัสถูกต้อง! ดำเนินการต่อ...",
      //       snackPosition: SnackPosition.BOTTOM,
      //       backgroundColor: Colors.green.withOpacity(0.8),
      //       colorText: Colors.white,
      //     );
      //
      //     // ✅ สามารถใส่โค้ดไปหน้าถัดไปหรือปิด modal ได้ที่นี่
      //   }
      // });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // registerCtl.refTextController.addListener(() {
    //   setState(() {
    //     _isTextNotEmpty = registerCtl.refTextController.text.isNotEmpty;
    //   });
    // });

    analytics.setCurrentScreen(screenName: "Register");
    String? ref = Get.parameters['ref'];
    if (ref != null && ref.isNotEmpty) {
      registerCtl.refTextController.text = ref;
      registerCtl.isScanned.value = true;
      registerCtl.update();
    }
  }
  // @override
  // void dispose() {
  //   registerCtl.refTextController.clear(); // ✅ ล้างค่าทุกครั้งที่ปิดหน้า
  //   super.dispose();
  // }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      // backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10), // Optional for rounded corners
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // Adjust values
              child: Container(
                width: Get.width,
                height: Get.height,
                color: Colors.white.withOpacity(0.09), // Semi-transparent overlay
              ),
            ),
          ),
          Container(
            width: Get.width,
            height: Get.height,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                // stops: const [0.0, 0.5, 1.0],
                colors: [
                  const Color(0xFFFFC700).withOpacity(0.7),
                  const Color(0xFFFFC700),
                  // const Color(0xFFFF9900).withOpacity(0.5),
                ],
              ),
            ),
          ),
          Container(
            width: Get.width,
            height: Get.height,
            color: Colors.white.withOpacity(0.3),
          ),
          // ClipRRect(
          //   borderRadius: BorderRadius.circular(10), // Optional for rounded corners
          //   child: BackdropFilter(
          //     filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0), // Adjust values
          //     child: Container(
          //       width: Get.width,
          //       height: Get.height,
          //       color: Colors.white.withOpacity(0.4), // Semi-transparent overlay
          //     ),
          //   ),
          // ),
          // ClipRRect(
          //     child: BackdropFilter(
          //       filter: ImageFilter.blur(sigmaX: 30.0, sigmaY: 10.0),
          //       child: Container(
          //         width: Get.width,
          //         height: Get.height,
          //         decoration: BoxDecoration(
          //           gradient: LinearGradient(
          //             begin: Alignment.topCenter,
          //             end: Alignment.bottomCenter,
          //             stops: const [0.0, 0.5, 1.0],
          //             colors: [
          //               const Color(0xFFFFC700).withOpacity(0.8),
          //               const Color(0xFFFFB100),
          //               const Color(0xFFFF9900).withOpacity(0.8),
          //             ],
          //           ),
          //         ),
          //       ),
          //     )),
          Positioned(
              left: 30,
              top: 100,
              child: Image.asset(
                'assets/image/logo.png',
                width: 104,
              )),
          Positioned(
              right: 30,
              top: 60,
              child:InkWell(
                onTap: () {
                  // print("🔄 ปิดหน้า Register → กลับไปหน้า Login");
                  loginCtl.page.value = "login"; // ✅ ตั้งค่ากลับไปเป็น login
                  loginCtl.typeMenu.value = "login";
                  Get.offAll(MobileBodyPage(), transition: Transition.downToUp, // ✅ ทำให้เปลี่ยนหน้าค่อยๆ จางเข้า
                  ); // ✅ ปิดทุกหน้าและกลับไป MobileBodyPage
                },
                child: Container(
                  height: 34,
                  width: 34,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white, width: 1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.close_rounded,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              )
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: Get.width,
              height: Get.height * 0.57,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0.0, 0.5, 1.0],
                  colors: [
                    Color(0xFFFFFFFF),
                    Color(0xFFFFFFFF),
                    Color(0xFFECECEC),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF664701).withOpacity(1),
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              padding: const EdgeInsets.only(top: 30, left: 18, right: 18),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// 🟡 Title
                  Text(
                    "สมัครบัญชีใช้งาน",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF282828),
                      height: 1,
                      shadows: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 4),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'กรอกข้อมูลให้ถูกต้อง',
                    style: TextStyle(
                      fontSize: 14,
                      color: const Color(0xFF895f00),
                      height: 1,
                      shadows: [
                        BoxShadow(
                          color: const Color(0xFF000000).withOpacity(0.1),
                          offset: const Offset(0, 4),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Container(
                        // color: Colors.red,
                        child:Column(
                          children: [
                            /// 🟢 TextField - ชื่อ
                            buildTextField(
                              controller: registerCtl.firstNameTextController,
                              hintText: 'กรอกชื่อจริง',
                            ),
                      
                            /// 🟢 TextField - นามสกุล
                            buildTextField(
                              controller: registerCtl.lastNameTextController,
                              hintText: 'นามสกุล',
                            ),
                      
                            /// 🟢 TextField - เบอร์โทร
                            buildTextField(
                              controller: loginCtl.phoneTextController,
                              hintText: 'เบอร์โทรศัพท์',
                              keyboardType: TextInputType.number,
                            ),
                      
                            /// 🔵 Ref Code & QR Button
                            Container(
                              width: Get.width,
                              height: 53,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(
                                  color: const Color(0xFFE8E6E2),
                                ),
                              ),
                              // color: Colors.transparent, // ✅ เปลี่ยนเป็นโปร่งใส
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  /// 🟣 Ref Code Button
                                  Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () {
                                        addRefCode(context);
                                        // debugPrint('Ref Code');
                                      },
                                      child: Container(
                                        width: Get.width * 0.7,
                                        height: 53,
                                        // color: Colors.blue,
                                        alignment: Alignment.center,
                                        child: ValueListenableBuilder<TextEditingValue>(
                                          valueListenable: registerCtl.refTextController,
                                          builder: (context, value, child) {
                                            return Padding(
                                              padding: EdgeInsets.only(left: 80),
                                              child: RichText(
                                                textAlign: TextAlign.center,
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: value.text.isEmpty
                                                          ? 'รหัสผู้แนะนำ'
                                                          : value.text,
                                                      style: TextStyle(
                                                          fontWeight: FontWeight.w400,
                                                          fontFamily: 'Prompt',
                                                          color: Color(0xFF282828),
                                                          fontSize: 14,
                                                          height: 1.9),
                                                    ),
                                                    TextSpan(
                                                      text: value.text.isEmpty
                                                          ? ' (ถ้ามี)'
                                                          : "",
                                                      style: TextStyle(
                                                          fontWeight: FontWeight.w400,
                                                          fontFamily: 'Prompt',
                                                          color: Color(0xFFBCBCBC),
                                                          fontSize: 14,
                                                          height: 1.9),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                        // Text(
                                        //   registerCtl.refTextController.text.isEmpty ? 'รหัสผู้แนะนำ' : registerCtl.refTextController.text,
                                        //   style: TextStyle(color: Colors.black),
                                        // ),
                                      ),
                                    ),
                                  ),
                      
                                  /// 🟣 Scan QR Button
                                  Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () async {
                                        final result = await Navigator.push(
                                          context,
                                          MaterialPageRoute(builder: (context) => QRScannerPage()),
                                        );
                                        if (result != null && result is String) {
                                          // เมื่อได้รับค่า QR Code กลับมา
                                          registerCtl.refTextController.text = result; // ตั้งค่าใน TextEditingController
                                          registerCtl.isScanned.value = true; // อัปเดตสถานะการสแกน
                                          registerCtl.update(); // รีเฟรช UI
                                        }
                                        // Navigator.push(
                                        //     context,
                                        //     MaterialPageRoute(
                                        //         builder: (context) => ScanQrRefcode()));
                                        // debugPrint('Scan QR Code');
                                      },
                                      child: Container(
                                        width: 53,
                                        height: 53,
                                        // color: Colors.green,
                                        alignment: Alignment.center,
                                        child: Image.asset(
                                          'assets/image/refCode/scanQr.png',
                                          scale: 2,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 58,
                            ),
                            // Spacer(),
                            registerCtl.firstNameTextController.text.isNotEmpty &&
                                registerCtl.lastNameTextController.text.isNotEmpty &&
                                loginCtl.phoneTextController.text.isNotEmpty
                                ? InkWell(
                              onTap: () {
                                //TODO :: ส่ง OTP => ใช้อันเดียวกับตัว Login
                                if (kDebugMode) {
                                  // print(loginCtl.typeMenu.value);
                                }
                                if (loginCtl.typeMenu.value != "registerWithTG") {
                                  registerCtl.newCheckProfile(context);
                                } else {
                                  // registerCtl.newCheckProfileWithTG(context);
                                }
                              },
                              child: Container(
                                width: Get.width,
                                height: 53,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    // stops: const [0.0, 1.0],
                                    colors: [
                                      const Color(0xFF000000).withOpacity(0.7),
                                      const Color(0xFF000000),
                                    ],
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: AppWidget.boldText(context, "สมัครบัญชี", 16,
                                    const Color(0xFFFFB100), FontWeight.w500),
                              ),
                            )
                                : InkWell(
                              onTap: () {
                                //TODO :: ส่ง OTP => ใช้อันเดียวกับตัว Login
                                if (kDebugMode) {
                                  // print(loginCtl.typeMenu.value);
                                }
                                if (loginCtl.typeMenu.value != "registerWithTG") {
                                  registerCtl.newCheckProfile(context);
                                } else {
                                  // registerCtl.newCheckProfileWithTG(context);
                                }
                              },
                              child: Opacity(
                                opacity: 0.2,
                                child: Container(
                                  width: Get.width,
                                  height: 53,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    // color: Colors.black,
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      // stops: const [0.0, 1.0],
                                      colors: [
                                        const Color(0xFF000000).withOpacity(0.7),
                                        const Color(0xFF000000),
                                      ],
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    'สมัครบัญชี',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontFamily: 'Prompt',
                                      color: const Color(0xFFFFB100),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                      
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 20,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              // SingleChildScrollView(
              //   // physics: const BouncingScrollPhysics(),
              //   child:
              // ),
            ),
          ),
        ],
      ),
    );

  }

  void addRefCode(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          // left: 16,
          // right: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom + 20, // ปรับ UI ไม่ให้โดนบัง
        ),
        child: SingleChildScrollView(
          child: Container(
            height: 373,
            width: Get.width,
            // color: Colors.red,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 15),
                Container(
                  width: 44,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Color(0XFF1A1818).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                SizedBox(height: 54),
                Container(
                    height: 70,
                    width: 75,
                    // color: Colors.red,
                    child: Center(child: Image.asset('assets/image/refCode/popUpRef.png',scale: 2,))),
                SizedBox(height: 16),
                Text(
                  'รหัสผู้แนะนำ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                    color: Color(0xFF1A1818),
                    fontFamily: 'Prompt',
                  ),
                ),
                Text(
                  'กรอกรหัสผู้แนะนำ 6 หลัก ตัวอย่าง: 123456',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    height: 1.71,
                    color: Color(0xFF282828),
                    fontFamily: 'Prompt',
                  ),
                ),
                SizedBox(height: 14),
                _buildPinCodeFields(),
                SizedBox(height: 10),
                // แสดง Error Message ถ้ามี
                Obx(() => mrCtl.errorMessage.isNotEmpty
                    ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline_rounded, color: Colors.red, size: 18),
                    SizedBox(width: 6),
                    Text(
                      mrCtl.errorMessage.value, // ✅ ดึงค่าจาก RxString
                      style: TextStyle(
                        fontFamily: 'Prompt',  // ✅ ใช้ฟอนต์ Prompt
                        fontWeight: FontWeight.w400,  // ✅ น้ำหนักฟอนต์ 400 (Regular)
                        fontSize: 12,  // ✅ ขนาด 12px
                        height: 1.0,  // ✅ เทียบเท่า line-height: 12px;
                        letterSpacing: 0.0,  // ✅ ไม่มี letter-spacing
                        color: Color(0xFFEB2227),  // 🎨 เปลี่ยนสีได้ตามต้องการ
                        shadows: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),  // ✅ #0000000D (5% opacity)
                            offset: Offset(0, 4),  // ✅ X = 0px, Y = 4px
                            blurRadius: 10,  // ✅ เบลอ 10px
                            spreadRadius: 0,  // ✅ กระจายเงา 0px
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                )
                    : SizedBox()),
                Spacer(),
                /// ปุ่มกด
                Padding(
                  padding: EdgeInsets.only(left: 16, right: 16,),
                  child: Obx(() => Opacity(
                    opacity: (registerCtl.refTextController.text.isEmpty)
                        ? 0.2
                        : 1, // ✅ ป้องกันการกดระหว่างโหลด
                    child: Container(
                      width: Get.width,
                      height: 54,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: mrCtl.loadingCheck.value
                            ? Color(0xFFEBEBEB) // ✅ เปลี่ยนเป็นสีเทาอ่อนเมื่อกด
                            : null, // ✅ ถ้าไม่ได้กด ให้ใช้ Gradient
                        gradient: mrCtl.loadingCheck.value
                            ? null // ❌ ปิด Gradient เมื่อกำลังโหลด
                            : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.7),
                            Colors.black,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            offset: const Offset(0, 4),
                            blurRadius: 10,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          // onTap: mrCtl.loadingCheck.value ? null : ()=> mrCtl.checkMRCode(context), // ✅ ป้องกันกดซ้ำถ้ายังโหลดอยู่
                          onTap: mrCtl.loadingCheck.value ? null : ()=> mrCtl.checkMRCodeForRegisterBody(context), // ✅ ป้องกันกดซ้ำถ้ายังโหลดอยู่
                          child: Center(
                            child: mrCtl.loadingCheck.value
                                ? Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text('รอสักครู่',
                                  style: TextStyle(
                                    color: Color(0xFF282828),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                  ),
                                ),
                                SizedBox(width: 10),
                                Container(
                                  width: 30,
                                  child:
                                  LoadingIndicator(
                                    indicatorType:
                                    Indicator
                                        .ballPulseSync,
                                    colors: [
                                      Color(
                                          0xFFFFB100)
                                    ],
                                    strokeWidth: 2,
                                  ),
                                )
                              ],
                            ) // ✅ แสดง Loading
                                : Text(
                              "ดำเนินการต่อ",
                              style: TextStyle(
                                color: Color(0xFFFFB100),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Prompt',
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )),
                ),
                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    ).whenComplete((){
      // registerCtl.refTextController.clear();
      mrCtl.errorMessage.value = "";
    });
  }
  _buildPinCodeFields() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 40,
      // color: Colors.red,
      // padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Pinput(
              separatorBuilder: (index) => SizedBox(width: 16),
              length: 6, // จำนวนหลักของรหัส
              controller: registerCtl.refTextController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly], // รับเฉพาะตัวเลข
              showCursor: true, // เปิดให้แสดง Cursor
              cursor: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 2, // ความกว้างของ Cursor
                    height: 20, // ความสูงของ Cursor
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: Color(0xFFFF9300), // สีของ Cursor
                    ),),
                ],
              ),
              defaultPinTheme: PinTheme(
                width: 30, // ความกว้างของช่อง
                height: 40, // ความสูงของช่อง
                textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF1A1818)
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFD9D8D5), width: 1),
                ),
              ),
              focusedPinTheme: PinTheme(
                width: 30,
                height: 40,
                textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF1A1818)
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFD9D8D5), width: 1),
                  // border: Border.all(color: Colors.blue, width: 2), // เปลี่ยนสีขอบเมื่อ focus
                ),
              ),
              preFilledWidget: Text(
                "–",  // ใช้ "–" เป็นค่าพื้นฐาน
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFFD9D8D5), // สีของ placeholder
                ),
              ),
              onCompleted: (code) {
                registerCtl.refTextController.text = code;
                // print("รหัสที่กรอก: $code");
                // print("รหัสที่กรอก: ${registerCtl.refTextController.text}");
              },
              onChanged: (value){
                if (value.isNotEmpty) {
                  mrCtl.errorMessage.value = ''; // ✅ ล้าง Error เมื่อมีการแก้ไข
                }
              },
            ),
          ),
          SizedBox(width: 16),
          // Container(
          //   color: Colors.red,
          //   width: 30,
          //   height: 40,),
          InkWell(
            onTap: (){
              Navigator.push(context,MaterialPageRoute(builder: (context) => QRScannerPage()));
            },
            child: Container(
                width: 30,
                height: 40,
                // color: Colors.teal,
                child: Center(child: Image.asset('assets/image/refCode/scanQr.png',scale: 2,))),
          ),
        ],
      ),
    );
  }

  Widget buildTextField({
    required TextEditingController controller,
    required String hintText,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: TextField(
        onChanged: (value) {
          // controller.text = value;
          // print(controller.text);
          setState(() {});
        },
        controller: controller,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.bottom,
        keyboardType: keyboardType,
        inputFormatters: [
          FilteringTextInputFormatter.deny(AppService.deny()),
        ],
        style: const TextStyle(
          fontWeight: FontWeight.w400,
          fontFamily: 'Prompt',
          color: Color(0xFF282828),
          fontSize: 14,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            fontFamily: 'Prompt',
            color: Color(0xFF282828),
            fontSize: 14,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: const BorderSide(
              color: Color(0xFFE8E6E2),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: const BorderSide(
              color: Color(0xFFE8E6E2),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: const BorderSide(
              color: Color(0xFFFFB100),
            ),
          ),
        ),
      ),
    );
  }
}