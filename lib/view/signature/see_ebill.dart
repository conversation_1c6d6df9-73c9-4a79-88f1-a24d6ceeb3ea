import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/view/signature/PopUpSign.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';

class SeeEbill extends StatefulWidget {
  const SeeEbill({Key? key}) : super(key: key);

  @override
  State<SeeEbill> createState() => _SeeEbillState();
}

class _SeeEbillState extends State<SeeEbill> {

  ESignatureController eSign = Get.find<ESignatureController>();
  final box = GetStorage();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    showPopUp();
  }

  Future<void> showPopUp() async {
    if (eSign.showPopUp) {
      Future.delayed(Duration(milliseconds: 500), () {
        signatureSpace();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF3F3F3),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     box.write("checkAcceptUseESign", null);
      //   },
      //   child: Icon(Icons.edit),
      //   backgroundColor: Color(0xFFFFB100),
      // ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: GetBuilder<ESignatureController>(
            builder: (eSignatureController) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            height: 35,
                            width: 35,
                            decoration: BoxDecoration(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey),
                            ),
                            alignment: Alignment.center,
                            child: SvgPicture.asset(
                              "assets/image/eSign/back_button.svg",
                              height: 12,
                            ),
                          )),
                      Text(
                        'PDF',
                        style: TextStyle(fontSize: 21, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(
                        height: 35,
                        width: 35,
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  Container(
                    height: Get.height * 0.8,
                    width: Get.width,
                    child: PDF().fromUrl(
                      eSignatureController.showPDF,
                      placeholder: (double progress) =>
                          Center(child: Text('$progress %')),
                      errorWidget: (dynamic error) =>
                          Center(child: Text(error.toString())),
                    ),
                  ),
                  eSignatureController.checkPutSign || eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["status_sign"] != null
                      ? InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            /// อย่าลืมปิดตรงนี้นะ
                            // signatureSpace();
                          },
                          child: Container(
                            width: Get.width,
                            height: 42,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                end: Alignment(0.0, 1),
                                begin: Alignment(0.0, 0.0),
                                colors: [
                                  Color(0xFF555555),
                                  Color(0xFF555555),
                                  Color(0xFF3C3627),
                                  Color(0xFF292828)
                                ],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset("assets/image/eSign/Edit_box2.svg",
                                    height: 18, fit: BoxFit.fitHeight),
                                SizedBox(width: 8),
                                Text(
                                  'เซนต์'+eSignatureController.typeDoc+'เรียบร้อย',
                                  style: TextStyle(
                                      color: Color(0xFFFFB100), fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        )
                      : InkWell(
                          onTap: () {
                            signatureSpace();
                          },
                          child: Container(
                            width: Get.width,
                            height: 42,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                end: Alignment(0.0, 1),
                                begin: Alignment(0.0, 0.0),
                                colors: [
                                  Color(0xFFFFC700),
                                  Color(0xFFFFB100),
                                  Color(0xFFFF9900)
                                ],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                    "assets/image/eSign/sign_pencil.svg",
                                    height: 18,
                                    fit: BoxFit.fitHeight),
                                SizedBox(width: 8),
                                Text(
                                  'เซนต์'+eSignatureController.typeDoc,
                                  style: TextStyle(
                                      color: Color(0xFF282828), fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              );
            }
          ),
        ),
      ),
    );
  }

  signatureSpace() async {
    if(box.read("checkAcceptUseESign") == null){
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          child: Container(
            width: Get.width * 0.8,
            height: Get.height * 0.35,
            decoration: BoxDecoration(
              color: Color(0xFFF3F3F3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: Get.width * 0.7,
                  child: Text(
                    "เพื่อให้การทำธุรกรรมของท่านเป็นไปอย่างราบรื่น เราขอความยินยอมจากท่านในการใช้ลายเซ็นดิจิทัล ข้อมูลลายเซ็นของท่านจะถูกเก็บรักษาไว้อย่างปลอดภัยที่เครื่องของท่านเอง และจะไม่ถูกเก็บหรือใช้เพื่อวัตถุประสงค์อื่นโดยไม่ได้รับความยินยอมจากท่าน",
                    maxLines: null,
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFFFFB100),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    minimumSize: Size(Get.width * 0.3, 40),
                  ),
                  onPressed: () async {
                    box.write("checkAcceptUseESign", true);
                    Get.back();
                    await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) => PopUPSign(),
                    );
                  },
                  child: Text('ตกลง',
                    style: TextStyle(fontSize: 14, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }else{
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => PopUPSign(),
      );
    }
    setState(() {});
  }
}
