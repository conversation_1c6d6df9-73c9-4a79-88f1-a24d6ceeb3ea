import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/verifyESignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:pin_input_text_field/pin_input_text_field.dart';
import 'package:timer_count_down/timer_count_down.dart';

class VerifyESign extends StatefulWidget {
  const VerifyESign({Key? key}) : super(key: key);

  @override
  State<VerifyESign> createState() => _VerifyESignState();
}

class _VerifyESignState extends State<VerifyESign> {

  Timer? _timer;
  int _start = 0;
  RxBool showSendAgain = false.obs;

  // final loginCtl = Get.put(LoginController());
  final verifyESignCtl = Get.put(VerifyESignController());
  ProfileController profileCtrl = Get.find<ProfileController>();

  final TextEditingController pinTextController = TextEditingController();


  void startTimer() {
    try {
      _start = 60;
      const oneSec = Duration(seconds: 1);
      _timer = Timer.periodic(
        oneSec,
            (Timer timer) => setState(
              () {
            if (_start < 1) {
              timer.cancel();
            } else {
              _start = _start - 1;
            }
          },
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  // static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // analytics.setCurrentScreen(screenName: "Verify");
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _timer!.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            width: Get.width,
            height: Get.height * 0.6,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 0.5, 1.0],
                colors: [Color(0xFFFFFFFF), Color(0xFFFFFFFF), Color(0xFFECECEC)],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF664701).withOpacity(1),
                  offset: const Offset(0, -2), // changes position of shadow
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 30,
                ),
                Container(
                  width: Get.width,
                  margin: const EdgeInsets.only(
                    left: 20,
                    right: 20,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppWidget.normalText(context, "ใส่รหัสยืนยัน OTP", 18,
                          const Color(0xFF282828), FontWeight.w400),
                      const SizedBox(
                        height: 10,
                      ),
                      AppWidget.normalText(
                          context,
                          "กรุณาใส่รหัสยืนยันบัญชีใช้งาน 6 หลัก เราได้ส่งรหัสไปที่",
                          12,
                          const Color(0xFF895F00),
                          FontWeight.w400),
                      const SizedBox(
                        height: 10,
                      ),
                      AppWidget.boldText(
                          context,
                          profileCtrl.profile.value.mobile,
                          12,
                          const Color(0xFF664701), FontWeight.w600),
                      const SizedBox(
                        height: 10,
                      ),
                      AppWidget.normalText(
                          context,
                          // "รหัสอ้างอิง : ${loginCtl.responseSendOTP.result!.result!.refCode}",
                          "รหัสอ้างอิง : ${verifyESignCtl.responseSendCode.refCode}",
                          12,
                          const Color(0xFFBCBCBC),
                          FontWeight.normal),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
                Container(
                    margin: const EdgeInsets.only(
                      left: 80,
                      right: 80,
                    ),
                    width: Get.width,
                    height: 50,
                    child: PinInputTextField(
                      pinLength: 6,
                      decoration: UnderlineDecoration(
                        textStyle: TextStyle(
                          color: const Color(0xFF241F35),
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          letterSpacing: 0.3,
                          shadows: <Shadow>[
                            Shadow(
                              offset: const Offset(0, 1),
                              blurRadius: 1.0,
                              color: const Color(0xFF000000).withOpacity(0.15),
                            ),
                          ],
                        ),
                        colorBuilder: PinListenColorBuilder(
                          const Color(0xFFE6E6E6),
                          const Color(0xFFE6E6E6).withOpacity(0.7),
                        ),
                      ),
                      controller: pinTextController,
                      textInputAction: TextInputAction.go,
                      enabled: true,
                      autoFocus: true,
                      keyboardType: TextInputType.number,
                      // onSubmit: (pin) async {
                      //   if (pin.length == 6) {
                      //
                      //   }
                      // },
                      onChanged: (pin) async {
                        if (pin.length == 6) {
                          await verifyESignCtl.newCheckOTP(context, pin);
                          pinTextController.text = "";
                          // if(checkVerify){
                            Navigator.pop(context);
                          // }
                        }
                      },
                    )),
                const SizedBox(
                  height: 40,
                ),
                showSendAgain.value == true
                    ? InkWell(
                  onTap: () async {
                    if (kDebugMode) {
                      print('ส่งรหัสอีกครั้ง');
                    }
                    var status = await verifyESignCtl.newSendOTP(context);
                    if (status == true) {
                      setState(() {
                        pinTextController.text = "";
                        showSendAgain.value = false;
                        startTimer();
                      });
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.only(
                      left: 20,
                      right: 20,
                    ),
                    alignment: Alignment.center,
                    child: RichText(
                      text: const TextSpan(
                        text: 'ไม่ได้รับรหัสยืนยัน? ',
                        style: TextStyle(
                          fontFamily: 'Prompt',
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF555555),
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text: 'ส่งรหัสอีกครั้ง',
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
                    : Align(
                  alignment: Alignment.center,
                  child: Countdown(
                    seconds: 60,
                    build: (_, double time) => Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppWidget.normalText(
                            context,
                            "ขอรหัสอีกครั้งภายใน ",
                            14,
                            const Color(0xFF707070),
                            FontWeight.w400),
                        AppWidget.normalText(
                            context,
                            "${time.ceil()}",
                            14,
                            const Color(0xFFFFB100),
                            FontWeight.w400),
                      ],
                    ),
                    onFinished: () {
                      setState(() {
                        showSendAgain.value = true;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
