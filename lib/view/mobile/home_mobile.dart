import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/car_repair_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/lifetime_appointment_controller.dart';
import 'package:mapp_prachakij_v3/view/car_repair_status/camera_page.dart';
import 'package:mapp_prachakij_v3/view/car_repair_status/home_car_repair_status.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/verify_tg.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/home/<USER>/dialog_newcar.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/event_register/detail_event_register.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/minilike/mini_like.dart';
import 'package:mapp_prachakij_v3/view/signature/sig_detail.dart';
import 'package:mapp_prachakij_v3/view/tester/webview.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../../component/widget.dart';
import '../../controller/setting_controller/MR_controller.dart';
import '../../controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import '../../controller/setting_controller/login_controller/login_controller.dart';
import '../../controller/setting_controller/new_car_controller.dart';
import '../../controller/setting_controller/page_controller.dart';
import '../../controller/setting_controller/profile_controller.dart';
import '../../controller/setting_controller/setting_controller.dart';
import '../home/<USER>/my-car.dart';
import '../home/<USER>/new_car.dart';
import '../home/<USER>/list_service.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';

class HomeMobile extends StatefulWidget {
  const HomeMobile({Key? key}) : super(key: key);

  @override
  State<HomeMobile> createState() => _HomeMobileState();
}

class _HomeMobileState extends State<HomeMobile> {
  final profileCtl = Get.put(ProfileController());
  final loginCtl = Get.put(LoginController());
  final pageCtl = Get.put(PageSelectController());
  final centerCtl = Get.put(SettingController());
  final likePointCtl = Get.put(LikePointController());
  final mrCtl = Get.put(ReferralMRController());
  final newCarCtl = Get.put(NewCarController());
  final lifetimeAppointment = Get.put(LifetimeAppointmentController());
  final webviewCtl = Get.put(WebViewLikePointController());
  final chatInAppCtl = Get.put(ChatInAppController());
  final carRepairCtl = Get.put(CarRepairController());
  final  tutorialController = Get.put(TutorialController());
  void showHomeTutorial() {
    // final tutorialCtl = Get.find<TutorialController>();

    // ตรวจสอบสถานะของหน้า Home ก่อนแสดง tutorial
    tutorialController.checkHomeStatus();

    // แสดง tutorial ตามสถานะ ตรวจสอบแล้ว
    tutorialController.showTutorialPage(context, "home");
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // ให้เวลา UI render ก่อนแสดง tutorial
      showHomeTutorial();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => NotificationListener(
      onNotification: (notification) {
        if (notification is ScrollUpdateNotification &&
            notification.metrics.axis == Axis.vertical) {
          pageCtl.scrollParam.value = notification.metrics.pixels;
        }
        if (pageCtl.scrollParam.value > 100) {
          newCarCtl.isShow.value = false;
        }
        return true;
      },
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 130,
                ),
                // chatInApp(),
                profileCtl.token.value != null ? infoCard() : Container(),
                const SizedBox(
                  height: 10,
                ),
                // const HomeCarRepairStatusPage(),
                profileCtl.token.value != null
                    ? newCarCtl.status.value == 200 && newCarCtl.newCar.newCarList!.isNotEmpty
                    ? const NewCarCard()
                    : const SizedBox()
                    : const SizedBox(),
                SizedBox(
                  height: newCarCtl.status.value == 200 ? 10 : 0,
                ),
                carDetail(),
                SizedBox(
                  height: lifetimeAppointment.status.value == 200 ? 10 : 0,
                ),
                const ListServicePage(),
                const SizedBox(
                  height: 10,
                ),
                InkWell(
                  onTap: () {
                    if (profileCtl.token.value == null) {
                      AppWidget.showDialogPageSlide(
                          context, const LoginPage());
                    } else {
                      mrCtl.navigatorMR();
                      mrCtl.showMRTutorial(context);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.only(left: 8, right: 8),
                    width: Get.width,
                    height: 176,
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            width: Get.width,
                            height: 130,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                stops: [0.0, 1.0],
                                colors: [
                                  Color(0xFFFEFEFE),
                                  Color(0xFFF8F8F8),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  spreadRadius: 0,
                                  blurRadius: 10,
                                  offset: const Offset(
                                      0, 4), // changes position of shadow
                                ),
                              ],
                            ),
                          ),
                        ),
                        Align(
                            alignment: Alignment.topLeft,
                            child: Container(
                              padding: const EdgeInsets.only(
                                  left: 16, bottom: 50),
                              child: Image.asset(
                                  "assets/image/MR/LadySky.png"),
                            )),
                        Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, bottom: 8),
                          alignment: Alignment.bottomCenter,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppWidget.boldText(
                                  context,
                                  "กิจกรรมแนะนำลูกค้า",
                                  12,
                                  const Color(0xFF282828),
                                  FontWeight.w600),
                              RichText(
                                text: const TextSpan(children: [
                                  TextSpan(
                                      text: "มาร่วมเป็นส่วนหนึ่งกับเรา",
                                      style: TextStyle(
                                          color: Color(0xFF282828),
                                          fontSize: 12,
                                          fontFamily: 'Prompt',
                                          fontWeight: FontWeight.w300)),
                                  TextSpan(
                                      text: " ยิ่งแนะนำ ยิ่งได้มาก ",
                                      style: TextStyle(
                                          color: Color(0xFF282828),
                                          fontSize: 12,
                                          fontFamily: 'Prompt-Medium',
                                          fontWeight: FontWeight.w600)),
                                  TextSpan(
                                      text: "\nสร้างรายได้แบบง่ายๆ",
                                      style: TextStyle(
                                          color: Color(0xFF282828),
                                          fontSize: 12,
                                          fontFamily: 'Prompt',
                                          fontWeight: FontWeight.w300)),
                                ]),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Container(
                                  width: Get.width,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: [0.0, 1.0],
                                      colors: [
                                        Color(0xFF664701),
                                        Color(0xFF1D1400),
                                      ],
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: RichText(
                                    text: const TextSpan(children: [
                                      TextSpan(
                                          text: "แนะนำ รับคะแนนเพิ่ม ",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontFamily: 'Prompt',
                                              fontWeight: FontWeight.w200)),
                                      TextSpan(
                                          text: "คลิก!",
                                          style: TextStyle(
                                              color: Color(0xFFFFB100),
                                              fontSize: 14,
                                              fontFamily: 'Prompt-Medium',
                                              fontWeight: FontWeight.w700)),
                                    ]),
                                  ))
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                const SizedBox(
                  height: 90,
                ),
              ],
            ),
            Column(
              children: [
                const SizedBox(
                  height: 130,
                ),
                newCarCtl.isShow.isTrue
                    ? const DialogNewCar()
                    : const SizedBox(),
              ],
            ),
            Positioned(
              top: -Get.height,
              child: AnimatedContainer(
                width: Get.width,
                height: centerCtl.closeHead.value == false
                    ? Get.height
                    : Get.height * 2,
                duration: const Duration(milliseconds: 300),
                alignment: Alignment.bottomCenter,
                // color: Colors.transparent,
                child: Stack(
                  children: [
                    Positioned(
                      top: Get.height,
                      child: migration(),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    ));
  }

  Widget carDetail() {
    if (profileCtl.token.value != null) {
      if (carRepairCtl.status.value == 200) {
        return const HomeCarRepairStatusPage();
      } else if (lifetimeAppointment.status.value == 200) {
        return const MyCarCard();
      } else {
        return const SizedBox();
      }
    } else {
      return const SizedBox();
    }
  }

  Widget infoCard() {
    return webviewCtl.readyForUse.isTrue
        ? webviewCtl.useOnlyLikePoint.isTrue
        ? Padding(
      padding:  EdgeInsets.only(left: 8, right: 8),
      child: Container(
        width: Get.width,
        // height: 48,
        // color: Colors.red,
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // TODO :: SCAN QR CODE
            Container(
              height: 48,
              width: 48,
              decoration: ShapeDecoration(
                gradient: const LinearGradient(
                  begin: Alignment(0.00, -1.00),
                  end: Alignment(0, 1),
                  colors: [Colors.white, Color(0xFFF7F7F7)],
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0C000000),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              // margin: const EdgeInsets.only(left: 8),
              child: InkWell(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () async {
                    var value = await Get.to(() => const GetQrCodePage());
                    if (value != null) {
                      setState(() {
                        AppLoader.loader(context);
                      });
                      var responseEvent =
                      await AppApi.getEventRegisterByID(
                          context, value);
                      if (responseEvent["status"] == 200) {
                        Get.to(() => DetailEventRegisterPage(
                            responseEvent["result"]));
                      }
                    }
                  },
                  child:
                  SvgPicture.asset("assets/image/home/<USER>")),
            ),
            // TODO :: SCAN QR CODE
            SizedBox(
              width: 10,
            ),
            Expanded(
              child: Container(
                // width: Get.width,
                // padding: const EdgeInsets.only(right: 8),
                // margin: const EdgeInsets.only(right: 8),
                height: 48,
                decoration: ShapeDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment(0.00, -1.00),
                    end: Alignment(0, 1),
                    colors: [Colors.white, Color(0xFFF7F7F7)],
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  shadows: const [
                    BoxShadow(
                      color: Color(0x0C000000),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    InkWell(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () async {
                        try {
                          await Permission.camera.request();
                          webviewCtl.phoneEncode.value = base64Encode(utf8
                              .encode(profileCtl.profile.value.phoneFirebase
                              .toString()));
                          webviewCtl.firstName.value =
                              profileCtl.profile.value.firstname.toString();
                          webviewCtl.lastName.value =
                              profileCtl.profile.value.lastname.toString();

                          // print(
                          //     "phoneEncode => ${webviewCtl.phoneEncode.value}");
                          // print(
                          //     "firstName => ${webviewCtl.firstName.value}");
                          // print("lastName => ${webviewCtl.lastName.value}");

                          await Navigator.of(context)
                              .push(MaterialPageRoute(
                            builder: (context) => const WebViewPage(),
                          ));
                        } catch (e) {
                          print('Navigation error: $e');
                        }
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: Get.width * 0.6,
                            margin: const EdgeInsets.only(
                                top: 5, bottom: 5, right: 8),
                            padding:
                            const EdgeInsets.only(left: 15, right: 15),
                            decoration: const BoxDecoration(
                              border: Border(
                                left: BorderSide(
                                    width: 1, color: Color(0xFFE8E6E2)),
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Image.asset(
                                      'assets/image/icon-likepoint.png',
                                      width: 20,
                                    ),
                                    const SizedBox(width: 5),
                                    Container(
                                      height: 30,
                                      width: 1,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                        BorderRadius.circular(5),
                                        color: Colors.grey.withOpacity(0.5),
                                      ),
                                    ),
                                    const SizedBox(width: 5),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8),
                                            child: Obx(() =>
                                                AppWidget.boldTextS(
                                                  context,
                                                  webviewCtl
                                                      .balanceLikePoint
                                                      .value,
                                                  14,
                                                  const Color(0xFF282828),
                                                  FontWeight.w700,
                                                )),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8),
                                            child: Obx(() =>
                                                AppWidget.boldTextS(
                                                  context,
                                                  "= ${webviewCtl.balanceLikePointTHB.value} บาท",
                                                  10,
                                                  const Color(0xFF895F00),
                                                  FontWeight.w500,
                                                )),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: Get.width * 0.15,
                            margin: const EdgeInsets.only(right: 5),
                            alignment: Alignment.centerRight,
                            child: Image.asset(
                              'assets/icon/To-Right.png',
                              width: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    )
        : Container(
      // width: Get.width,
        height: 140,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Colors.white, Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x0C000000),
              blurRadius: 10,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // TODO :: SCAN QR CODE
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: InkWell(
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      onTap: () async {
                        var value =
                        await Get.to(() => const GetQrCodePage());
                        if (value != null) {
                          setState(() {
                            AppLoader.loader(context);
                          });
                          var responseEvent =
                          await AppApi.getEventRegisterByID(
                              context, value);
                          if (responseEvent["status"] == 200) {
                            Get.to(() => DetailEventRegisterPage(
                                responseEvent["result"]));
                          }
                        }
                      },
                      child: SvgPicture.asset(
                          "assets/image/home/<USER>")),
                ),
                // TODO :: SCAN QR CODE

                // TODO :: LIKEWALLET
                Container(
                  // width: 145,
                  // margin: const EdgeInsets.only(top: 5, bottom: 5, right: 8),
                  // padding: const EdgeInsets.only(left: 15, right: 15),
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(
                          width: 1, color: Color(0xFFE8E6E2)),
                      // right: BorderSide(width: 1, color: Color(0xFFE8E6E2)),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // Image.asset(
                          //   'assets/image/home/<USER>',
                          //   width: 16,
                          // ),

                          Obx(
                                () => profileCtl.token.value != null
                                ? likePointCtl.likePoint.value != 0.00
                                ? AppWidget.boldTextS(
                                context,
                                " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                                14,
                                const Color(0xFF282828),
                                FontWeight.w700)
                                : AppWidget.boldTextS(
                                context,
                                " 0",
                                14,
                                const Color(0xFF282828),
                                FontWeight.w700)
                                : AppWidget.boldTextS(
                                context,
                                " 0",
                                14,
                                const Color(0xFF282828),
                                FontWeight.w700),
                          ),
                        ],
                      ),
                      Obx(
                            () => profileCtl.token.value != null
                            ? likePointCtl.likePoint.value != 0.00
                            ? AppWidget.boldTextS(
                            context,
                            " ${AppService.numberFormat(likePointCtl.likePoint.value / 100)} บาท",
                            10,
                            const Color(0xFF895F00),
                            FontWeight.w500)
                            : AppWidget.boldTextS(
                            context,
                            " 0",
                            10,
                            const Color(0xFF895F00),
                            FontWeight.w500)
                            : AppWidget.boldTextS(
                            context,
                            " 0",
                            11,
                            const Color(0xFF895F00),
                            FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                // TODO :: LIKEWALLET

                SizedBox(width: Get.width * 0.07),
                // // TODO :: LIKEPOINT 2.0
                webviewCtl.statusMigrate.isTrue
                    ? InkWell(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  onTap: () async {
                    try {
                      print("click");
                      centerCtl.changeCloseHead(true);
                    } catch (e) {
                      // Handle any exceptions or errors here
                      print('Navigation error: $e');
                    }
                  },
                  child: Container(
                    width: 125,
                    height: 43,
                    alignment: Alignment.centerRight,
                    margin:
                    const EdgeInsets.only(top: 5, bottom: 5),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/image/icon-likepoint.png',
                          width: 25,
                        ),
                        const SizedBox(width: 10),
                        const Column(
                          mainAxisAlignment:
                          MainAxisAlignment.center,
                          children: [
                            Text(
                              "อัปเกรด",
                              style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 13,
                                  fontFamily: "Prompt-Medium",
                                  fontWeight: FontWeight.w700),
                            ),
                            Text(
                              "PMSPoint",
                              style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 12,
                                  fontFamily: "Prompt-Medium",
                                  height: 0.8,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                )
                    : Container()
                // // TODO :: LIKEPOINT 2.0
              ],
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    margin: const EdgeInsets.only(left: 16, right: 16),
                    child: const Divider(
                      color: Color(0xFFE8E6E2),
                      thickness: 1,
                    )),
                Container(
                  margin: const EdgeInsets.only(left: 16, right: 16),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        clipBehavior: Clip.antiAlias,
                        decoration: const BoxDecoration(),
                        child: Image.asset(
                          'assets/image/info-red.png',
                          height: 10,
                        ),
                      ),
                      const SizedBox(width: 6),
                      AppWidget.boldText(
                          context,
                          "ประกาศปรับรูปแบบคะแนนสะสมใหม่!",
                          14,
                          const Color(0xFF664701),
                          FontWeight.w500),
                    ],
                  ),
                ),
                Container(
                    margin: const EdgeInsets.only(left: 16, right: 16),
                    width: Get.width,
                    child: AppWidget.normalText(
                        context,
                        "สร้างประสบการณ์กับคะแนนสะสมรูปแบบใหม่ และสิทธิประโยชน์\nที่มากกว่าเดิม ที่นี่เร็วๆ นี้",
                        12,
                        const Color(0xBF1A1818),
                        FontWeight.w500)),
              ],
            )
          ],
        ))
        : Container(
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Colors.white, Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          shadows: const [
            BoxShadow(
              color: Color(0x0C000000),
              blurRadius: 10,
              offset: Offset(0, 4),
              spreadRadius: 0,
            )
          ],
        ),
        height: 65,
        alignment: Alignment.center,
        child: Skeletonizer.zone(
            child: ListTile(
              leading: Bone.circle(size: 40),
              title: Bone.text(words: 3),
              subtitle: Bone.text(),
            )));
  }

  Widget chatInApp() {
    if (profileCtl.token.value != null) {
      return Obx(() => Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 8,
              right: 8,
            ),
            child: InkWell(
              hoverColor: Colors.transparent,
              onTap: () async {
                if (chatInAppCtl.createGroup.isFalse) {
                  // await chatInAppCtl.checkRegister(context);
                  var ckSubRegister = await chatInAppCtl.firstCheckTG();

                  if (ckSubRegister == 'false') {
                    await chatInAppCtl.sendOTPTG(context);
                  } else if (ckSubRegister == "success") {
                    // Get.find<NotifyController>().delNotiChat();
                    Get.to(() => const WebViewTelegram());
                    return;
                  }
                }

                ///
              },
              child: Container(
                width: Get.width,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0.0, 1.0],
                    colors: chatInAppCtl.createGroup.isFalse
                        ? [
                      const Color(0xFFFFFFFF),
                      const Color(0xFFF8F8F8),
                    ]
                        : [
                      const Color(0xFF000000).withOpacity(0.2),
                      const Color(0xFF000000).withOpacity(0.2),
                    ],
                  ),
                ),
                child: chatInAppCtl.createGroup.isFalse
                    ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icon/contact_icon.png',
                      width: 30,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.boldTextS(
                        context,
                        "พูดคุยกับประชากิจฯ ",
                        14,
                        const Color(0xFF282828),
                        FontWeight.w500),
                    AppWidget.normalTextS(context, "ได้ที่นี่", 14,
                        const Color(0xFF905F00), FontWeight.w400)
                  ],
                )
                    : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icon/contact_icon.png',
                      width: 30,
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    AppWidget.boldTextS(
                        context,
                        "กำลังสร้างกลุ่ม กรุณารอสักครู่",
                        14,
                        const Color(0xFF282828),
                        FontWeight.w500),
                    const SizedBox(
                      width: 10,
                    ),
                    Countdown(
                      seconds: 90,
                      build: (BuildContext context, double time) =>
                          Text(
                            time.toString(),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF905F00),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                      interval: const Duration(milliseconds: 1000),
                      onFinished: () {
                        chatInAppCtl.createGroup.value = false;
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ));
    } else {
      return const SizedBox();
    }
  }

  Widget migration() {
    return AnimatedContainer(
        width: Get.width,
        duration: const Duration(milliseconds: 300),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Color(0xFFFFFFFF), Color(0xFFF3F3F3)],
          ),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(20),
            bottomRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.only(
          left: Get.width * 0.05,
          right: Get.width * 0.05,
        ),
        child: Obx(() => Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              height: Get.height * 0.055,
            ),
            GestureDetector(
              onTap: () {
                webviewCtl.checkMigrate();
                centerCtl.changeCloseHead(false);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Text(
                    'ปิดหน้านี้',
                    style: TextStyle(
                      color: Color(0xFF282828),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  ClipOval(
                    child: Container(
                      width: 26,
                      height: 26,
                      color: Colors.white,
                      child: Icon(
                        Icons.close,
                        color: Colors.black,
                        size: 15,
                      ),
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: Get.height * 0.01,
            ),
            Container(
              width: Get.width * 0.9,
              height: 70,
              decoration: ShapeDecoration(
                gradient: const LinearGradient(
                  begin: Alignment(0.00, -1.00),
                  end: Alignment(0, 1),
                  colors: [Colors.white, Color(0xFFF7F7F7)],
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                shadows: const [
                  BoxShadow(
                    color: Color(0x0C000000),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // TODO :: LIKEWALLET
                  Container(
                    width: 145,
                    margin:
                    const EdgeInsets.only(top: 5, bottom: 5, right: 8),
                    padding: const EdgeInsets.only(left: 15, right: 15),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Image.asset(
                              'assets/image/home/<USER>',
                              width: 16,
                            ),
                            Obx(
                                  () => profileCtl.token.value != null
                                  ? likePointCtl.likePoint.value != 0.00
                                  ? AppWidget.boldTextS(
                                  context,
                                  " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                                  14,
                                  const Color(0xFF282828),
                                  FontWeight.w700)
                                  : AppWidget.boldTextS(
                                  context,
                                  " 0",
                                  14,
                                  const Color(0xFF282828),
                                  FontWeight.w700)
                                  : AppWidget.boldTextS(
                                  context,
                                  " 0",
                                  14,
                                  const Color(0xFF282828),
                                  FontWeight.w700),
                            ),
                          ],
                        ),
                        Obx(
                              () => profileCtl.token.value != null
                              ? likePointCtl.likePoint.value != 0.00
                              ? AppWidget.boldTextS(
                              context,
                              "${AppService.numberFormat(likePointCtl.likePoint.value / 100)} บาท",
                              10,
                              const Color(0xFF895F00),
                              FontWeight.w500)
                              : AppWidget.boldTextS(
                              context,
                              " 0",
                              10,
                              const Color(0xFF895F00),
                              FontWeight.w500)
                              : AppWidget.boldTextS(context, " 0", 11,
                              const Color(0xFF895F00), FontWeight.w500),
                        ),
                      ],
                    ),
                  ),
                  // TODO :: LIKEWALLET

                  // // TODO :: LIKEPOINT 2.0
                  Container(
                    width: 125,
                    height: 43,
                    alignment: Alignment.centerRight,
                    margin:
                    const EdgeInsets.only(top: 5, bottom: 5, right: 8),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/image/icon-likepoint.png',
                          width: 25,
                        ),
                        const SizedBox(width: 10),
                        const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "อัปเกรด",
                              style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 13,
                                  fontFamily: "Prompt-Medium",
                                  fontWeight: FontWeight.w700),
                            ),
                            Text(
                              "PMSPoint",
                              style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 12,
                                  fontFamily: "Prompt-Medium",
                                  height: 0.8,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  // // TODO :: LIKEPOINT 2.0
                ],
              ),
            ),
            SizedBox(
              height: Get.height * 0.02,
            ),
            webviewCtl.isSuccess.isFalse
                ? Column(
              children: [
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    'อัปเกรดคะแนนเวอร์ชั่นใหม่!',
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: Color(0xFF2B1710),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(
                  height: Get.height * 0.01,
                ),
                SizedBox(
                  width: Get.width,
                  child: RichText(
                    text: const TextSpan(
                      children: [
                        TextSpan(
                          text: 'สร้างประสบการณ์ใหม่กับ ',
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: "Prompt",
                            fontSize: 13,
                          ),
                        ),
                        TextSpan(
                          text: 'PMSPoint',
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: "Prompt-medium",
                            fontWeight: FontWeight.w400,
                            fontSize: 13,
                          ),
                        ),
                        TextSpan(
                          text:
                          ' (พีเอ็มเอสพอยท์) \nอัปเกรดคะแนนเดิมของคุณ เพื่อรับสิทธิประโยชน์ที่มากกว่า \nร่วมสนุกและทำกิจกรรมกับ ประชากิจฯ ได้แล้ววันนี้',
                          style: TextStyle(
                            color: Colors.black,
                            fontFamily: "Prompt",
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: Get.height * 0.01,
                ),
              ],
            )
                : Container(
                child: Row(
                  children: [
                    SvgPicture.asset('assets/image/check_ring_round.svg'),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      "อัปเกรดคะแนนสำเร็จ!",
                      style: TextStyle(
                        color: Color(0xFF2B1710),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                )),
            webviewCtl.isUpgrading.isFalse
                ? Column(
              children: [
                InkWell(
                  onTap: () {
                    AppService.launchUrl(
                        'https://www.agilesoftgroup.com/pms_term.html');
                  },
                  child: SizedBox(
                    width: Get.width,
                    child: const Text(
                      'เงื่อนไข:',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: Get.width,
                  child: const Text(
                    '  1. คะแนนจะเปลี่ยนชื่อใหม่เป็น PMSPoint \n  2. จะสามารถใช้ได้เฉพาะบนแอปประชากิจฯ เท่านั้น',
                    maxLines: null,
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 13,
                      fontFamily: "Prompt",
                    ),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: webviewCtl.isChecked.value,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5)),
                      activeColor: Color(0xFFFFC700),
                      side: BorderSide(
                          width: 2, color: Color(0xFFFFC700)),
                      onChanged: (bool? value) {
                        webviewCtl.checkAccept(value!);
                      },
                    ),
                    const Text(
                      'ยอมรับข้อกำหนด',
                      style: TextStyle(
                        color: Colors.black,
                        fontFamily: "Prompt",
                        fontSize: 13,
                      ),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                    const Text(
                      'เงื่อนไขการให้บริการ',
                      style: TextStyle(
                        decoration: TextDecoration.underline,
                        color: Colors.black,
                        fontFamily: "Prompt",
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ],
            )
                : const SizedBox(),
            webviewCtl.isSuccess.isFalse
                ? Stack(
              children: [
                InkWell(
                  onTap: () async {
                    if (webviewCtl.isChecked.isTrue) {
                      await webviewCtl.upgradeLikePoint();
                    }
                  },
                  child: Container(
                    width: Get.width * 0.9,
                    height: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                    alignment: Alignment.center,
                    child: webviewCtl.isUpgrading.isFalse
                        ? const Text(
                      "อัปเกรดคะแนน",
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontFamily: "Prompt-medium",
                        fontWeight: FontWeight.w400,
                      ),
                    )
                        : Row(
                      mainAxisAlignment:
                      MainAxisAlignment.center,
                      children: [
                        const Text(
                          "กำลังอัปเกรด",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                            fontFamily: "Prompt-medium",
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        LoadingAnimationWidget.waveDots(
                          color: Color(0xFFFFB800),
                          size: 30,
                        ),
                        // CircularProgressIndicator(
                        //   color: Color(0xFFFFB800),
                        // )
                      ],
                    ),
                  ),
                ),
                webviewCtl.isChecked.isFalse
                    ? Container(
                  width: Get.width * 0.9,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Color(0x85FFFFFF),
                    borderRadius: BorderRadius.circular(10),
                  ),
                )
                    : const SizedBox(),
              ],
            )
                : const SizedBox(),
            SizedBox(
              height: Get.height * 0.02,
            ),
          ],
        )));
  }
}