import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/car_catalog.dart';
import 'package:mapp_prachakij_v3/model/promotion.dart';

class PromotionController extends GetxController {

  var isLoading = true.obs;

  PromotionList promotionList = PromotionList();
  CarCatalogList carCatalogList = CarCatalogList();

  RxString url = "".obs;
  RxString urlEvent = "".obs;

  @override
  void onInit() async {
    super.onInit();
    await getURL();
    await getPromotion();
    update();
  }

  getPromotion() async {
    try {
      isLoading.value = true;
      final responsePromotion = await AppApi.callAPIjwt("GET", AppUrl.getPromotions, {});
      var status = responsePromotion['status'];
      if(status == 200){
        promotionList = PromotionList.fromJson(responsePromotion["result"]);
      } else {
        promotionList = PromotionList(data: []);
      }
      await getCarCatalog();
      isLoading.value = false;
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> getCarCatalog() async {
    try {
      final responseCarCatalog = await AppApi.callAPIjwt("GET", AppUrl.getCatalogs, {});
      if(responseCarCatalog['status'] == 200){
        carCatalogList = CarCatalogList.fromJson(responseCarCatalog["result"]);
      } else {
        carCatalogList = CarCatalogList(data: []);
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getFeelWell =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getURL() async {
    try{
      var res = await FireStore.getPictureURL();
      url.value = res[0]["URL_IMG"];
      urlEvent.value = res[0]["URL_EVENT"];
    }catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

}