
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class ReferCodeController extends GetxController {

  var storage = GetStorage();
  final SecureStorage secureStorage = SecureStorage();
  var isLoading = true.obs;


  updateRefCodeMr() async{
    try{
      print('updateRefCodeMr');

      var usedPhone = await getLocalPhone();
      Map<String,dynamic> data = {
        "phone":usedPhone
      };
      // print('DataupdateRefCodeMr: $data');
      final response = await AppApi.callAPIjwt('POST', AppUrl.UpdateRefCode, data);
      // print('Raw Response: $response');
      if(response["status"] == 200){
        print('Update Ref Code Success');
      }
      else{
        print('Update Ref Code Failed');
      }
    }catch(e){
      print(e);
    }
  }

  Future<String?> getLocalPhone() async {
    var usedPhone = storage.read("phone_profile");
    if (usedPhone != null && usedPhone != "") {
      usedPhone;
    } else {
      final profileCtl = await Get.find<ProfileController>();
      usedPhone = profileCtl.profile.value.mobile;
    }
    return usedPhone;
  }

  getRefCode () async{
    try{
      print("getRefCode");
      isLoading(true);
      var userID = await secureStorage.readSecureData("userId");
      Map<String,dynamic> data = {
        "id":  userID
      };
      // print('Data getRefCode: $data');
      final response = await AppApi.callAPIjwt('POST', AppUrl.getRefCode, data);
      // print("อยู่ตรงนี้ ${response}");
      if(response["status"] == 200){
        print('getRefCode Success');
        // print('Response getRefCode: ${response["data"]}');
        // return response["data"];
      }
      else{
        print('getRefCode Failed');
      }
    }catch(e){
      print(e);
    }
  }
}