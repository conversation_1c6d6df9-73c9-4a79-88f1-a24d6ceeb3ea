import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/refer_code_controller.dart';

import '../../component/api.dart';
import '../../component/secureStorage.dart';
import '../../component/url.dart';
import '../../model/MR_model/MR.dart';
import '../../model/member.dart';
import 'likepoint_controller/likepoint_controller.dart';
import 'login_controller/agreement_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';


class ProfileController extends GetxController with GetTickerProviderStateMixin {
  late TabController homeTabController;
  RxInt currentIndex = 0.obs;

  var isLoading = true.obs;
  RxInt lengthTab = 0.obs;
  final token = Rxn<String>();

  ResponseRankMR rankMR = ResponseRankMR();
  Rx<ResponseProfileAndMR> profile = ResponseProfileAndMR().obs;

  final SecureStorage secureStorage = SecureStorage();

  final likePointCtl = Get.put(LikePointController());
  final agreementCtl = Get.put(AgreementController());
  var storage = GetStorage();

  final refCodeClt = Get.put(ReferCodeController());
  @override
  void onInit() async {
    super.onInit();
    await getProfileAndMR();
    update();
  }

  void _handleTabSelection() {
    if (!homeTabController.indexIsChanging) {
      currentIndex.value = homeTabController.index;
      update();
    }
  }

  @override
  void onClose() {
    homeTabController.dispose();
    super.onClose();
  }


  clearProfile() async {
    try{
      isLoading.value = true;
      profile.value = ResponseProfileAndMR();
      secureStorage.deleteSecureData("userId");
      token.value = null;
      lengthTab.value = 3;
      homeTabController = TabController(vsync: this, length: lengthTab.value);
      homeTabController.addListener(_handleTabSelection);
      likePointCtl.likePoint.value = 0.00;
      update();
    } catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      Future.delayed(const Duration(milliseconds: 1000), () {
        isLoading.value = false;
      });
    }
  }

  //
  Future<dynamic> getProfileAndMR() async {
    try {
      print('เข้า getProfileAndMR');
      isLoading(true);
      var userID = await secureStorage.readSecureData("userId");
      if(userID == null){
        await clearProfile();
        return;
      }

      token.value = await secureStorage.readSecureData("accessToken");
      // print("check token");
      // print(token.value);
      if(token.value == null){
        lengthTab.value = 3;
      } else {
        lengthTab.value = 5;
      }
      homeTabController = TabController(vsync: this, length: lengthTab.value);
      homeTabController.addListener(_handleTabSelection);
      // storage.read("phone_profile").then((value) {
      //   print(value);
      //   if(value != null && value != ""){
      //     profile.value.mobile = value;
      //   }
      // });
      Member valueMember = Member.fromJson({
        "id": userID,
        // "id": "7777",
      });
      final response = await AppApi.post(AppUrl.getProfileAndMR, valueMember.toJson());
      print(response['result']['ref_code_mr']);
      profile = ResponseProfileAndMR.fromJson(response['result']).obs;
      // likePointCtl.getLikePoint(profile.value.mobile);
      await getRankMR(profile.value.mrCode.toString());
      if(profile.value.acceptAgreement == "N" || profile.value.acceptAgreement == ""){
        agreementCtl.acceptAgreement(profile.value.id, profile.value.mobile);
      }
      storage.write("phone_profile", profile.value.mobile);
      refCodeClt.updateRefCodeMr();
      refCodeClt.getRefCode();
      // storage.write("phone_profile", "0898098146");
      update();
    } catch(e){
      if (kDebugMode) {
        print('พังตรงนี้');
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      Future.delayed(const Duration(milliseconds: 500), () {
        isLoading.value = false;
      });
    }
  }

  Future<dynamic> getProfileEdit() async {
    try {
      isLoading(true);
      var userID = await secureStorage.readSecureData("userId");
      Member valueMember = Member.fromJson({
        "id": userID,
      });
      lengthTab.value = 5;
      homeTabController = TabController(vsync: this, length: lengthTab.value);
      homeTabController.addListener(_handleTabSelection);
      final response = await AppApi.post(AppUrl.getProfileAndMR, valueMember.toJson());
      profile = ResponseProfileAndMR.fromJson(response['result']).obs;
      update();
    } catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      Future.delayed(const Duration(milliseconds: 500), () {
        isLoading.value = false;
      });
    }
  }

  Future<dynamic> getRankMR(String idMR) async {
    try{
      GetRankMR valueGetRankMR = GetRankMR.fromJson({
        "MrCode": idMR
      });

      final responseRankMR = await AppApi.post(AppUrl.getRankReferralMR, valueGetRankMR.toJson());
      rankMR = ResponseRankMR.fromJson(responseRankMR['result']);
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> saveProfile(id, firstname,lastname,idcard,birthday,number,moo,road,tumbol,amphur,province,zipcode,email,location) async {
    try {
      String? address = "";

      if(number != ""){
        address += number;
      }
      if(moo != ""){
        address += " หมู่ $moo";
      }
      if(road != ""){
        address += " ถนน $road";
      }
      if(tumbol != ""){
        address += " ตำบล $tumbol";
      }
      if(amphur != ""){
        address += " อำเภอ $amphur";
      }
      if(province != ""){
        address += " จังหวัด $province";
      }
      if(zipcode != ""){
        address += " $zipcode";
      }

      SaveProfile valueSaveProfile = SaveProfile.fromJson({
        "id": id,
        "firstname": firstname,
        "lastname": lastname,
        "idcard": idcard,
        "birthday": birthday,
        "address": address,
        "number": number,
        "moo": moo,
        "road": road,
        "tumbol": tumbol,
        "amphur": amphur,
        "province": province,
        "zipcode": zipcode,
        "email": email,
        "location": location,
      });
      final response = await AppApi.post(AppUrl.saveProfileByID, valueSaveProfile.toJson());
      await getProfileAndMR();
      int status = response['status'];
      return status;
    } catch(e){
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
    }
  }
  void showProfileTutorial(BuildContext context, ScrollController controller) {
    final tutorialCtl = Get.find<TutorialController>();

    // ตรวจสอบว่า controller ไม่เป็น null ก่อนส่งไป
    if (controller != null) {
      print("Sending ScrollController to TutorialController");
      tutorialCtl.showTutorialPage(context, "profile", controller: controller);
    } else {
      print("ScrollController is null in ProfileController");
      tutorialCtl.showTutorialPage(context, "profile");
    }
  }
}