import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/version.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';

class CheckVersionAppController extends GetxController {

  final centerCtl = Get.put(SettingController());

  RxBool hms = false.obs;

  @override
  onInit() async {
    super.onInit();
    await getUpdate(Get.context!);
  }

  getUpdate(context) async {
    try {
      if (kIsWeb) {
      } else {
        if (centerCtl.os.value == "android") {
          // FlutterHmsGmsAvailability.isHmsAvailable.then((t) {
          //     hms.value = true;
          // });
          if (kDebugMode) {
            print(hms.value);
          }
          // print("เข้า android");
          // final versionInApp =  (await AppVersion.initPackageInfo()).toString();
          // final versionInStore = (await AppVersion.checkVersionStore()).toString();
          const find = '.';
          const replaceWith = '';

          // final versionInAppCheck = versionInApp.replaceAll(find, replaceWith);
          // final versionInStoreCheck = versionInStore.replaceAll(find, replaceWith);

          // var versionInAppCheckInt = int.parse(versionInAppCheck);
          // var versionInStoreCheckInt = int.parse(versionInStoreCheck);
          if(hms.value == true){
            print('เข้า huawei');
            //huawei
            // if (versionInAppCheckInt < versionInStoreCheckInt) {
            //   centerCtl.versionInStore.value = versionInStore;
            //   centerCtl.updateUrl.value = "https://appgallery.huawei.com/app/C103452403";
            //   // pop up Update Version
            //   AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
            // }
          }else{
            // android

            // centerCtl.versionInStore.value = versionInStore;
            centerCtl.updateUrl.value = "https://play.google.com/store/apps/details?id=com.prachakij.pms_app&hl=th&gl=US";
            AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
            // if (versionInAppCheckInt < versionInStoreCheckInt) {
            //   // pop up Update Version
            //   centerCtl.versionInStore.value = versionInStore;
            //   centerCtl.updateUrl.value = "https://play.google.com/store/apps/details?id=com.prachakij.pms_app&hl=th&gl=US";
            //   AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
            // }
          }
        }else if (centerCtl.os.value == "ios"){
          // print("เข้า ios");
          // final versionInApp =  (await AppVersion.initPackageInfo()).toString();
          // final versionInStore = (await AppVersion.checkVersionStore()).toString();
          // centerCtl.versionInStore.value = versionInStore;
          centerCtl.updateUrl.value = "https://apps.apple.com/th/app/prachakij/id1507877291";
          const find = '.';
          const replaceWith = '';

          // final versionInAppCheck = versionInApp.replaceAll(find, replaceWith);
          // final versionInStoreCheck = versionInStore.replaceAll(find, replaceWith);

          // var versionInAppCheckInt = int.parse(versionInAppCheck);
          // var versionInStoreCheckInt = int.parse(versionInStoreCheck);

          // if (versionInAppCheckInt < versionInStoreCheckInt) {
            // pop up Update Version
            AppWidget.showDialogPageSlide(context, const AlertUpdateVersionPage());
          // }
        }
      }
    } catch(e) {
      if (kDebugMode) {
        print("Error get version update => $e");
      }
    }
  }
}