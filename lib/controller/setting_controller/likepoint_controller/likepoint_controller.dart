import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/like_point_model/like_point.dart';

class LikePointController extends GetxController {

  ResponseLikePoint responseLikePoint = ResponseLikePoint();
  RxDouble likePoint = 0.00.obs;

  Future<dynamic> getLikePoint(phone) async {
    try {
      var phoneCode = '+66${phone.substring(1)}';
      Map data = {
        "phoneNumber": phoneCode
      };
      final response = await AppApi.postNormal(AppUrl.getBalanceByPhoneNumber, data);
      responseLikePoint = ResponseLikePoint.fromJson(response['result']);
      likePoint.value = responseLikePoint.totalBalance!;
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getLikePoint =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

}