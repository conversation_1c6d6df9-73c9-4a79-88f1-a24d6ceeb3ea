import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';

import '../../../component/firestore.dart';

class ViewGetLikePointController extends GetxController {

  final profileCtl = Get.put(ProfileController());
  final webLikepointCtl = Get.put(WebViewLikePointController());

  RxString runningPoi = "".obs;
  var datetime = "00-00-0000T00:00:00";
  RxBool canGetLikeShare = false.obs;
  RxBool getLikeWatchLoader = false.obs;
  RxInt timeOut = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    // await getTime();
    update();
  }

  getTime() async {
    String url = 'http://devdev.prachakij.com/nuiAPI/LDX/gettime.php';
    Map map = {"menu": "gettime"};

    final jsonResponse = await AppApi.postNormal(url, map);
    print(jsonResponse["date"]);
    if (jsonResponse["date"] != "") {
        datetime = jsonResponse["date"];
    } else {
      print("not get time");
    }
  }

  Future<dynamic>  checkCanGetLikePOI(runningAds, typePoi, typeAds) async {
    print('in checkCanGetLikePOI');
    var firestore = await FireStore.getIDpoi();
    print(firestore["watch"]);
    if (typePoi == "share") {
      runningPoi.value = firestore["share"];
    } else if (typePoi == "watch") {
      runningPoi.value = firestore["watch"];
    }

    Map map = {
      "phone": profileCtl.profile.value.mobile,
      "running_ads": runningAds,
      "type_ads": typeAds,
      "running_poi": runningPoi.value,
      "datecheck": AppService.convertDateTime(datetime, "DDB")
    };

    final jsonResponse =
    await AppApi.postNormal(AppUrl.checkCanGetLikePOI, map);
    if (jsonResponse["status"] == 200) {
      if (typePoi == "share") {
        canGetLikeShare.value = true;
      } else if (typePoi == "watch") {
        getLikeWatchLoader.value = true;
        timeOut.value = 10;
        // return timeOut;
      }
    } else {
      // AppAlert.snackBottom(context, "ได้รับ like จาก ADS นี้แล้วในวันนี้");
    }
  }

  Future<dynamic> getPOIReward(runningAds, typePoi, typeAds) async {

    print("getPOIReward");
    var firestore = await FireStore.getIDpoi();
    print(firestore["watch"]);
    if (typePoi == "share") {
      runningPoi.value = firestore["share"];
    } else if (typePoi == "watch") {
      runningPoi.value = firestore["watch"];
    }
    // runningPoi = 23; //ทดสอบ

    Map map = {
      "phone": profileCtl.profile.value.mobile,
      "running_ads": runningAds,
      "type_ads": typeAds,
      "running_poi": runningPoi.value
    };

    final jsonResponse =
    await AppApi.postNormal(AppUrl.insertTransactionPOI, map);

    if (jsonResponse["status"] == 200) {
      Map map = {
        "phone": "+66" + profileCtl.profile.value.mobile!.substring(1),
        "activityID": runningPoi.value,
        "firstName": profileCtl.profile.value.firstname,
        "lastName": profileCtl.profile.value.lastname,
        "merchantID": webLikepointCtl.merchantID.value
      };

      var jsonResponse = await AppApi.callAPIjwt("POST",AppUrl.payLikeLockPMSPoint, map);

      print('after payLikeLock');
      print(jsonResponse);
      if (typePoi == "watch") {
          getLikeWatchLoader.value = false;
      }
      if (jsonResponse["status"] == 200) {
        print("รับ PMSpoint สำเร็จ");
        // AppAlert.snackBottom(context, "รับ likepoint สำเร็จ");
        Get.snackbar("คุณได้รับ ${AppService.numberFormatNon0(jsonResponse['result']['data'][1]['amountPay'])} PMSpoint", "จากกิจกรรม ${jsonResponse['result']['data'][1]['activityName']}", colorText: Colors.white, snackPosition: SnackPosition.TOP, duration: Duration(seconds: 5));
      } else {
        // AppAlert.snackBottom(context, "รับ likepoint ผิดพลาด");
        Get.snackbar("PMSpoint", "รับ PMSpoint ผิดพลาด", colorText: Colors.white, snackPosition: SnackPosition.TOP, duration: Duration(seconds: 5));
      }
    } else {
      // AppAlert.snackBottom(context, "บันทึก Transaction ผิดพลาด");
      Get.snackbar("PMSpoint", "บันทึก Transaction ผิดพลาด", colorText: Colors.white, snackPosition: SnackPosition.TOP, duration: Duration(seconds: 5));
    }
  }
}