import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_prachakij_v3/view/chat_in_app/verify_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:timer_count_down/timer_count_down.dart';

import '../../component/alert.dart';
import '../../component/api.dart';
import '../../component/loader.dart';
import '../../component/url.dart';
import '../../component/widget.dart';
import '../../model/check_phone_exist.dart';
import '../../model/member.dart';
import '../../view/chat_in_app/webview_tg.dart';
import '../../view/login/login.dart';
import 'login_controller/login_controller.dart';
import 'login_controller/register_controller.dart';
import 'profile_controller.dart';

class ChatInAppController extends GetxController {

  InAppWebViewController? webViewCtrl;

  RxString token = "".obs;

  final profileCtl = Get.put(ProfileController());
  final loginCtl = Get.put(LoginController());
  final registerCtl = Get.put(RegisterController());

  RxString url = "".obs;

  final box = GetStorage();

  int useCount = 0;

  RxInt countDownCreateGroup = 90.obs;

  RxString typeVerify = "".obs;
  RxBool createGroup = false.obs;
  bool ckRegister = false;
  RxString phoneCode = "".obs;
  RxString machCH = "".obs;

  checkUse(context) async {
    String phone = profileCtl.profile.value.mobile.toString();
    phoneCode.value = phone.replaceFirst("0", "66");
    useCount = box.read('useCount') ?? 0;

    if(useCount == 0){
      AppLoader.loaderChat(context);
    } else {
      useCount = 1;
      box.write('useCount', 1);
    }
  }

  // checkRegister(context) async {
  //   String phone = profileCtl.profile.value.mobile.toString();
  //   phoneCode.value = phone.replaceFirst("0", "66");
  //   final response = await http.post(
  //     Uri.parse('https://telegram.agilesoftgroup.com/checkRegister'),
  //     body: {
  //       'phone': phoneCode.value,
  //     },
  //   );
  //   var data = jsonDecode(response.body);
  //   print(data);
  //   url.value = data["group_url"];
  //   if(url.value != null){
  //     Get.to(() => const WebViewTelegram());
  //   } else {
  //     print("ไปทำสมัคร");
  //     await sendOTPTG(context);
  //   }
  // }

  firstCheckTG() async {
    print("firstCheckTG");
      String phoneLoad = profileCtl.profile.value.mobile.toString();
      phoneCode.value = phoneLoad.replaceFirst("0", "66");
    String phone = phoneCode.value;
    phoneCode.value = phone.replaceAll("+", "");
    var body = {
      'phone': phoneCode.value,
    };
    print(body);
    final response = await http.post(
      Uri.parse('https://telegram.agilesoftgroup.com/checkRegister'),
      body: body,
    );


    var data = jsonDecode(response.body);

    try {
      if (data["status"] == "user_not_found") {
        // print("user_not_found");
        ckRegister = false;
        update();
        return "false";
      }
      if (data["group_id"] != null) {
        url.value = data["group_url"];
        ckRegister = true;
        // timer(0, true);
        update();
        return "success";
      }
      ckRegister = false;
      update();
      return "pending";
    } catch (e) {
      ckRegister = false;
      update();
    }
    print(ckRegister);
    // }
    return "false";
  }

  ///{status: error, error: 401: SESSION_REVOKED (caused by auth.AcceptLoginToken)}
  registerREVOKED(context) async {
    print("registerREVOKED");
    try {
      AppLoader.loader(context);
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/register');
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "");
      Map data = {
        "phone" : phoneCode,
        "countrycode" : "66"
      };
      var resSendOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resSendOTPTG.body);
      var status = resData["status"];
      if(status == "awaiting code"){
        AppLoader.dismiss(context);
        machCH.value = resData["machch_on"];
        loginCtl.page.value = "verify-tg";
        loginCtl.typeMenu.value = "connect-social-tg-revoke";
        AppWidget.showDialogPageSlide(context, const LoginPage());
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "ส่ง OTP ไม่สำเร็จ", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if(kDebugMode){
        print(e);
      }
    }
  }

  checkOTPTGConnectREVOKED(context) async {
    try {
      print("เข้า checkOTPTGConnectREVOKED");
      AppLoader.loader(context);
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess');
      Map data = {
        "code" : pinTGTextController.text,
        "f_name" : profileCtl.profile.value.firstname,
        "l_name" : profileCtl.profile.value.lastname,
        "phone" : phoneCode,
        "machch_on" : machCH.value,
        "bu" : "PMSgr",
        "type" : "revoke"
      };
      var resCheckOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resCheckOTPTG.body);
      if(resData['status'] == "otpfail"){
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "รหัส OTP ไม่ถูกต้อง", "ตกลง");
      } else if (resData['status'] == "error"){
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "เกิดข้อผิดพลาด\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
      } else {
        Get.back();
        AppLoader.dismiss(context);
        await restoreGramJS();
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  restoreGramJS() async {
    try {
      AppLoader.loader(Get.context!);
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/restoreGramjs');
      Map data = {
        "phone" : phoneCode,
        "bu" : "PMSgr"
      };
      var resSendOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resSendOTPTG.body);
    } catch (e) {
      if(kDebugMode){
        print(e);
      }
    }
  }

  //TODO :: STEP 1 for login telegram ==> Check User
  RxString userID_tg = "".obs;
  // checkUserTG(context) async {
  //   print('checkUserTG');
  //   try {
  //     AppLoader.loader(context);
  //     String phoneCode = loginCtl.phoneTextController.text.replaceFirst("0", "66");
  //     Uri url = Uri.parse('https://telegram.agilesoftgroup.com/checkRegister');
  //     Map data = {
  //       "phone" : phoneCode,
  //     };
  //     var resCheckUserTG = await http.post(url, body: data);
  //     final resData = jsonDecode(resCheckUserTG.body);
  //     if(resData["status"] == "user_not_found"){
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(context, "ไม่พบบัญชี TG", "กรุณาสมัคร", "ตกลง");
  //       loginCtl.page.value = "register";
  //     } else {
  //       userID_tg.value = resData["user_id"];
  //       // await getIDWithTG(context);
  //     }
  //   } catch (e){
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //   }
  // }
  //
  // newCheckProfileWithTG(context) async {
  //   try {
  //     AppLoader.loader(context);
  //     CheckPhone valueCheckPhone = CheckPhone.fromJson({
  //       "phone": loginCtl.phoneTextController.text,
  //     });
  //     final responseCheckProfile = await AppApi.post(AppUrl.checkPhoneExist, valueCheckPhone.toJson());
  //     var status = responseCheckProfile['status'];
  //     if(status == 200){
  //       await sendOTPTG(context);
  //     } else if(status == 203){
  //       AppLoader.dismiss(context);
  //       var res = await AppAlert.showNewAccept(context, "แจ้งเตือน", "เบอร์นี้เคยทำการสมัครสมาชิกแล้ว\nกรุณาล็อคอินเพื่อใช้งาน", "ตกลง");
  //       if(res){
  //         loginCtl.typeMenu.value = "login";
  //         loginCtl.page.value = "login";
  //       }
  //       return 200;
  //     } else {
  //       AppLoader.dismiss(context);
  //       print('statusCheckPhone ไม่สำเร็จ');
  //       return false;
  //     }
  //     update();
  //     return status;
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('$e => error checkProfile');
  //     }
  //   }
  // }

  //TODO :: STEP 1 for regis telegram ==> SendOTP
  sendOTPTG(context) async {
    try {
      // AppLoader.loader(context);
      showDialogStatus(context, "กำลังส่ง OTP กรุณารอ", 30);
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/register');
      String phoneCode = profileCtl.profile.value.mobile.toString().replaceFirst("0", "");

      Map data = {
        "phone" : phoneCode,
        "countrycode" : "66"
      };
      print(data);
      var resSendOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resSendOTPTG.body);
      var status = resData["status"];
      if(status == "awaiting code"){
        AppLoader.dismiss(context);
        machCH.value = resData["machch_on"];
        typeVerify.value = "otp";
        Get.to(() => const VerifyTG());
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "ส่ง OTP ไม่สำเร็จ", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if(kDebugMode){
        print(e);
      }
    }
  }

  //TODO :: STEP 2 for regis telegram ==> CheckOTP
  final TextEditingController pinTGTextController = TextEditingController();
  checkOTPTG(context) async {
    print('เข้านี่');
    try {
      AppLoader.loader(context);
      // String phoneCode = loginCtl.phoneTextController.text.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess');
      Map data = {};

      if(typeVerify.value == "otp"){
        data = {
          "code" : pinTGTextController.text,
          "f_name" : profileCtl.profile.value.firstname,
          "l_name" : profileCtl.profile.value.lastname,
          "phone" : phoneCode.value,
          "machch_on" : machCH.value,
          "bu" : "PMSgr",
        };
      } else {
        data = {
          "password" : pinTGTextController.text,
          "f_name" : profileCtl.profile.value.firstname,
          "l_name" : profileCtl.profile.value.lastname,
          "phone" : phoneCode.value,
          "machch_on" : machCH.value,
          "bu" : "PMSgr",
        };
      }
      var resCheckOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resCheckOTPTG.body);
      print(resData);
      pinTGTextController.clear();
      if(resData['status'] == "error" && resData['error']['error'] == "otpfail"){
        print('otp ไม่ถูกต้อง');
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "รหัส OTP ไม่ถูกต้อง", "ตกลง");
      } else if (resData['status'] == 'error' && resData['error']['error'] == 'awaiting password'){
        print('ติด 2 step');
        // AppLoader.dismiss(context);
        // typeVerify.value = "password";
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "กรุณาเปิดแอป Telegram\nเพื่อทำการปิด Two-Step Verification", "ตกลง");
        Get.to(() => const HomeNavigator());
      } else {
        AppLoader.dismiss(context);
        Get.to(() => const HomeNavigator());
        showDialogStatusCreateGroup(context, "กำลังสร้างกลุ่มรับใช้\nกรุณาเปิดแอปไว้", countDownCreateGroup.value);
        createGroup.value = true;
        await createGroupTG(context);
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  RxString userIDAPP = "".obs;
  // registerSocialTG(context) async {
  //   try {
  //     print('เข้า registerSocialTG');
  //     Map data = {
  //       "userID_tg" : "N",
  //       "mobile" : loginCtl.phoneTextController.text,
  //       "roleId" : "user",
  //       "firstname" : profileCtl.profile.value.firstname,
  //       "lastname" : profileCtl.profile.value.lastname,
  //       "create_user" : "Mapp"
  //     };
  //     final response = await AppApi.callAPIjwt("POST", AppUrl.registerTG, data);
  //     if(response["status"] != 200){
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "ไม่สามารถสมัครสมาชิกได้\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
  //       loginCtl.page.value = "login";
  //     } else {
  //       userIDAPP.value = response["result"]["lastInsertId"].toString();
  //       // await loginCtl.newCheckProfile(context);
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //   }
  // }

  //TODO :: STEP สร้างกลุ่ม TG
  createGroupTG(context) async {
    try {
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess_2');
      Map data = {
        "code" : pinTGTextController.text,
        "f_name" : profileCtl.profile.value.firstname,
        "l_name" : profileCtl.profile.value.lastname,
        "phone" : phoneCode,
        "machch_on" : machCH.value,
        "bu" : "PMSgr",
      };
      var resCheckOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resCheckOTPTG.body);
      if(resData['status'] == "success"){
        userID_tg.value = resData["user_id"];
        await updateIDTG();
      } else {
        print('สร้าง Group ไม่สำเร็จ');
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  updateIDTG() async {
    try {
      Map data = {
        "userID_tg" : userID_tg.value,
        "id" : userIDAPP.value
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.updateIDTG, data);
      if (kDebugMode) {
      }
      if(response["status"] == 200){
        print("updateIDTG success");
      } else {
        print("updateIDTG fail");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<void> showDialogStatus(context, text, sec) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.center,
          child: Container(
            width: 300,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.black,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.5),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 4), // changes position of shadow
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/icon/contact_icon.png',
                  height: 43.74,
                  width: 50,
                ),
                const SizedBox(height: 10),
                AppWidget.boldText(context, "แจ้งเตือนสถานะแชท", 14, Colors.black, FontWeight.w400),
                const SizedBox(height: 10),
                AppWidget.normalText(context, text, 14, Colors.black, FontWeight.w400),
                const SizedBox(height: 10),
                ///countdown
                Countdown(
                  seconds: sec,
                  build: (BuildContext context, double time) => Text(
                    time.toString(),
                    style: const TextStyle(
                      fontSize: 20,
                      color: Colors.red,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  interval: const Duration(milliseconds: 100),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> showDialogStatusCreateGroup(context, text, sec) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Material(
          color: Colors.transparent,
          child: Align(
            alignment: Alignment.center,
            child: Container(
              width: 300,
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 4), // changes position of shadow
                  ),
                ],
              ),
              alignment: Alignment.center,
              child: Stack(
                children: [
                  SizedBox(
                    width: 300,
                    height: 200,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/icon/contact_icon.png',
                          height: 43.74,
                          width: 50,
                        ),
                        const SizedBox(height: 10),
                        AppWidget.boldText(context, "แจ้งเตือนสถานะแชท", 14, Colors.black, FontWeight.w400),
                        const SizedBox(height: 10),
                        AppWidget.normalText(context, "กำลังสร้างกลุ่มรับใช้", 14, Colors.black, FontWeight.w400),
                        AppWidget.normalText(context, "กรุณาเปิดแอปไว้สักครู่", 14, Colors.black, FontWeight.w400),
                        const SizedBox(height: 10),
                        ///countdown
                        Countdown(
                          seconds: sec,
                          build: (BuildContext context, double time) =>
                          DefaultTextStyle(
                              style: const TextStyle(fontSize: 20, color: Colors.red, fontWeight: FontWeight.w400, fontFamily: 'Prompt'),
                              child: Text(time.toString(),)),
                          interval: const Duration(milliseconds: 100),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 300,
                    height: 200,
                    alignment: Alignment.topRight,
                    child: IconButton(
                      hoverColor: Colors.transparent,
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.close_rounded, color: Colors.black),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

}