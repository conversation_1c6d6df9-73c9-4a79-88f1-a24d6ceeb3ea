import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class SaveActivityController extends GetxController {

  RxInt status = 0.obs;

  final profileCtl = Get.put(ProfileController());

  saveActivity(menu,note1,note2) async {
    try {
      Map data = {
        "id" : profileCtl.profile.value.id,
        "menu" : menu,
        "note1" : note1,
        "note2" : note2,
        "type" : "Mapp"
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.saveActivityCF, data);
      status.value = response['status'];
      update();
    } catch (e) {
      print(e);
    }
  }
}