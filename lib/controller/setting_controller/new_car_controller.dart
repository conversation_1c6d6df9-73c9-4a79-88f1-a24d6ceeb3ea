import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/new_car_model.dart';

class NewCarController extends GetxController {

  RxBool isLoading = true.obs;
  RxInt status = 0.obs;

  final pageController = PageController();
  final profileCtl = Get.put(ProfileController());

  RxInt currentIndex = 0.obs;

  RxBool isShow = false.obs;

  @override
  void onInit() async {
    // TODO: implement onInit
    await getNewCar();
    update();
    super.onInit();
  }

  NewCar newCar = NewCar();

  getNewCar() async {
    try {
      Map data = {
        "phone" : profileCtl.profile.value.mobile,
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.newCar, data);
      status.value = response['status'];
      if(status.value == 200){
        newCar = NewCar.fromJson(response["result"]);
        for (var car in newCar.newCarList!) {
          if (car.statusApp == "successfully") {
            newCar.newCarList!.remove(car);
          }

          if (newCar.newCarList!.isEmpty) {
            status.value = 404;
            newCar = NewCar(newCarList: []);
          }
        }

      }
      isLoading.value = false;
      update();
    } catch (e) {
      print(e);
    }
  }

}