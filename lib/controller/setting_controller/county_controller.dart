import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';

class CountyController extends GetxController {
  RxList amphurs = [].obs;
  RxList tumbols = [].obs;

  RxString selectAmphur = "".obs;
  RxString selectTumbol = "".obs;

  var isLoading = true.obs;

  @override
  void onInit() async {
    super.onInit();
    getAmphur();
    update();
  }

  Future<dynamic> getAmphur() async {
    try {
      isLoading(true);
      Map data = {
        "province" : "จันทบุรี"
      };
      final resAmphurs = await AppApi.post(AppUrl.getAmphurs, data);
      if(resAmphurs["status"] == 200){
        final amphurNames = [];
        for (var i = 0; i < resAmphurs['result'].length; i++) {
          amphurNames.add(resAmphurs['result'][i]['name_th']);
        }
        amphurs.value = amphurNames;
      } else {
        amphurs.value = [];
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error promotion getAumphur =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }finally{
      isLoading(false);
    }
  }

  Future<dynamic> getTumbol() async {
    try {
      isLoading(true);
      Map data = {
        "amphur" : selectAmphur.value
      };
      final resAmphurs = await AppApi.post(AppUrl.getTumbol, data);
      if(resAmphurs["status"] == 200){
        final tumbolNames = [];
        for (var i = 0; i < resAmphurs['result'].length; i++) {
          String name = resAmphurs['result'][i]['name_th'];
          if (!name.contains('*')) {
            tumbolNames.add(name);
          }
        }
        tumbols.value = tumbolNames;
        print(tumbols.value);
      } else {
        tumbols.value = [];
      }
      update();
    } catch(e) {
      if (kDebugMode) {
        print("error promotion getTumbol => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
    }
  }

}