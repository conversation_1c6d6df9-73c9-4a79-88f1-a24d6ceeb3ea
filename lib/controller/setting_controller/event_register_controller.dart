import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../../component/url.dart';
import '../../component/api.dart';

class EventRegisterController extends GetxController {

  RxBool statusRegis = false.obs;

  checkEventRegister(idActivity, id) async {
    try {

      Map data = {
        "id_activity" : idActivity,
        "member" : id.toString(),
      };
      var response = await AppApi.callAPIjwt("POST", AppUrl.checkRegisterEvent, data);
      if (response['status'] == 200) {
        statusRegis.value = true;
      } else {
        statusRegis.value = false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
}