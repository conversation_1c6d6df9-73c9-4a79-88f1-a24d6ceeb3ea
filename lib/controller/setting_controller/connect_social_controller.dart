import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_prachakij_v3/view/login/login.dart';

import 'chat_in_app_controller.dart';

class ConnectSocialController extends GetxController {

  final profileCtl = Get.find<ProfileController>();
  final loginCtl = Get.put(LoginController());
  final chatInAppCtl = Get.put(ChatInAppController());

  connectSocialID(fieldIdConnect, field, idSocial, status) async {
    try {
      Map data = {
        "phone" : profileCtl.profile.value.mobile,
        "field_id_connect" : fieldIdConnect,
        "field" : field,
        "id_connect" : idSocial,
        "status" : status
      };
      print(data);
      var response = await AppApi.callAPIjwt("POST", AppUrl.connectIdSocial, data);
      print(response);
      if (response['status'] == 200) {

        return true;
      } else {
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: STEP 1 for update telegram ==> SendOTP
  RxString machCH = "".obs;

  sendOTPTG(context) async {
    try {
      AppLoader.loader(context);
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/register');
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "");
      Map data = {
        "phone" : phoneCode,
        "countrycode" : "66"
      };
      var resSendOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resSendOTPTG.body);
      var status = resData["status"];
      if(status == "awaiting code"){
        AppLoader.dismiss(context);
        machCH.value = resData["machch_on"];
        loginCtl.page.value = "verify-tg";
        loginCtl.typeMenu.value = "connect-social-tg";
        AppWidget.showDialogPageSlide(context, const LoginPage());
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "ส่ง OTP ไม่สำเร็จ", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if(kDebugMode){
        print(e);
      }
    }
  }

  RxString userIDTG = "".obs;
  checkOTPTGConnect(context) async {
    try {
      print("เข้า checkOTPTGConnect");
      AppLoader.loader(context);
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess');
      Map data = {
        "code" : chatInAppCtl.pinTGTextController.text,
        "f_name" : profileCtl.profile.value.firstname,
        "l_name" : profileCtl.profile.value.lastname,
        "phone" : phoneCode,
        "machch_on" : machCH.value,
        "bu" : "PMSgr",
      };
      var resCheckOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resCheckOTPTG.body);
      if(resData['status'] == "otpfail"){
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "รหัส OTP ไม่ถูกต้อง", "ตกลง");
      } else if (resData['status'] == "error"){
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "เกิดข้อผิดพลาด\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
      } else {
        Get.back();
        AppLoader.dismiss(context);
        await createGroupTG(context);
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  createGroupTG(context) async {
    try {
      print('เข้า createGroupTG');
      String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
      Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess_2');
      Map data = {
        "code" : chatInAppCtl.pinTGTextController.text,
        "f_name" : profileCtl.profile.value.firstname,
        "l_name" : profileCtl.profile.value.lastname,
        "phone" : phoneCode,
        "machch_on" : machCH.value,
        "bu" : "PMSgr",
      };
      print("data create group : $data");
      var resCheckOTPTG = await http.post(url, body: data);
      final resData = jsonDecode(resCheckOTPTG.body);
      if(resData['status'] == "success"){
        userIDTG.value = resData["user_id"];
        print(userIDTG.value);
        await connectSocialID("userID_tg", "tg_connect", userIDTG.value, "Y");
        AppAlert.showNewAccept(context, "สร้างกลุ่มสำเร็จ", "คุณสามารถใช้เมนู\nพูดคุยกับประชากิจได้แล้ว", "ตกลง");
      } else {
        AppLoader.dismiss(context);
        print('สร้าง Group ไม่สำเร็จ');
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  // sendOTPTGRevoke(context) async {
  //   try {
  //     AppLoader.loader(context);
  //     Uri url = Uri.parse('https://telegram.agilesoftgroup.com/register');
  //     String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "");
  //     Map data = {
  //       "phone" : phoneCode,
  //       "countrycode" : "66"
  //     };
  //     var resSendOTPTG = await http.post(url, body: data);
  //     final resData = jsonDecode(resSendOTPTG.body);
  //     var status = resData["status"];
  //     if(status == "awaiting code"){
  //       AppLoader.dismiss(context);
  //       machCH.value = resData["machch_on"];
  //       loginCtl.page.value = "verify-tg";
  //       loginCtl.typeMenu.value = "connect-social-tg";
  //       AppWidget.showDialogPageSlide(context, const LoginPage());
  //     } else {
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(context, "ส่ง OTP ไม่สำเร็จ", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
  //     }
  //   } catch (e) {
  //     if(kDebugMode){
  //       print(e);
  //     }
  //   }
  // }
  //
  // checkOTPTGConnectRevoke(context) async {
  //   try {
  //     print("เข้า checkOTPTGConnect");
  //     AppLoader.loader(context);
  //     String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
  //     Uri url = Uri.parse('https://telegram.agilesoftgroup.com/mainProcess');
  //     Map data = {
  //       "code" : registerCtl.pinTGTextController.text,
  //       "f_name" : profileCtl.profile.value.firstname,
  //       "l_name" : profileCtl.profile.value.lastname,
  //       "phone" : phoneCode,
  //       "machch_on" : machCH.value,
  //       "bu" : "PMSgr",
  //       "type" : "revoke"
  //     };
  //
  //     var resCheckOTPTG = await http.post(url, body: data);
  //     final resData = jsonDecode(resCheckOTPTG.body);
  //     if(resData['status'] == "otpfail"){
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "รหัส OTP ไม่ถูกต้อง", "ตกลง");
  //     } else if (resData['status'] == "error"){
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "เกิดข้อผิดพลาด\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
  //     } else {
  //       Get.back();
  //       AppLoader.dismiss(context);
  //       await restoreGramJS(context);
  //     }
  //   } catch (e){
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //   }
  // }
  //
  // restoreGramJS(context) async {
  //   String phoneCode = profileCtl.profile.value.mobile!.replaceFirst("0", "66");
  //   Uri url = Uri.parse('https://telegram.agilesoftgroup.com/restoreGramjs');
  //   Map data = {
  //     "phone" : phoneCode,
  //     "bu" : "PMSgr"
  //   };
  //   var resReStoreGramJS = await http.post(url, body: data);
  //   print(resReStoreGramJS);
  //
  // }

}