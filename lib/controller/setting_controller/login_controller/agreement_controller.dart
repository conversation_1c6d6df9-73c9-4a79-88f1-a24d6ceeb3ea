import 'dart:convert';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:mapp_prachakij_v3/component/url.dart';

import '../../../component/api.dart';

class AgreementController extends GetxController{
  RxBool accept = false.obs;


  acceptAgreement(userID, phone) async {
    try {
      print('เข้า acceptAgreement HCS');
      DateTime dateTimeObject = DateTime.now();
      Map body = {
        "topicId": "0.0.4310017",
        "data":{
          "id": userID,
          "phone": phone,
          "create_time": dateTimeObject.toIso8601String(),
          "system": "APP",
          "step": "ยอมรับข้อตกลงและเงื่อนไข APP:Prachakij"
        }
      };
      var bodyJson = jsonEncode(body);
      var response = await http.post(Uri.parse(AppApi.hcsUrl), body: bodyJson, headers: {
        "Content-Type": "application/json",
        "x-api-key": AppApi.hcsKey
      });
      final resData = jsonDecode(response.body);
      await setAgreement(userID);
    } catch (e){
      print(e);
    }
  }

  setAgreement(id) async {
    Map data = {
      "id" : id.toString()
    };
    var response = await AppApi.callAPIjwt("POST", AppUrl.updateAgreement, data);
    if(response['status'] == "200"){
      print('set agreement = Y');
    }
  }
}