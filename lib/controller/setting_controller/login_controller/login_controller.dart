import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:get/get.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../tutorial_controller.dart';
import 'agreement_controller.dart';
import '../../../component/alert.dart';
import '../../../component/api.dart';
import '../../../component/connect.dart';
import '../../../component/loader.dart';
import '../../../component/secureStorage.dart';
import '../../../component/url.dart';
import '../../../index.dart';
import '../../../model/check_phone_exist.dart';
import '../../../model/generator_token.dart';
import '../../../model/login_apple.dart';
import '../../../model/login_line.dart';
import '../../../model/member.dart';
import '../../../model/send_code.dart';
import '../../../model/verify_code.dart';
import '../../../view/home/<USER>';
import '../../service/service.dart';
import '../profile_controller.dart';

class LoginController extends GetxController {
  //new
  final TextEditingController phoneTextController = TextEditingController();
  final TextEditingController pinTextController = TextEditingController();
  //new

  final SecureStorage secureStorage = SecureStorage();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  RxString menu = "".obs;
  var isLoading = true.obs;
  ResponseCheckMember responseCheckMember = ResponseCheckMember();
  ResponseSendCode responseSendCode = ResponseSendCode();
  ResponseVerifyCode responseVerify = ResponseVerifyCode();
  ResponseGenToken responseGenToken = ResponseGenToken();
  ResponseSendOtp responseSendOTP = ResponseSendOtp();
  Line line = Line();
  Apple apple = Apple();

  final profileCtl = Get.put(ProfileController());
  final agreementCtl = Get.put(AgreementController());
  final tutorialCtl = Get.put(TutorialController());

  //new
  RxString typeMenu = "".obs;
  RxString page = "".obs;

  RxString showPhone = "".obs;
  //new

  @override
  void onInit() async {
    super.onInit();
    update();
  }

  Future<dynamic> genToken(context, String userId, String roleId) async {
    try {
      isLoading(true);
      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken =
          await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      update();
      return statusGen;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      isLoading(false);
    }
  }

  void saveTokenMessage(String phone) async {
    _firebaseMessaging.getToken().then((token) async {
      assert(token != null);

      Map data = {"phone": phone, "token": token};
      final response = await AppApi.post(AppUrl.saveNotifyToken, data);
      int statusCode = response["status"];

      if (statusCode == 200) {
        if (kDebugMode) {
          print("saveTokenMessage success");
        }
      } else {
        if (kDebugMode) {
          print('Unsuccessful save NotifyToken ');
        }
      }
    });
  }

  lineLogin(context) async {
    try {
      await LineSDK.instance.login(scopes: ["profile"]);
      final profile = await LineSDK.instance.getProfile();
      line = Line.fromJson(profile.data);
      var status = await loginWithLine(context, profile.userId);
      return status;
    } on PlatformException catch (e) {
      if (e.code != 'CANCEL') {
        AppAlert.showError(context, 'Error : lineLogin', 'ตกลง');
      }
    }
  }

  loginWithLine(
    context,
    lineID,
  ) async {
    try {
      AppLoader.loader(context);
      Map data = {"lineID": lineID};
      final response = await AppApi.post(AppUrl.checkMemberWithLine, data);
      int status = response["status"];

      if (status == 200) {
        var member = response['result'][0];
        var statusGenToken =
            await genToken(context, member['id'].toString(), member['roleId']);
        int statusToken = statusGenToken;
        if (statusToken == 200) {
          await AppService.setPref('bool', 'loginStatus', true);
          await secureStorage.writeSecureData(
              "userId", member['id'].toString());
          await secureStorage.writeSecureData(
              "accessToken", responseGenToken.accessToken.toString());
          await profileCtl.getProfileAndMR();
          saveTokenMessage(member['mobile']);
          Get.offAll(() => const IndexPage());
        } else {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(
              context, 'เข้าระบบไม่สำเสร็จ', 'กรุณาติดต่อทีมพัฒนา', 'ตกลง');
        }
      } else {
        AppLoader.dismiss(context);
        menu.value = "registerSocial";
        return status;
      }
    } catch (e) {
      AppAlert.showError(context, 'ERROR : loginWithLine', 'ตกลง');
    }
  }

  appleLogIn(context) async {
    try {
      if (kDebugMode) {
        print('join appleLogIn');
      }
      await SignInWithApple.isAvailable();
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      apple.userIdentifier = credential.userIdentifier;
      var status = await loginWithApple(context, credential.userIdentifier);
      return status;
    } catch (e) {
      AppService.sendError(context, e);
    }
  }

  loginWithApple(context, appleID) async {
    try {
      if (kDebugMode) {
        print('on loginWithApple');
      }
      AppLoader.loader(context);

      Map data = {"appleID": appleID};

      final response = await AppApi.post(AppUrl.checkMemberWithApple, data);
      int status = response["status"];
      if (status == 200) {
        var member = response['result'][0];
        Map getToken = {
          "userId": member['id'].toString(),
          "appId": "pmsMobile",
          "roleId": member['roleId']
        };
        final resToken = await AppApi.post(AppUrl.generatorToken, getToken);
        int statusToken = resToken['status'];
        if (statusToken == 200) {
          await AppService.setPref('bool', 'loginStatus', true);
          await secureStorage.writeSecureData(
              "userId", member['id'].toString());
          await secureStorage.writeSecureData(
              "accessToken", responseGenToken.accessToken.toString());
          await profileCtl.getProfileAndMR();
          saveTokenMessage(member['mobile']);
          Get.offAll(() => const HomeNavigator());
          AppLoader.dismiss(context);
          Get.offAll(() => const HomeNavigator());
        } else {
          AppLoader.dismiss(context);
          AppAlert.showNewAccept(
              context, 'เข้าระบบไม่สำเสร็จ', 'กรุณาติดต่อทีมพัฒนา', 'ตกลง');
        }
      } else {
        AppLoader.dismiss(context);
        menu.value = "registerSocial";
        return status;
      }
    } catch (e) {
      AppService.sendError(context, 'login With Apple ไม่ได้!');
    }
  }

//   facebookLogin(context) async {
//     try {
//
//       print("facebookLogin");
//
//       final result = await FacebookAuth.instance.login(); // by default we request the email and the public profile
// // or FacebookAuth.i.login()
//       if (result.status == LoginStatus.success) {
//         // you are logged
//         final  accessToken = result.accessToken!;
//
//         print("SUCCESS ==> SUCCESS ==> SUCCESS");
//         print(accessToken.token);
//         print(accessToken.userId);
//       } else {
//         print(result.status);
//         print(result.message);
//       }
//       // await LineSDK.instance.login(scopes: ["profile"]);
//       // final profile = await LineSDK.instance.getProfile();
//       // line = Line.fromJson(profile.data);
//       // var status = await loginWithLine(context, profile.userId);
//       // return status;
//     } on PlatformException catch (e) {
//       if (e.code != 'CANCEL') {
//         AppAlert.showError(context, 'Error : lineLogin', 'ตกลง');
//       }
//     }
//   }

  //TODO :: ส่ง OTP

  int _retryCount = 0;

  newSendOTP(context) async {
    print("newSendOTP called on web platform");
    try {
      AppLoader.loader(context);
      if (phoneTextController.text.length != 10) {
        return AppAlert.showNewAccept(
            context, "กรุณาตรวจสอบ", "เบอร์โทรของท่าน\nไม่ครบ 10 หลัก", "ตกลง");
      }
      String phone = "+66${phoneTextController.text.substring(1)}";
      Map sendCode = {
        "phone": phone,
        "from": "Prachakij",
      };
      print("Sending OTP request with data: $sendCode");
      final resSendOTP = await AppApi.post(AppUrl.requertOTP, sendCode);
      responseSendCode = ResponseSendCode.fromJson(resSendOTP);
      var status = resSendOTP["statusCode"];
      print("OTP response status: $status");

      if (status == 200) {
        page.value = "verify";
        AppLoader.dismiss(context);

        // ไม่ต้องนำทาง<lemmaี่ ปล่อยให้ controller <lemma่<lemmaใช้<lemmaเอง
        return true;
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(
            context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
        return false;
      }
    } catch (e) {
      print("Error sending OTP: $e");
      AppLoader.dismiss(context);
      return false;
    }
  }

  //TODO :: STEP 1 for login => check otp
  // newCheckOTP(context) async {
  //   print("newCheckOTP");
  //   try {
  //     AppLoader.loader(context);
  //     var phoneCode = "+66${phoneTextController.text.substring(1)}";
  //     VerifyCode valueVerify = VerifyCode.fromJson({
  //       "phone": phoneCode,
  //       "otpCode": pinTextController.text,
  //       "refCode": responseSendCode.refCode.toString(),
  //       "fromBU": "Prachakij",
  //     });
  //     final resVerify =
  //         await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
  //     responseVerify = ResponseVerifyCode.fromJson(resVerify);
  //     var status = resVerify['statusCode'];
  //     if (status == 200) {
  //       bool profileCheckSuccess = await newCheckProfile(context);
  //
  //       // เ่อม log เ่อมตรวจสอบ
  //       print("Profile check result: $profileCheckSuccess");
  //
  //       // ปิด loader ก่อนส่งค่า
  //       AppLoader.dismiss(context);
  //
  //       // ส่งค่า true เพื่อให้รู้ว่าตรวจสอบสำเร็จ
  //       return true;
  //     } else {
  //       AppLoader.dismiss(context);
  //       await AppAlert.showNewAccept(
  //           context, "แจ้งเตือน", "กรุณาตรวจสอบรหัส OTP ที่ท่านกรอก", "ตกลง");
  //       return false;
  //     }
  //   } catch (e) {
  //     print("Error checking OTP: $e");
  //     AppLoader.dismiss(context);
  //     return false;
  //   }
  // }

  // newCheckOTP(BuildContext context) async {
  //   print("Executing newCheckOTP...");
  //   try {
  //     AppLoader.loader(context);
  //
  //     String phoneCode = "+66${phoneTextController.text.substring(1)}";
  //     VerifyCode valueVerify = VerifyCode.fromJson({
  //       "phone": phoneCode,
  //       "otpCode": pinTextController.text,
  //       "refCode": responseSendCode.refCode.toString(),
  //       "fromBU": "Prachakij",
  //     });
  //
  //     print("Sending OTP verification request: ${valueVerify.toJson()}");
  //
  //     final resVerify = await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
  //     print("Received response: $resVerify");
  //
  //     AppLoader.dismiss(context);
  //
  //     responseVerify = ResponseVerifyCode.fromJson(resVerify);
  //
  //     var status = resVerify['statusCode'];
  //     if (status == 200) {
  //       print("OTP verified, checking profile...");
  //       bool profileCheckSuccess = await newCheckProfile(context);
  //       print("Profile check result: $profileCheckSuccess");
  //       return profileCheckSuccess;
  //     } else {
  //       print("OTP verification failed or returned an error.");
  //       await AppAlert.showNewAccept(context, "แจ้งเตือน", "กรุณาตรวจสอบรหัส OTP ที่ท่านกรอก", "ตกลง");
  //       return false;
  //     }
  //   } catch (e) {
  //     print("Error during OTP check: ${e.toString()}");
  //     AppLoader.dismiss(context);
  //     return false;
  //   }
  // }

  Future<bool> newCheckOTP(BuildContext context) async {
    print("Executing newCheckOTP...");
    try {
      AppLoader.loader(context);

      // 🔧 แก้ไขการจัดรูปแบบเบอร์โทร
      String rawPhone =
          phoneTextController.text.replaceAll(RegExp(r'[^0-9]'), '');
      print("🔍 Raw phone: $rawPhone");
      if (rawPhone.length < 9) {
        AppLoader.dismiss(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("กรุณากรอกเบอร์โทรให้ถูกต้อง")),
        );
        return false;
      }
      String phoneCode = "+66${rawPhone.substring(1)}";
      print("🔍 Phone code: $phoneCode");

      // 🔍 ตรวจสอบค่า refCode
      String refCode = responseSendCode.refCode?.toString() ?? "";
      print("🔍 RefCode: $refCode");
      print("🔍 OTP Code: ${pinTextController.text}");

      if (refCode.isEmpty) {
        AppLoader.dismiss(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("ไม่พบรหัสอ้างอิง (refCode)")),
        );
        return false;
      }

      VerifyCode valueVerify = VerifyCode.fromJson({
        "phone": phoneCode,
        "otpCode": pinTextController.text,
        "refCode": refCode,
        "fromBU": "Prachakij",
      });

      print("🔸 Sending OTP verification request: ${valueVerify.toJson()}");
      print("🔸 API URL: ${AppUrl.verifyOTP}");

      final resVerify =
          await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());

      AppLoader.dismiss(context);
      print("✅ Received response: $resVerify");

      responseVerify = ResponseVerifyCode.fromJson(resVerify);
      var status = resVerify['statusCode'];
      String message = resVerify['msg'] ?? 'ไม่มีข้อความ';
      print("Verification status code: $status");
      print("Verification message: $message");

      if (status == 200) {
        print("🎉 OTP verification successful.");
        bool profileCheckSuccess = await newCheckProfile(context);
        print("Profile check result: $profileCheckSuccess");
        return profileCheckSuccess;
      } else {
        String errorMessage =
            resVerify['message'] ?? 'เกิดข้อผิดพลาดในการยืนยัน OTP';
        print(
            "❌ OTP verification failed with status code: $status, message: $errorMessage");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
        return false;
      }
    } catch (e) {
      print("🚨 Error during OTP check: ${e.toString()}");
      AppLoader.dismiss(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง")),
      );
      return false;
    }
  }

  //TODO :: STEP 2 for login => check profile
  newCheckProfile(context) async {
    print('join newCheckProfile');
    try {
      CheckPhone valueCheckPhone = CheckPhone.fromJson({
        "phone": phoneTextController.text,
      });
      final responseCheckProfile =
          await AppApi.post(AppUrl.checkPhoneExist, valueCheckPhone.toJson());
      var status = responseCheckProfile['status'];
      if (status == 203) {
        responseCheckMember =
            ResponseCheckMember.fromJson(responseCheckProfile['result'][0]);
        newGenToken(context, responseCheckMember.id.toString(),
            responseCheckMember.roleId.toString());
        tutorialCtl.checkHomeStatus();
      } else if (status == 200) {
        AppLoader.dismiss(context);
        var res = await AppAlert.showNewAccept(
            context,
            "แจ้งเตือน",
            "เบอร์นี้ไม่เคยทำการสมัครสมาชิก\nกรุณาสมัครสมาชิกเพื่อใช้งาน",
            "ตกลง");
        if (res) {
          typeMenu.value = "register";
          page.value = "register";
        }
        return 200;
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(
            context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
        print('statusCheckPhone ไม่สำเร็จ');
        return false;
      }
      update();
      return status;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: STEP 3 for login => gen Token
  newGenToken(context, userId, roleId) async {
    try {
      print('join newGenToken');
      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken =
          await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      if (statusGen == 200) {
        typeMenu.value = "login";
        page.value = "login";
        await AppService.setPref('bool', 'loginStatus', true);
        await secureStorage.writeSecureData(
            "userId", responseCheckMember.id.toString());
        await secureStorage.writeSecureData(
            "accessToken", responseGenToken.accessToken.toString());
        //TODO :: STEP 4 for login => get Profile
        await profileCtl.getProfileAndMR();
        saveTokenMessage(responseCheckMember.mobile.toString());
        Future.delayed(Duration.zero, () {
          Get.offAll(() => const HomeNavigator());
        });
        AppLoader.dismiss(context);
        isLoading.value = false;
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "ข้อผิดพลาด",
            "สร้าง Token ไม่สำเร็จ\nลองใหม่ครั้ง", "ตกลง");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
}
