import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class VersionController extends GetxController {
  var currentVersion = "";

  @override
  void onInit() {
    super.onInit();
    getVersion();
  }

  Future<void> getVersion() async {
    try {
      var version = await FireStore.getCurrentVersion();
        currentVersion = version.toString();
      print("currentVersion");
      print(currentVersion);
      update();
    } catch (e) {
      AppService.sendError(e, 'ERROR : getVersion in Drawers');
    }
  }
}