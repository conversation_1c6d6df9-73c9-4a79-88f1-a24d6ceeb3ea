import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/SA.dart';

class SAController extends GetxController with GetTickerProviderStateMixin {

  late TabController saTabController;
  RxInt currentIndex = 0.obs;


  var isLoading = true.obs;

  SAList resSAHeadOffice = SAList();
  SAList resSANaYaiAm = SAList();
  SAList resSAKlung = SAList();
  SAList resSASoiDao = SAList();

  @override
  void onInit() async {
    super.onInit();
    saTabController = TabController(vsync: this, length: 4);
    saTabController.addListener(_handleTabSelection);
    await getSA();
    update();
  }

  void _handleTabSelection() {
    if (!saTabController.indexIsChanging) {
      currentIndex.value = saTabController.index;
      update();
    }
  }

  @override
  void onClose() {
    saTabController.dispose();
    super.onClose();
  }

  Future<dynamic> getSA() async {
    try {
      isLoading(true);

      SA valueSAHeadOffice = SA.fromJson({
        "branch": "สำนักงานใหญ่"
      });
      SA valueSANaYaiAm = SA.fromJson({
        "branch": "นายายอาม"
      });
      SA valueSAKlung = SA.fromJson({
        "branch": "ขลุง"
      });
      SA valueSASoiDao = SA.fromJson({
        "branch": "สอยดาว"
      });
      final responseSAHeadOffice = await AppApi.post(AppUrl.getSa, valueSAHeadOffice.toJson());
      resSAHeadOffice = SAList.fromJson(responseSAHeadOffice["result"]);
      final responseSANaYaiAm = await AppApi.post(AppUrl.getSa, valueSANaYaiAm.toJson());
      resSANaYaiAm = SAList.fromJson(responseSANaYaiAm["result"]);
      final responseSAKlung = await AppApi.post(AppUrl.getSa, valueSAKlung.toJson());
      resSAKlung = SAList.fromJson(responseSAKlung["result"]);
      final responseSASoiDao = await AppApi.post(AppUrl.getSa, valueSASoiDao.toJson());
      resSASoiDao = SAList.fromJson(responseSASoiDao["result"]);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error sc getEvent =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }finally{
      isLoading(false);
    }
  }
}