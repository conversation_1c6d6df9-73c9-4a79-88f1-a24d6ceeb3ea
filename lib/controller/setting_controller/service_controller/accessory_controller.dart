import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/member_accessory.dart';

class AccessoryController extends GetxController {

  var isLoading = true.obs;

  MemberAccessoryList memberAccessoryList = MemberAccessoryList();

  @override
  void onInit() async {
    super.onInit();
    await getMemberAccessory();
    update();
  }

  Future<dynamic> getMemberAccessory() async {
    try {
      print("getMemberAccessory");
      isLoading(true);
      MemberAccessory valueMemberAccessory = MemberAccessory.fromJson({
        "menu": "อุปกรณ์ประดับยนต์"
      });

      final responseMemberAccessory = await AppApi.post(AppUrl.getListMember, valueMemberAccessory.toJson());
      print("responseMemberAccessory => ${responseMemberAccessory.toString()}");
      memberAccessoryList = MemberAccessoryList.fromJson(responseMemberAccessory["result"]);
      print("memberAccessoryList => ${memberAccessoryList.toString()}");
      isLoading.value = false;
      update();
    }catch(e){
      if (kDebugMode) {
        print("error assessory getEvent =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }
}