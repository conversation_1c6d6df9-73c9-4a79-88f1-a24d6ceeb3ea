import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/member_pmg.dart';

class PMGController extends GetxController with GetTickerProviderStateMixin {
  late TabController pmgTabController;
  RxInt currentIndex = 0.obs;

  var isLoading = true.obs;

  MemberPMGList memberPMGList = MemberPMGList();

  @override
  void onInit() async {
    super.onInit();
    pmgTabController = TabController(vsync: this, length: 4);
    pmgTabController.addListener(_handleTabSelection);
    await getMemberPMG();
    update();
  }

  void _handleTabSelection() {
    if (!pmgTabController.indexIsChanging) {
      currentIndex.value = pmgTabController.index;
      update();
    }
  }

  @override
  void onClose() {
    pmgTabController.dispose();
    super.onClose();
  }

  Future<dynamic> getMemberPMG() async {
    try {
      isLoading(true);
      MemberPMG valueMemberPMG = MemberPMG.fromJson({
        "menu": "ศูนย์ซ่อมสีและตัวถัง"
      });
      final responseMemberPMG = await AppApi.post(AppUrl.getListMember, valueMemberPMG.toJson());
      memberPMGList = MemberPMGList.fromJson(responseMemberPMG["result"]);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error pmg getEvent =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }finally{
      isLoading(false);
    }
  }
}