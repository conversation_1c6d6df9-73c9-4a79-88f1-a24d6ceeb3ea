import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/service_model/appointment.dart';

class AppointmentController extends GetxController {

  RxDouble scrollParam = 0.00.obs;
  var isLoading = true.obs;
  RxList province = [].obs;

  RxString timeSelect = "".obs;

  RxString branchSelect = "".obs;

  RxString carSelect = "รถปิกอัพ".obs;
  RxString carRegSelect = "".obs;
  RxBool addCar = false.obs;
  RxBool editCar = false.obs;

  RxString provinceSelect = "".obs;
  RxBool openProvince = false.obs;
  RxBool showSaveButton = false.obs;

  var phoneController = TextEditingController();
  var carCharacterController = TextEditingController();
  var carNumberController = TextEditingController();

  CarRegList carRegList = CarRegList();

  final profileCtl = Get.find<ProfileController>();

  @override
  void onInit() async {
    super.onInit();
    await getCarReg();
    isLoading.value = false;
    update();
  }

  getCarReg() async {
    try {
      phoneController.text = profileCtl.profile.value.mobile.toString();
      Map data = {};
      final response = await AppApi.callAPIjwt("GET", "${AppUrl.getCarReg}${profileCtl.profile.value.mobile}", data);
      if(response["status"] == 200){
        carRegList = CarRegList.fromJson(response["result"]);
        update();
      } else {
        carRegList = CarRegList(data: []);
      }
      update();
      return;
    }catch(e){
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  createCarReg(context) async {
    try {
      AppLoader.loader(context);
      String carLicense = "";
      if(provinceSelect.value != "รถบรรทุก"){
        carLicense = "${carCharacterController.text} ${carNumberController.text} ${provinceSelect.value}";
      } else {
        carLicense = "${carNumberController.text} ${provinceSelect.value}";
      }
      Map data = {
        "car_user_phone": profileCtl.profile.value.mobile,
        "create_user": "MApp_PMS",
        "car_character": carCharacterController.text == "" ? "" : carCharacterController.text,
        "car_number": carNumberController.text,
        "car_province": provinceSelect.value,
        "car_license": carLicense,
        "car_engine_no": "",
        "car_type": carSelect.value
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.createCarReg, data);
      if(response["status"] == 200){
        carCharacterController.text = "";
        carNumberController.text = "";
        provinceSelect.value = "";
        await getCarReg();
        addCar.value = false;
      } else {
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
      AppLoader.dismiss(context);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error saveCarLicense => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  removeCarReg(context, index) async {
    try {
      AppLoader.loader(context);
      Map data = {};
      final response = await AppApi.callAPIjwt("DELETE", "${AppUrl.removeCarReg}$index", data);
      if(response["status"] == 200){
        carRegList.data!.removeWhere((carReg) => carReg.running == index);
        await getCarReg();
        update();
      } else {
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
      AppLoader.dismiss(context);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  getProvince() async {
    try {
      Map data = {};
      final resProvinces = await AppApi.post(AppUrl.getProvinces, data);
      if(resProvinces["status"] == 200){
        final provinceNames = [];
        for (var i = 0; i < resProvinces['result'].length; i++) {
          provinceNames.add(resProvinces['result'][i]['name_th']);
        }
        province.value = provinceNames;
      } else {
        province.value = [];
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getAumphur => $e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  popupBeforeCallAPI(context, String inputDateStr) async {
    try{
      DateTime inputDate = DateTime.parse(inputDateStr);
      String outputDateString = DateFormat("yyyy-MM-dd").format(inputDate);

      String preSet = outputDateString+" "+timeSelect.value;
      DateFormat format = DateFormat("yyyy-MM-dd H:mm");
      DateTime DateForCheck = format.parse(preSet);
      DateTime now = DateTime.now();
      if(DateForCheck.isBefore(now)){
        Fluttertoast.showToast(msg: "ไม่สามารถจองคิวในวันที่และเวลาที่ผ่านมาได้", backgroundColor: Colors.red, textColor: Colors.white, fontSize: 13.0);
        return false;
      }
      AppLoader.dismiss(context);
      // String outputDateString = DateFormat("yyyy-MM-dd").format(inputDate);
      var res = await AppAlert.showNewAccept(context, "ข้อมูลการจองคิว", "ชื่อลูกค้า : ${profileCtl.profile.value.fullname}\nทะเบียนรถ : ${carRegSelect.value}\nประเภทรถ : ${carSelect.value}\nหมายเลขโทรศัพท์ : ${phoneController.text}\nต้องการจองคิวที่สาขา : ${branchSelect.value}\nวันที่ : ${outputDateString}\nเวลา : ${timeSelect.value}", "ตกลง");
      if(res){
        AppLoader.loader(context);

        print("completed");
        return createAppointment(inputDateStr);
        // return null;
      }
      // return res;
    }catch(e){
      print(e);
      return null;
    }
  }

  createAppointment(date) async {
    try {
      if(phoneController.text == ""){
        phoneController.text = profileCtl.profile.value.mobile.toString();
        // return;
      }

      DateTime inputDate = DateTime.parse(date);
      String outputDateString = DateFormat("yyyy-MM-dd").format(inputDate);

      Map data = {
          "service_from" : "Mapp",
          "service_date_name" : profileCtl.profile.value.fullname,
          "service_date_license" : carRegSelect.value,
          "service_date_tel" : phoneController.text,
          "service_date_option" : "จองคิวศูนย์บริการ",
          "service_date_branch" : branchSelect.value,
          "service_date_latlng" : "",
          "service_date_location" : "",
          "service_date_date" : outputDateString,
          "service_date_time" : timeSelect.value,
          "service_date_requirement" : "",
          "service_car" : carSelect.value
      };
      final response = await AppApi.callAPIjwt("POST", AppUrl.createAppointment, data);
      var status = response["status"];
      update();
      return status;
    }catch(e){
      if (kDebugMode) {
        print("error getCarReg =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

}