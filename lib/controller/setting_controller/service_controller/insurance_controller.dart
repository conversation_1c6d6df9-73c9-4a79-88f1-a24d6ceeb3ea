import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/member_insurance.dart';

class InsuranceController extends GetxController{

  var isLoading = true.obs;

  MemberInsuranceList memberInsuranceList = MemberInsuranceList();

  @override
  void onInit() async {
    super.onInit();
    await getMemberInsurance();
  }

  Future<dynamic> getMemberInsurance() async {
    try {
      isLoading(true);
      MemberInsurance valueMemberInsurance = MemberInsurance.fromJson({
        "menu": "ทะเบียน ประกัน/พรบ."
      });
      final responseInsurance = await AppApi.post(AppUrl.getListMember, valueMemberInsurance.toJson());
      memberInsuranceList = MemberInsuranceList.fromJson(responseInsurance["result"]);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error insurance getEvent =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }finally{
      isLoading(false);
    }
  }
}