import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/SC.dart';

class SCController extends GetxController with GetTickerProviderStateMixin {

  late TabController scTabController;
  RxInt currentIndex = 0.obs;

  var isLoading = true.obs;

  SCList resSCHeadOffice = SCList();
  SCList resSCNaYaiAm = SCList();
  SCList resSCKlung = SCList();
  SCList resSCSoiDao = SCList();
  SCList resSCBigTruck = SCList();
  SCList resSCOnline = SCList();

  @override
  void onInit() async {
    super.onInit();
    scTabController = TabController(vsync: this, length: 6);
    scTabController.addListener(_handleTabSelection);
    await getSC();
    update();
  }

  void _handleTabSelection() {
    if (!scTabController.indexIsChanging) {
      currentIndex.value = scTabController.index;
      update();
    }
  }

  @override
  void onClose() {
    scTabController.dispose();
    super.onClose();
  }

  Future<dynamic> getSC() async {
    try {
      isLoading(true);
      SC valueSCHeadOffice = SC.fromJson({
        "branch": "สำนักงานใหญ่"
      });
      SC valueSCNaYaiAm = SC.fromJson({
        "branch": "นายายอาม"
      });
      SC valueSCKlung = SC.fromJson({
        "branch": "ขลุง"
      });
      SC valueSCSoiDao = SC.fromJson({
        "branch": "สอยดาว"
      });
      SC valueSCBigTruck = SC.fromJson({
        "branch": "รถใหญ่"
      });
      SC valueSCOnline = SC.fromJson({
        "branch": "SC ONLINE"
      });
      final responseSCHeadOffice = await AppApi.post(AppUrl.getSale, valueSCHeadOffice.toJson());
      resSCHeadOffice = SCList.fromJson(responseSCHeadOffice["result"]);
      final responseSCNaYaiAm = await AppApi.post(AppUrl.getSale, valueSCNaYaiAm.toJson());
      resSCNaYaiAm = SCList.fromJson(responseSCNaYaiAm["result"]);
      final responseSCKlung = await AppApi.post(AppUrl.getSale, valueSCKlung.toJson());
      resSCKlung = SCList.fromJson(responseSCKlung["result"]);
      final responseSCSoiDao = await AppApi.post(AppUrl.getSale, valueSCSoiDao.toJson());
      resSCSoiDao = SCList.fromJson(responseSCSoiDao["result"]);
      final responseSCBigTruck = await AppApi.post(AppUrl.getSale, valueSCBigTruck.toJson());
      resSCBigTruck = SCList.fromJson(responseSCBigTruck["result"]);
      final responseSCOnline = await AppApi.post(AppUrl.getSale, valueSCOnline.toJson());
      resSCOnline = SCList.fromJson(responseSCOnline["result"]);
      isLoading(false);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error SCController getSC =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }
}