import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/member_officeparts.dart';

class OfficeAndPartsController extends GetxController {

  var isLoading = true.obs;

  MemberOfficePartsList memberOfficePartsList = MemberOfficePartsList();

  @override
  void onInit() async {
    super.onInit();
    await getMemberOfficeParts();
    update();
  }

  Future<dynamic> getMemberOfficeParts() async {
    try {
      isLoading(true);
      MemberOfficeParts valueMemberOfficeParts = MemberOfficeParts.fromJson({
        "menu": "ศูนย์บริการและอะไหล่"
      });
      final responseOfficeParts = await AppApi.post(AppUrl.getListMember, valueMemberOfficeParts.toJson());
      memberOfficePartsList = MemberOfficePartsList.fromJson(responseOfficeParts["result"]);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error office getEvent =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }finally{
      isLoading(false);
    }
  }
}