import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class HomeServiceController extends GetxController {

  final profileCtl = Get.put(ProfileController());
  var phoneController = TextEditingController();

  RxList province = [].obs;
  RxList carList = [].obs;

  var isLoading = true.obs;

  @override
  void onInit() async {
    super.onInit();
    phoneController.text = profileCtl.profile.value.mobile.toString();
    isLoading.value = false;
    update();
  }

  createHomeService(carLicense, phone, latlng)async{
    try{
      Map data = {
        "service_date_name" : profileCtl.profile.value.fullname,
        "service_date_license" : carLicense,
        "service_date_tel" : phone,
        "service_date_option" : "บริการซ่อมถึงบ้าน",
        "service_date_branch" : "",
        "service_date_latlng" : latlng,
        "service_date_location" : "",
        "service_date_date" : "",
        "service_date_time" : "",
        "service_date_requirement" : ""
      };
      final res = await AppApi.callAPIjwt("POST", AppUrl.createHomeService, data);
      if (kDebugMode) {
        print(res);
      }
      return res["status"];
    }catch(e){
      if (kDebugMode) {
        print("Error home service ==> $e");
      }
    }
  }

}