import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/send_code.dart';
import 'package:mapp_prachakij_v3/model/verify_code.dart';

class VerifyESignController extends GetxController {

  ProfileController profileCtrl = Get.find<ProfileController>();
  // final TextEditingController pinTextController = TextEditingController();

  ResponseSendCode responseSendCode = ResponseSendCode();
  ResponseVerifyCode responseVerify = ResponseVerifyCode();

  RxBool redirectVerify = false.obs;

  //TODO :: ส่ง OTP
  newSendOTP(context) async {
    print("newSendOTP");
    try {
      AppLoader.loader(context);
      Map sendCode = {
        "phone": profileCtrl.profile.value.phoneFirebase,
        "from": "Prachakij",
      };

      final resSendOTP = await AppApi.post(AppUrl.requertOTP, sendCode);
      responseSendCode = ResponseSendCode.fromJson(resSendOTP);
      var status = resSendOTP["statusCode"];
      if(status == 200){
        redirectVerify.value = false;
        AppLoader.dismiss(context);
        return true;
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
        return false;
      }
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: STEP 1 for login => check otp
  newCheckOTP(context, otp) async {
    print("newCheckOTP");
    try {
      AppLoader.loader(context);

      VerifyCode valueVerify = VerifyCode.fromJson({
        "phone": profileCtrl.profile.value.phoneFirebase,
        "otpCode": otp,
        "refCode": responseSendCode.refCode.toString(),
        "fromBU": "Prachakij",
      });
      final resVerify = await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
      responseVerify = ResponseVerifyCode.fromJson(resVerify);
      var status = resVerify['statusCode'];
      if(status == 200){
        // await newCheckProfile(context);
        redirectVerify.value = true;
        update();
        Navigator.pop(context);

        return true;
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "แจ้งเตือน", "กรุณาตรวจสอบรหัส OTP ที่ท่านกรอก", "ตกลง");
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }
}