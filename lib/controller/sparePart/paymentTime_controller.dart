import 'dart:async';
import 'dart:typed_data';
import 'dart:ui';

import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_Order_controller.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

class PaymentTimer extends GetxController {
  int start = 0;
  Timer? _timer;
  int updateIndexShow = 0;
  var totalPay = 0;

  RxBool isLonding = false.obs;

  List<bool> proveCreate = [];
  RxBool isGenQr = false.obs;

  var qrData;
  // Function to initialize timer using current time and a timestamp from the API
  void initializeTimer() {
    // Get the current time in milliseconds
    DateTime now = DateTime.now();
    int currentTime = now.millisecondsSinceEpoch ;

    // Calculate the time difference in seconds
    start = currentTime;

    // print("currentTime");
    // print(currentTime);

    startTimer();
  }

  void startTimer() {
    if (_timer != null) {
      _timer!.cancel(); // Cancel any previous timer before starting a new one
    }
    int elapsed = 0; // Track the elapsed time since the last check

    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (start > 0) {
        start = start + 1000; // Increment 'start' by 1000ms (1 second)
        elapsed += 1000; // Increment the elapsed time

        if (elapsed >= 5000) {
          // Check every 5000ms
          if (proveCreate.contains(true)) {
            // if the QR code has been created, check the order status
            checkSparePartOrderStatus();
          }
          elapsed = 0; // Reset elapsed time after checking

        }

      } else {
        _timer!.cancel();
      }
      update(); // Call update method
    });
  }

  void updateIndex(int index) {
    updateIndexShow = index;
    update();
  }

  @override
  void onClose() {
    _timer?.cancel(); // Cancel the timer when the controller is disposed
    super.onClose();
  }

  // create qr payment
  Future<void> createQrPayment(orderID, point, pocketID, total, discount_amount) async {
    try {
      print("createQrPayment");
      var profileCtrl = Get.find<ProfileController>();
      final sparepartOrderCtl = Get.find<SparepartOrderController>();

      isLonding(true);
      update();
      if (point == ''){
        point = 0 ;
      }

      total = total.toInt();
      // Call the API to create a QR payment
      Map data = {
        "order_id": orderID.toString(),
        "point_user": point.toString(),
        "pocket_id" : pocketID.toString(),
        "phone_number" : profileCtrl.profile.value.mobile.toString(),
        "discount_amount": discount_amount,
        // "total_amount": total.toString()
        "total_amount": 1.25.toString() // for test
      };

      print(data);
      totalPay = total;

      var response = await AppApi.callAPIjwt("POST", "https://agilesoftgroup.com/PMS/online-spare-parts/gen-qr-code", data);
      // var response = await AppApi.callAPIjwt("POST", "https://f991-180-180-117-177.ngrok-free.app/PMS/online-spare-parts/gen-qr-code", data);

      print(response);
      if(response['status'] == 200) {
        // If the response status is 200, the QR code was successfully created
        //do something
        qrData = response['result']["qr_data"];

        proveCreate.add(true);

        await sparepartOrderCtl.updateDataSpecific(orderID, response['result']["qr_data"], "qr_data");
        final update = await sparepartOrderCtl.updateDataSpecific(orderID, response['result']["qr_code_expire"], "qr_code_expire");
        await sparepartOrderCtl.updateDataSpecific(orderID, response['result']["discount_points"], "discount_points");

        if(update){
          isLonding(false);
        }
      }
      update();

    } catch (e) {
      print(e);
    }
  }

  Future<void> saveByteDataAsPng(String qrData) async {
    // Request storage permission if needed
    var status = await Permission.storage.request();
    if (status.isGranted) {
      try {
        // Create a Pretty QR code image
        final qrImage = QrImage(

            QrCode.fromData(
              data: qrData,
              errorCorrectLevel: QrErrorCorrectLevel.H,
            )

        );

        // Convert QR image to ByteData as PNG (using ImageByteFormat.png)
        final byteData = await qrImage.toImageAsBytes(
          size: 512,
          format: ImageByteFormat.png,
          decoration: const PrettyQrDecoration(
            background: Color(0xFFFFFFFF),
          ),
        );

        // Convert ByteData to Uint8List for file saving
        Uint8List pngBytes = byteData!.buffer.asUint8List();

        final result = await ImageGallerySaver.saveImage(pngBytes, quality: 100, name: 'QRCodeMR_${DateTime.now().millisecondsSinceEpoch}.png');

        // print(result);
      } catch (e) {
        print('Error generating or saving the QR code image: $e');
      }
    } else {
      print('Storage permission denied');
    }
  }

  void checkSparePartOrderStatus() async {
    try{
      final sparepartOrderCtl = Get.find<SparepartOrderController>();
      // print("checkSparePartOrderStatus");
      for (var index = 0; index < sparepartOrderCtl.listOrder.length; index++) {
        final order = sparepartOrderCtl.listOrder[index];
        final orderId = order["order_id"];
        final qrCodeExpire = int.parse(order["qr_code_expire"] ?? '0');

        if (qrCodeExpire - start < 0) {
          // QR code has expired, continue to the next iteration
          continue;
        } else {
          // Call the API asynchronously without awaiting the result
          try{
            // print(orderId);
            var response = await AppApi.callAPIjwt("GET", "https://agilesoftgroup.com/PMS/online-spare-parts/update-payment-by-orderID/$orderId", {});

            // print(response);
            if (response["status"] != 200 || response == null) {
              // If response is 201, continue without blocking
              return;
            } else {

              proveCreate.removeAt(0);
              sparepartOrderCtl.updateListOrderWithIndex(index, response["result"]);
              // print(response);
              break;
            }
          }catch(e){

            print(e);
            return;
          }

        }
      }
    }catch(e){
      print(e);
    }
  }
}