import 'dart:async';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';
import 'package:mapp_prachakij_v3/model/sparePart/bagModel.dart';

class SparepartController extends GetxController {
  var product = {};
  var searchProductVar = {};
  RxList<BagProduction?> bagProduction = <BagProduction>[].obs;
  TextEditingController searchController = TextEditingController();
  RxBool showTab = true.obs;
  // var accessories = [];
  ShowProduction? showDetail = ShowProduction();
  var countCheckbox = 0;
  bool checkAll = false;
  List<String> keys = [];

  final storage = GetStorage();
  var isLoading = true.obs;

  var orderList = [];

  /// เดี๋ยวต้องใช้จาก API จริง

  @override
  void onInit() async {
    super.onInit();
    loadBagFromStorage();
  }

  Future<void> getData() async {
    try {
      print("getData");
      isLoading(true);  // Start loading


      // Fetch data
      var response = await AppApi.callAPIjwt(
        "GET",
        "${AppUrl.apiCF}/online-spare-parts/get-all-spare-parts",
        {},
      );

      if (response["status"] == 200) {
        product = response["result"];
        print("product $product");
      }
    } catch (e) {
      print("error");
      print(e);
    } finally {
      isLoading(false);  // Stop loading
    }
  }


  Future<void> countCheckboxFunc() async {
    var countInside = 0;

    // Loop through each element in bagProduction and check the 'checked' field
    bagProduction.forEach((element) {
      if (element!.checked == true) {
        countInside++;
      }
    });

    // Update the countCheckbox variable with the number of checked items
    countCheckbox = countInside;

    // Check if all items are checked
    checkAll = (countCheckbox == bagProduction.length);

    // Notify GetX to update the UI (uncomment if necessary)
    // update();
  }

  void changeShowTab (input) {
    showTab.value = input;
  }
  void changeCheckAll(input) {
    checkAll = input;
    update();
  }

  void addShowDetail(
      inputType, inputSubtype, inputIndex, stock, productName) async {

    // print("$inputType, $inputSubtype, $inputIndex, $stock, $productName");
    int OGindex = product[inputType][inputSubtype].indexWhere(
          (item) => item['product_name'] == productName,
    );

    if(OGindex == -1){
      OGindex = inputIndex;
    }

    showDetail = ShowProduction(
        type: inputType,
        subtype: inputSubtype,
        index: inputIndex,
        stock: stock,
        name: productName);
    // showDetail.value.subtype = inputSubtype;
    // showDetail.value.type = inputType;
    // showDetail.value.index = inputIndex;
    // showDetail.value.stock = stock;
    // showDetail.value.name = productName;
    update();
  }

  ///bag control
  Future<bool> addBag(String inputType, String inputSubtype, int inputIndex, int stock, String name) async {
    var sendBoolean = false;
    // Check if this is the first time (length is 0)
    if (bagProduction.isEmpty) {
      // Directly add the new BagProduction object
      bagProduction.add(
        BagProduction(
          type: inputType,
          subtype: inputSubtype,
          index: inputIndex,
          stock: stock,
          name: name,
          amount: 1,
          checked: false,
        ),
      );
      sendBoolean = true;
      print("Added first item: ${bagProduction[0].toString()}");
    } else {
      // Check if an item with the same type, subtype, index, and name already exists
      var indexRow = bagProduction.indexWhere((item) => item!.subtype == inputSubtype && item.type == inputType && item.name == name);

      if (indexRow != -1) {
        // Call the plusOne function instead of directly incrementing the amount
        sendBoolean = await plusOne(bagProduction[indexRow]!);
      } else {
        bagProduction.add(
          BagProduction(
            type: inputType,
            subtype: inputSubtype,
            index: inputIndex,
            stock: stock,
            name: name,
            amount: 1,
            checked: false,
          ),
        );
        print("Added new item: ${bagProduction[0].toString()}");
      }
    }

    saveBagToStorage();
    // Notify GetX to update the UI
    bagProduction.refresh();

    return sendBoolean;
  }

  Future<void> addBagThenPushToOrder(String inputType, String inputSubtype,
      int inputIndex, String name) async {

    BagProduction? existingItem;

    // Loop through the bagProduction to find the matching item
    for (var item in bagProduction) {
      if (item!.type == inputType &&
          item.subtype == inputSubtype &&
          item.index == inputIndex &&
          item.name == name) {
        existingItem = item;
        break; // Stop the loop if the item is found
      }
    }

    if (existingItem != null) {
      // If the item exists, mark it as checked
      existingItem.checked = true;
    } else {
      // If the item does not exist, add a new BagProduction object to the list
      bagProduction.add(
        BagProduction(
          type: inputType,
          subtype: inputSubtype,
          index: inputIndex,
          name: name,
          amount: 1,
          checked: true,
        ),
      );
    }

    saveBagToStorage();
    // Notify GetX to update the UI
    bagProduction.refresh();
  }

  Future<void> removeBag(BagProduction removeItem) async {
    var indexRow = bagProduction.indexWhere(
          (item) =>
      item!.subtype == removeItem.subtype &&
          item.type == removeItem.type &&
          item.name == removeItem.name,
    );

    // Check if the item was found
    if (indexRow != -1) {
      // Remove the item at the found index
      bagProduction.removeAt(indexRow);
      countCheckboxFunc();
      print("Removed ${removeItem.name} from the bag.");
    } else {
      // Handle the case where the item does not exist
      print("${removeItem.name} not found in the bag.");
    }

    saveBagToStorage();
    // Notify GetX to update the UI
    bagProduction.refresh();
  }


  void clearBag() async {
    // Remove items from bagProduction where checked is true
    bagProduction.removeWhere((item) => item?.checked == true);

    // Call update to notify GetX to refresh the UI
    update();
    saveBagToStorage();
  }

  Future<bool> plusOne(BagProduction itemProduct) async {
    var sendboolean = false;
    // Find the existing item based on type, subtype, index, and name
    var indexRow = bagProduction.indexWhere(
          (item) =>
      item!.subtype == itemProduct.subtype &&
          item.type == itemProduct.type &&
          item.name == itemProduct.name,
    );

    if (indexRow != -1) {
      // If the item exists, check if stock is available
      var currentAmount = bagProduction[indexRow]!.amount ?? 0;
      var stockAvailable = bagProduction[indexRow]!.stock!; // Assuming stock is in the BagProduction object

      if (currentAmount < stockAvailable) {
        // Increment the amount if stock is available
        bagProduction[indexRow]!.amount = currentAmount + 1;
        print("Updated amount for ${itemProduct.name}: ${bagProduction[indexRow]!.amount}");
        sendboolean = true;
      } else {
        // Show FlutterToast when stock is not available
        Fluttertoast.showToast(
          msg: "ไม่สามารถเพิ่มได้เนื่องจากสินค้ามีจำนวนจำกัด",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
        sendboolean = false;
      }
    } else {
      // If the item does not exist, check if stock is available
      if (itemProduct.stock! > 0) {
        // Add it to the list with an initial amount of 1
        bagProduction.add(
          BagProduction(
            type: itemProduct.type,
            subtype: itemProduct.subtype,
            index: itemProduct.index,
            stock: itemProduct.stock,
            name: itemProduct.name,
            amount: 1, // Start with an amount of 1
            checked: false,
          ),
        );
        sendboolean = true;
        print("Added new item: ${itemProduct.name} with amount: 1");
      } else {
        // Show FlutterToast when there is no stock available
        Fluttertoast.showToast(
          msg: "ไม่สามารถเพิ่มได้เนื่องจากสินค้ามีจำนวนจำกัด",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
        sendboolean = false;
      }
    }

    saveBagToStorage();
    // Notify GetX to update the UI
    bagProduction.refresh();
    return sendboolean;
  }

  Future<void> minusOne(BagProduction itemProduct) async {

    // Find the existing item based on type, subtype, index, and name
    var indexRow = bagProduction.indexWhere(
          (item) =>
      item!.subtype == itemProduct.subtype &&
          item.type == itemProduct.type &&
          item.name == itemProduct.name,
    );

    if (indexRow != -1) {
      // Safely get the current amount, defaulting to 0 if null
      var currentAmount = bagProduction[indexRow]!.amount ?? 0;

      if (currentAmount > 1) {
        // If the amount is greater than 1, decrement it
        bagProduction[indexRow]!.amount = currentAmount - 1;
        print("Updated amount for ${itemProduct.name}: ${bagProduction[indexRow]!.amount}");
      } else if (currentAmount == 1) {

        print(currentAmount);
        // If the amount is 1, remove the item from the list
        // bagProduction.removeAt(indexRow);
        // print("Removed item: ${itemProduct.name} from bagProduction.");
      }
    } else {
      print("Item not found in bagProduction.");
    }

    saveBagToStorage();
    // Notify GetX to update the UI
    bagProduction.refresh();
  }


  /// end bag control

  Future<bool> insertOrder(BuildContext context) async {
    try {
      print("insertOrder");
      AppLoader.loader(context);
      ProvinceController provinceCtrl = Get.find<ProvinceController>();
      var profileCtrl = Get.find<ProfileController>();

      final orderList = [];
      final addressId = await provinceCtrl.seeCheckedIndex();

      double totalShippingCost = 0; // To accumulate the total shipping cost

      // Loop through bagProduction and add items to orderList
      bagProduction.forEach((element) {
        var productInfo = product[element!.type][element.subtype][element.index];

        // Calculate shipping cost for each item
        double shippingCost = calculateShippingCost(element.amount!, productInfo);

        // Add to total shipping cost
        totalShippingCost += shippingCost;

        // Add product details to order list
        orderList.add({
          "product_id": productInfo["parts_number"].toString(),
          "product_code": productInfo["product_name"].toString(),
          "quantity": element.amount.toString(),
          "price": productInfo["sale_price"],
        });
      });

      Map data = {
        "address_id": addressId.toString(),
        "member_id": int.parse(profileCtrl.profile.value.id.toString()),
        "items": orderList,
        "shipping_cost": totalShippingCost, // Use total calculated shipping cost
      };

      print(data);

      // Send the data using the API call
      var response = await AppApi.callAPIjwt(
          "POST",
          "${AppUrl.apiCF}/online-spare-parts/order-spare-parts",
          // "https://7c8a-182-53-201-75.ngrok-free.app/PMS/online-spare-parts/order-spare-parts",
          data
      );

      print(response);

      if (response["status"] == 200 && response["result"]['orderId'] != null) {
        Fluttertoast.showToast(
          msg: "สั่งซื้อสำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0,
        );
        AppLoader.dismiss(context);
        clearBag();
        return true;
      } else {
        AppLoader.dismiss(context);
        Fluttertoast.showToast(
          msg: "สั่งซื้อไม่สำเร็จ",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
        return false;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  double calculateShippingCost(int amount, Map<String, dynamic> productInfo) {
    // Default shipping cost if no rates apply
    double shippingCost = double.tryParse(productInfo["base_shipping_cost"]) ?? 0;

    // Check rate1
    if (amount >= 1 && productInfo["rate1"].isNotEmpty) {
      shippingCost = productInfo["shipping_cost_1"].toDouble();
    }

    // Check rate2
    if (amount >= 16 && productInfo["rate2"].isNotEmpty) {
      shippingCost = productInfo["shipping_cost_2"].toDouble();
    }

    // Check rate3 (if applicable)
    if (productInfo["rate3"].isNotEmpty && amount >= (int.tryParse(productInfo["rate3"].split(" ").first) ?? 0)) {
      shippingCost = productInfo["shipping_cost_3"].toDouble();
    }

    return shippingCost;
  }



  Timer? _debounce;

  Future<bool> searchProduct(String value) async {
    // Cancel the previous search if it's still pending
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    // Create a Completer to manage the asynchronous return of true or false
    Completer<bool> completer = Completer<bool>();

    // Set a new delay of 1.5 seconds
    _debounce = Timer(Duration(milliseconds: 1500), () async {
      if (value != "") {
        try {
          // print("getData");
          Map data = {"value": value};

          // print(data);
          var response = await AppApi.callAPIjwt(
              "POST",
              "https://agilesoftgroup.com/PMS/online-spare-parts/get-spare-parts",
              data);

          // print(response);
          if (response["status"] == 200) {
            searchProductVar = response["result"];
            completer
                .complete(true); // Return true if the search was successful
          } else {
            searchProductVar = {};
            completer.complete(true); // Return false if the search failed
          }

          update();
        } catch (e) {
          // print("error");
          // print(e);
          completer.complete(false); // Return false in case of an error
        }
      } else {
        searchProductVar = {};
        update();
        completer.complete(true); // Return false if the input value is empty
      }
    });

    // Return the future from the completer, which will complete once the Timer block finishes
    return completer.future;
  }

  void dispose() {
    _debounce?.cancel();
  }

  // Function to load data from GetStorage
  void loadBagFromStorage() {
    List<dynamic> storedBag = storage.read('bagProduction') ?? [];
    bagProduction.assignAll(storedBag.map((item) => BagProduction.fromJson(item)).toList());
  }

  // Function to store bagProduction in GetStorage
  void saveBagToStorage() {
    storage.write('bagProduction', bagProduction.map((item) => item?.toJson()).toList());
  }
}
