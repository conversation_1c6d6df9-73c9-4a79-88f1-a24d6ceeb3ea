import 'dart:convert';

import 'dart:io';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';

class SparepartOrderController extends GetxController {

  RxList listOrder = [].obs;
  var showImg = [];
  File? imageFile;
  RxString slipLink = "".obs;

  Future<void> getSparepartOrder () async {
    try{
      print("getSparepartOrder");
      listOrder.clear();
      var profileCtrl = Get.find<ProfileController>();
      Map data = {};

      var response = await AppApi.callAPIjwt("GET", "${AppUrl.apiCF}/online-spare-parts/order-spare-parts-get/${profileCtrl.profile.value.id}", data);

      print(response);

      if(response['status'] == 200){
        // listOrder = response['result']["data"];
        listOrder.assignAll(response['result']["data"]);
        print("listOrder${listOrder.length}");
        // setImgOrder();
        update();
      }

    }catch(e){
      print(e);
    }
  }

  double calculateTotalPriceWithShipping(List<dynamic> items) {
    double totalPrice = items.fold(0.0, (previousValue, element) {
      // Calculate item cost
      double itemCost = double.parse(element['price']!) * (element['quantity'] as num? ?? 0);

      // Return accumulated price
      return previousValue + itemCost;
    });

    return totalPrice;
  }

  double calculateRateShipping(List<dynamic> items) {
    double totalShipping = 0.0;

    for (var element in items) {
      var rates = element['rates']; // Get the rates for the current item
      double shippingCost = 0.0;

      // If rates is not an Iterable, skip this item
      if (rates == null || rates is! Iterable || rates.isEmpty) {
        continue; // Skip this item
      }

      for (var rate in rates) {
        // Ensure rate contains 'threshold' and 'cost' and are valid numbers or convertible to numbers
        var threshold = rate['threshold'];
        var cost = rate['cost'];

        if (threshold == null || cost == null) {
          continue; // Skip if rate is incomplete
        }

        // Convert to num if they are strings
        num? thresholdValue = _convertToNum(threshold);
        num? costValue = _convertToNum(cost);

        if (thresholdValue == null || costValue == null) {
          continue; // Skip if conversion fails
        }

        // Check if quantity is less than or equal to the rate's threshold
        if ((element['quantity'] as num? ?? 0) <= thresholdValue) {
          shippingCost = costValue.toDouble(); // Safely cast to double
          break; // Break when we find the first applicable rate
        }
      }

      // Accumulate the shipping cost
      totalShipping += shippingCost;
    }

    return totalShipping;
  }

// Helper function to convert dynamic values to num
  num? _convertToNum(dynamic value) {
    if (value is num) {
      return value;
    } else if (value is String) {
      return num.tryParse(value); // Try to parse string to num
    }
    return null;
  }



  Future<void> setImgOrder() async {
    showImg.clear();
    showImg = [];
    for(int i = 0; i < listOrder.length; i++) {
      showImg.add([]);
      for(int j = 0; j < listOrder[i]["items"].length; j++) {
        var img = await findImgByOrderId(listOrder[i]["items"][j]["product_id"]);
        showImg[i].add(img);
      }
    }
    update();
  }

  Future<String> findImgByOrderId(orderId) async {
    var ahlaiCtrl = Get.find<SparepartController>();

    // Wait for the future to complete and get the actual product map
    var productData = await ahlaiCtrl.product;

    // Get all keys from the product object
    List<String> keys = productData.keys.toList().cast<String>();

    // Iterate over each key
    for (String key in keys) {
      // Find sub keys
      var subKeys = productData[key].keys.toList().cast<String>();
      for (String subKey in subKeys) {
        // Find the index of the item in the current subKey's array
        int index = productData[key][subKey].indexWhere((item) => item['parts_number'] == orderId);

        // If the index is found, return the key, subKey, and index
        if (index == -1) {

          continue;
        } else {
          // print(ahlaiCtrl.product[key][subKey][index]["upload"].toString());
          return ahlaiCtrl.product[key][subKey][index]["upload"].toString();
        }
      }
    }

    // If no match is found, return empty values
    return "";
  }

  Future<dynamic> pickImage() async {
    final ImagePicker _picker = ImagePicker();

    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    // print(pickedImage.toString());

    imageFile = pickedImage != null ? File(pickedImage.path) : null;
    return updaloadSlip(imageFile);
  }

  Future<dynamic> updaloadSlip(imageFile) async {
    try{
      List<int> imageBytes = imageFile!.readAsBytesSync();
      String base64Image = base64Encode(imageBytes);

      var linkImg = await AppService.uploadPhotoToS3(base64Image, "MappPMS/ESlipImages");

      return linkImg;
    }catch(e){
      print(e);
      return null;
    }
  }

  Future<void> saveLinkSlip(inputLink) async {
    try {
      print("saveLinkSlip");
      slipLink.value = inputLink;
      update();
      print(slipLink);
    } catch (e) {
      print(e);
    }
  }

  Future<void> deleteLinkSlip() async {
    try {
      print("deleteLinkSlip");
      slipLink.value = "";
    } catch (e) {
      print(e);
    }
  }

  Future<bool> confirmOrder(orderId, point_user, exchangeRate, pocketID) async {
    try {
      print("confirmOrder");

      double rate = double.tryParse(exchangeRate) ?? 0.0;

      var order = listOrder.firstWhere(
            (element) => element['order_id'] == orderId,
        orElse: () => null,
      );

      if (order == null) {
        print("Order not found.");
        return false; // Return false if the order is not found
      }

      if (slipLink.value == null || slipLink.value.isEmpty) {
        print("Slip link is missing.");
        return false; // Return false if slip link is missing
      }

      Map data = {
        "order_id": orderId,
        "url_slip": slipLink.value,
        "point_user": point_user,
        "discount_amount": point_user * rate,
        "pocket_id": pocketID,
        "total_amount": calculateTotalPriceWithShipping(order['items']).toString(),
      };

      print(data);

      var response = await AppApi.callAPIjwt(
        "POST",
        "${AppUrl.apiCF}/online-spare-parts/update-payment-slip",
        data,
      );

      print(response);

      if (response['status'] == 200 && response["result"] != null) {
        int index = listOrder.indexWhere((order) => order["order_id"] == orderId);
        if (index != -1) {
          listOrder[index]["slip_payment"] = response["result"]["slip_payment"];
          listOrder[index]["discount_points"] = response["result"]["discount_points"];
          slipLink.value = "";
          update();
        }
        return true; // Return true if the order was successfully confirmed
      }

      return false; // Return false if the status is not 200
    } catch (e) {
      print(e);
      return false; // Return false if an exception occurred
    }
  }

  Future<bool> updateDataSpecific(orderId, input, variable) async {
    try {
      print("updateDataSpecific");

      int index = listOrder.indexWhere((item) => item['order_id'] == orderId);
      print(index);

      if(index == -1) {
        return false;
      }
      listOrder[index][variable] = input;
      update();

      return true;
    } catch (e) {
      print(e);
      return false;
    }
  }
  void updateListOrderWithIndex(int index, dynamic value) {
    try {
      // Check if index is within bounds
      if (index >= 0 && index < listOrder.length) {
        listOrder[index] = value;
      } else {
        print("Index out of bounds");
      }
    } catch (e) {
      print("Error updating list order: $e");
    }
  }

  void cancelOrder(orderId) async {
    try {
      print("cancelOrder");

      var response = await AppApi.callAPIjwt("GET",
          "${AppUrl.apiCF}/online-spare-parts/cancel-payment/$orderId",
          {});
      print(response);
      if(response['status'] == 200) {

        await getSparepartOrder();
        update();
      }
    } catch (e) {
      print(e);
    }
  }
}