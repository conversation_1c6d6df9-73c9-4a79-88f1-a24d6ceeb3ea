import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditAddressController extends GetxController {
  TextEditingController nameController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController zipcodeController = TextEditingController();

  void preInputForEdit (name, address, zipcode) {
    nameController.text = name;
    addressController.text = address;
    zipcodeController.text = zipcode;
    update();
  }
}