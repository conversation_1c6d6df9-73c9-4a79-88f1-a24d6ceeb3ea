import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/sparePart/province_model.dart';



class ProvinceController extends GetxController {
  // final provinceList = List<Province>().obs;
  final isLoading = false.obs;
  var addressId = 0;

  ProvinceList listAddress = ProvinceList();
  var listProvinces = [];
  var listAmphurs = [];
  var listTumbols = [];

  var provinceSelect = "";
  var amphurSelect = "";
  var tumbolSelect = "";
  var zipcodeSelect = "";

  bool useLocation = true;
  List<bool> isSelected = [];

  var showSelectedNOW = "รับสินค้าที่อีซูซุประชากิจฯ";
  RxList shippingAddress = [].obs;

  bool checkInsert = false;

  @override
  void onInit() {
    getProvince();
    super.onInit();
  }

  // Function to get the address and store it in shippingAddress
  Future<void> fetchAddressForOrder(int shippingAddressId, int index) async {
    try {
      // Fetch the new address
      String address = await getAddressForOrder(shippingAddressId);

      // Ensure the list has enough length to accommodate the index
      while (shippingAddress.length <= index) {
        shippingAddress.add(""); // Add empty entries until reaching the desired index
      }

      // Update the specific index with the fetched address
      shippingAddress[index] = address;

    } catch (e) {
      print(e);

      // Handle errors by updating the index with an error message
      if (shippingAddress.length > index) {
        shippingAddress[index] = "เกิดข้อผิดพลาด";
      }
    }
  }

  Future<void> keepSelected (type, value) async {
    if(type == 'province') {
      provinceSelect = value;
      amphurSelect = "";
      tumbolSelect = "";
      zipcodeSelect = "";
      update();
      getAmphur();
    }else if(type == 'amphur') {
      amphurSelect = value;
      tumbolSelect = "";
      zipcodeSelect = "";
      update();
      getTumbol();
    }else if(type == 'tumbol') {
      tumbolSelect = value;
      zipcodeSelect = "";
      update();
      await getZipcode();
    }
  }

  getProvince() async {
    try {
      Map data = {};
      final response = await AppApi.post(AppUrl.getProvinces, data);
      int status = response['status'];
      if (status == 200) {
        List<String> items = [];
        for (var i = 0; i < response['result'].length; i++) {
          var province = response['result'][i]['name_th'];
          items.add(province);
        }

          listProvinces = items;
        update();
      } else {
        AppService.sendError(response.toString(), 'getProvince in profile');
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : getProvince in Profile');
    }
  }

  getAmphur() async {
    try {
      if (provinceSelect != null && provinceSelect != '') {
        Map data = {"province": provinceSelect};
        final response = await AppApi.post(AppUrl.getAmphurs, data);
        int status = response['status'];
        if (status == 200) {
          List<String> items = [];
          for (var i = 0; i < response['result'].length; i++) {
            var amphur = response['result'][i]['name_th'];
            items.add(amphur);
          }

            listAmphurs = items;
          update();
        } else {
          AppService.sendError(response.toString(), 'getAmphur in profile');
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getAmphur in profile');
    }
  }

  getTumbol() async {
    try {
      if (amphurSelect != null && amphurSelect != '') {
        Map data = {"amphur": amphurSelect};
        final response = await AppApi.post(AppUrl.getTumbol, data);
        int status = response['status'];
        if (status == 200) {
          List<String> items = [];
          for (var i = 0; i < response['result'].length; i++) {
            var tumbol = response['result'][i]['name_th'];
            items.add(tumbol);
          }

            listTumbols = items;
          update();
        } else {
          AppService.sendError(response.toString(), 'getTumbol in profile');
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getTumbol in profile');
    }
  }

  Future<void> getZipcode() async {
    try {
      if (tumbolSelect != null &&
          tumbolSelect != '' &&
          amphurSelect != null &&
          amphurSelect != '') {
        Map data = {
          "tambol": tumbolSelect,
          "amphur": amphurSelect,
        };
        final response = await AppApi.post(AppUrl.getZipcode, data);
        int status = response['status'];
        if (status == 200) {
          zipcodeSelect = response['result'][0]['zip_code'].toString();
            // _zipcodeController.text = zipcode;
          update();
        }
      }
    } catch (e) {
      AppService.sendError(e, 'getZipcode in profile');
    }
  }

  void getAddress() async {
    try{
      var profileCtrl = Get.find<ProfileController>();
      ///POST
      // Map data = {
      //   "member_id": profileCtrl.profile.value.id
      // };
      //
      // var response = await AppApi.callAPIjwt("POST", "https://agilesoftgroup.com/PMS/online-spare-parts/address-getall", data);
      //
      // print(response);
      // if(response['status'] == 200) {
      //   print(response['result']);
      // }
      ///GET
      isSelected = [];
      Map data = {};
      var response = await AppApi.callAPIjwt("GET", "https://agilesoftgroup.com/PMS/online-spare-parts/address-getall/${profileCtrl.profile.value.id}", data);

      if(response['status'] == 200) {
        listAddress = ProvinceList.fromJson(response['result']);
         isSelected = List.generate(listAddress.data!.length, (index) {
          if(listAddress.data![index].type_default == 0){
            return false;
          }else{
            useLocation = false;
            return true;
          }
        });
        seeSelectedNOW();

      }else{
        listAddress = ProvinceList(data: []);
      }

      update();
    }catch(e){
      AppService.sendError(e, 'getAddress in profile');
    }
  }

  void checked(index, bool value) async {
    try{
      isSelected.fillRange(0, isSelected.length, false);
      if(index == -1){
        useLocation = true;
        update();
        return;
      }
      isSelected[index] = value;
      if(value){
        useLocation = false;
      }
      update();
    }catch(e){
      AppService.sendError(e, 'checked in profile');
    }
  }

  void switcher(String addressInput, String addressFullname) async {
    try{
      if(!checkInsert){
        saveAddress(addressInput, addressFullname);
      }else{
        updateAddress(addressInput, addressFullname);
      }
      update();
    }catch(e){
      AppService.sendError(e, 'switcher in profile');
    }
  }

  void changeCheckInsert(bool input){
    checkInsert = input;
  }

  void saveAddress(String addressInput, String addressFullname) async {
    try{
      var profileCtrl = await Get.find<ProfileController>();
      Map data = {
        "member_id": profileCtrl.profile.value.id,
        "address_line": addressInput,
        "fullname": addressFullname,
        "province": provinceSelect,
        "district": amphurSelect,
        "sub_district": tumbolSelect,
        "postal_code": zipcodeSelect,
        "country": "Thailand"
      };

      var response = await AppApi.callAPIjwt("POST", "https://agilesoftgroup.com/PMS/online-spare-parts/address-insert", data);

      if(response['status'] == 200) {
        getAddress();
      }

      provinceSelect = "";
      amphurSelect = "";
      tumbolSelect = "";
      zipcodeSelect = "";
      update();

    }catch(e){
      AppService.sendError(e, 'saveAddress in profile');
    }
  }

  void updateAddress(String addressInput, String addressFullname) async {
    try{
      var profileCtrl = await Get.find<ProfileController>();
      Map data = {
        "member_id": profileCtrl.profile.value.id,
        "address_line": addressInput,
        "fullname": addressFullname,
        "province": provinceSelect,
        "district": amphurSelect,
        "sub_district": tumbolSelect,
        "postal_code": zipcodeSelect,
        "country": "Thailand",
        "address_id": addressId
      };

      var response = await AppApi.callAPIjwt("POST", "https://agilesoftgroup.com/PMS/online-spare-parts/address-update", data);

      if(response['status'] == 200) {
        getAddress();
      }

      provinceSelect = "";
      amphurSelect = "";
      tumbolSelect = "";
      zipcodeSelect = "";
      update();

    }catch(e){
      AppService.sendError(e, 'saveAddress in profile');
    }
  }

  void setDefultAddress() async{
    try{
      isLoading(true);
      var profileCtrl = Get.find<ProfileController>();
      final addressId = await seeCheckedIndex();
      Map data = {
        "member_id": profileCtrl.profile.value.id,
        "address_id": addressId
      };

      final response = await AppApi.callAPIjwt("POST", "https://agilesoftgroup.com/PMS/online-spare-parts/address-set-default", data);

      if(response['status'] == 200){
        getAddress();
        isLoading(false);

      }
    }catch(e){
      AppService.sendError(e, 'updateAddress in profile');
    }
  }

  Future<dynamic> seeCheckedIndex() async {
    try{
      int trueIndex = -1;

      for (int i = 0; i < isSelected.length; i++) {
        if (isSelected[i] == true) {
          trueIndex = i;
          break;  // Stop as soon as we find the true value
        }
      }

      if(trueIndex == -1){
        return -1;
      }else{
        return listAddress.data![trueIndex].address_id;
      }
    }catch(e){
      AppService.sendError(e, 'seeCheckedIndex in profile');
    }
  }

  Future<void> seeSelectedNOW() async {
    try{
      int trueIndex = -1;

      for (int i = 0; i < isSelected.length; i++) {
        if (isSelected[i] == true) {
          trueIndex = i;
          break;  // Stop as soon as we find the true value
        }
      }

      if(trueIndex == -1){
        showSelectedNOW = "รับสินค้าที่อีซูซุประชากิจฯ";

      }else{
        showSelectedNOW = listAddress.data![trueIndex].address_line! + " " + listAddress.data![trueIndex].sub_district! + " " + listAddress.data![trueIndex].district! + " " + listAddress.data![trueIndex].province! + " " + listAddress.data![trueIndex].postal_code!;
      }
      update();
    }catch(e){
      showSelectedNOW = "รับสินค้าที่อีซูซุประชากิจฯ";
      update();
    }
  }

  void preInputForEdit(province, amphur, tumbol, zipcode) async {
    provinceSelect = province;
    amphurSelect = amphur;
    tumbolSelect = tumbol;
    zipcodeSelect = zipcode;
    update();
  }

  void changeAddressID(int id) {
    addressId = id;
    update();
  }

  Future<String> getAddressForOrder(int addressID) async {
    try {
      // If addressID is -1, return the default message immediately
      if (addressID == -1) {
        return "รับสินค้าที่อีซูซุประชากิจฯ"; // Default message
      }

      // Check if the listAddress is null or empty to avoid null exceptions
      if (listAddress.data == null || listAddress.data!.isEmpty) {
        return "รับสินค้าที่อีซูซุประชากิจฯ"; // Default message if no addresses available
      }

      // Find the index using addressID
      int index = listAddress.data!.indexWhere(
            (address) => address.address_id == addressID,
      );

      // Check if a valid index was found
      if (index != -1) {
        // Construct the address string
        var showAddress = "${listAddress.data![index].address_line!} ${listAddress.data![index].sub_district!} ${listAddress.data![index].district!} ${listAddress.data![index].province!} ${listAddress.data![index].postal_code!}";

        return showAddress; // Return the constructed address
      } else {
        // Return default message if no address is found
        return "รับสินค้าที่อีซูซุประชากิจฯ";
      } // Return the constructed address
    } catch (e) {
      // Return default message in case of an error
      return "รับสินค้าที่อีซูซุประชากิจฯ";
    }
  }
}