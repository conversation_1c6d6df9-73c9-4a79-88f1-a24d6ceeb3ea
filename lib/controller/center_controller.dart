import 'package:flutter/foundation.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/service_model/claim_history.dart';
import 'package:mapp_prachakij_v3/model/service_model/claim_home.dart';
import 'package:mapp_prachakij_v3/model/service_model/claim_online.dart';
import 'package:mapp_prachakij_v3/model/license.dart';
import 'package:mapp_prachakij_v3/model/member.dart';
import 'package:mapp_prachakij_v3/model/phone_callcenter.dart';
import 'package:mapp_prachakij_v3/model/province.dart';
import 'package:get/get.dart';

class CenterController extends GetConnect {
  static getPhoneCenter(context, String menu) async {
    try{
      if(menu != null){
        PhoneCenter valuePhoneCenter = PhoneCenter.fromJson({
          "menu": menu,
        });
        final responsePhoneCenter = await AppApi.post(AppUrl.getPhoneCenter, valuePhoneCenter.toJson());
        ResponsePhoneCenter resultPhoneCenter = ResponsePhoneCenter.fromJson(responsePhoneCenter);
        return resultPhoneCenter;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error getPhoneCenter');
      }
    }
  }

  static getProfile(context, String id) async {
    try{
      if(id != null){
        Member valueMember = Member.fromJson({
          "id" : id,
        });
        final responseMember = await AppApi.post(AppUrl.getProfileById, valueMember.toJson());
        ResponseMember resultMember = ResponseMember.fromJson(responseMember['result']);
        return resultMember;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error getProfile');
      }
    }
  }

  static getProvince(context) async {
    try{
      Map data = {};
      final responseProvince = await AppApi.post(AppUrl.getProvinces, data);
      ProvinceList resultProvince = ProvinceList.fromJson(responseProvince["result"]);
      return resultProvince;
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error getProvince');
      }
    }
  }

  static addLicense(context, phone, character, number, province) async {
    try {
      if(character != "" &&
        number != "" &&
        province != ""
      ){
        String newCar = "$character $number $province";
        SaveLicense valueSaveLicense = SaveLicense.fromJson({
          "phone": phone,
          "create_user": "MApp_PMS",
          "character": character,
          "number": number,
          "province": province,
          "car_license": newCar,
          "car_engine_no": "",
        });
        final resSaveLicense = await AppApi.post(AppUrl.saveCar, valueSaveLicense.toJson());
        ResponseAddLicense resultSaveLicense = ResponseAddLicense.fromJson(resSaveLicense);
        return resultSaveLicense;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error addLicense');
      }
    }
  }

  static getLicense(context, phone) async {
    try {
      if(phone != ""){
        GetLicense valueGetLicense = GetLicense.fromJson({
          "phone": phone,
        });
        final resGetLicense = await AppApi.post(AppUrl.getCar, valueGetLicense.toJson());
        carLicenseList resultGetLicense = carLicenseList.fromJson(resGetLicense["result"]);
        return resultGetLicense;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error getLicense');
      }
    }
  }

  static createClaimOnline(context, fileOne, fileTwo, fileThree, fileFour, fileFive,
      customerId, sc, car, insurance, phone, line, phoneSystem, displayName
      ) async {
    try{
      if(fileOne != ""){
        ClaimOnline valueClaimOnline = ClaimOnline.fromJson({
          'fileOne': fileOne,
          'fileTwo': fileTwo,
          'fileThree': fileThree,
          'fileFour': fileFour,
          'fileFive': fileFive,
          'customerId': customerId,
          'sc': sc,
          'car': car,
          'insurance': insurance,
          "phone": phone,
          "idLine": line,
          "phoneSystem": phoneSystem,
          "displayName": displayName,
        });
        final responseClaimOnline = await AppApi.post(AppUrl.createClaimOnline, valueClaimOnline.toJson());
        ResponseClaimOnline resultClaimOnline = ResponseClaimOnline.fromJson(responseClaimOnline);
        return resultClaimOnline;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error createClaimOnline');
      }
    }
  }

  static createClaimHome(context, customerName, location, latlng, file, detail,
      customerId, sc, car, insurance, phone, line, phoneSystem
      ) async {
    try{
      if(file != ""){
        ClaimHome valueClaimHome = ClaimHome.fromJson({
          'name_customer': customerName,
          'location_name': location,
          'latlng': latlng,
          'files': file,
          'detail': detail,
          "phone": phone,
          "idLine": line,
          "phoneSystem": phoneSystem,
          'customerId': customerId,
          'car': car,
          'insurance': insurance,
          'sc': sc,
        });
        final responseClaimHome = await AppApi.post(AppUrl.createClaimOnline, valueClaimHome.toJson());
        ResponseClaimHome resultClaimHome = ResponseClaimHome.fromJson(responseClaimHome);
        return resultClaimHome;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error createClaimHome');
      }
    }
  }

  static getClaimHistory(context, phone) async {
    try{
      if(phone != ""){
        ClaimHistory valueClaimHistory = ClaimHistory.fromJson({
          'phone': phone,
        });
        final responseClaimHistory = await AppApi.post(AppUrl.getClaimHistory, valueClaimHistory.toJson());
        ClaimHistoryList resultClaimHistory = ClaimHistoryList.fromJson(responseClaimHistory["result"]);
        return resultClaimHistory;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e ==> on error getClaimHistory');
      }
    }
  }
}