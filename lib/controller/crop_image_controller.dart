import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/connect.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/profile/crop_image.dart';

class CropImageController extends GetxController {

  File? imageCropFile;
  String imageBase64 = "";

  final profileCtl = Get.put(ProfileController());

  editProfile2(image, context) async {
    try {
      File imageFile = File(image);
      if (!imageFile.existsSync()) {
        // ถ้าไฟล์รูปภาพไม่มีอยู่จริงในที่ตั้งที่ระบุ
        if (kDebugMode) {
          print("Image file not found.");
        }
        return;
      }
      imageCropFile = imageFile;
      await AppWidget.showDialogPage(context, const CropImagePage());
    } catch (e) {
      print(e);
    }
  }

  saveCrop(context, ByteData image) async {
    try {
      print('เข้า save crop');
      Uint8List imageBytes = image.buffer.asUint8List();
      String base64Image = base64Encode(imageBytes);
      var imgUrl = await AppService.uploadPhotoToS3(base64Image, "MappPMS/ProfileImages");
      if (imgUrl != false) {
        saveImageProfile(imgUrl, context);
      } else {
        AppAlert.showError(context, 'Upload รูปภาพไม่สำเร็จ', 'ตกลง');
        // AppService.sendError('upload photo to s3 error', 'editProfile in drawer');
      }
    } catch (e) {
      print("Error save crop => $e");
    }
  }


  saveImageProfile(linkImageS3, context) async {
    try {
      if (profileCtl.profile.value.mobile != "") {
        Map data = {
          "database": AppConnect.db,
          "table": "customercenter",
          "field_user_phone": "mobile",
          "field_img": "profile_picture",
          "phone": profileCtl.profile.value.mobile,
          "link_img": linkImageS3,
        };
        final response = await AppApi.post(AppUrl.savePhotoProfile, data);
        int status = response['status'];
        if (status == 200) {
          profileCtl.getProfileEdit();
          AppLoader.dismiss(context);
          await AppService.setPref('bool', 'prefProfileHome', false);
          await AppService.setPref('bool', 'prefProfileDrawer', false);
          AppAlert.showNewAccept(
              context, 'Upload Success', 'เปลี่ยนรูปภาพโปรไฟล์แล้ว', 'ตกลง');
        } else {
          AppLoader.dismiss(context);
          AppAlert.showError(context, 'อัพโหลดรูปภาพไม่สำเร็จ', 'ตกลง');
          AppService.sendError(response['result'].toString(), 'saveImageProfile');
        }
      } else {
        AppLoader.dismiss(context);
        AppAlert.showError(context, 'ไม่มีเบอร์โทรลูกค้า', 'ตกลง');
      }
    } catch (e) {
      AppService.sendError(e, 'ERROR : saveImageProfile in Drawer');
    }
  }
}