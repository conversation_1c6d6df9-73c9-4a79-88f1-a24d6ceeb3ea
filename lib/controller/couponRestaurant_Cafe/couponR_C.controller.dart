import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';


import '../../model/coupon_RC.dart';

class CouponRCController extends GetxController {
  var storage = GetStorage();
  var isLoading = false.obs;
  var isConfirmLoading = false.obs;

  ConponRcList couponList = ConponRcList();
  ConponRcModel coupon = ConponRcModel();

  var profilePhone = "";
  var selectItemIndex = 0;
  RxBool wrongNumber = false.obs;
  RxString storeName = "".obs;

  @override
  Future<void> onInit() async {
    // TODO: implement onInit
    super.onInit();
    await getLocalPhone();
    getCouponRC();
  }

  void selectItem(int index) {
    selectItemIndex = index;
    update();
  }

  Future<bool> confirmCouponUsed(store) async {
    isConfirmLoading(true);
    if (store == 1) {
      storeName.value = "ร้านอาหารอร่อยทุกวัน";
    } else if (store == 2) {
      storeName.value = "ร้าน CU Cafe";
    }
    await Future.delayed(Duration(seconds: 2));
    couponList.data![selectItemIndex].status = "1";
    // couponList.data![selectItemIndex]. = storeName;
    useCoupon(couponList.data![selectItemIndex].running, store);
    isConfirmLoading(false);

    update();
    return true;
  }

  Future<void> getLocalPhone() async {
    var usedPhone = storage.read("phone_profile");
    if (usedPhone != null && usedPhone != "") {
      usedPhone;
    } else {
      final profileCtl = await Get.find<ProfileController>();
      usedPhone = profileCtl.profile.value.mobile;
    }
    profilePhone = usedPhone;
    update();
  }

  Future<void> getCouponRC() async{
    try {
      isLoading(true);
      update();

      couponList = ConponRcList();
      print("getCouponCR");
      Map data = {
        //อย่าลืมเปลี่ยน
        "phone" : profilePhone,
        ///เบอร์ลูกค้า
        // "phone" : "0898098146",
      };
      // print(data);
      var response = await AppApi.callAPIjwt("POST", AppUrl.getCouponRC, data);
      // print('responsegetCouponCR: $response');
      if (response['status'] == 200) {
        // Correctly iterate through the list, even if it has only one item:
        couponList = ConponRcList.fromJson(response['result']);
      } else {
        // print("Response status is not 200: ${response['status']}");
      }

      isLoading(false);
      update();
      } catch (e) {
      print("Exception occurred: $e");
    }
  }

  Future<void> cliamCouponRC(context, inputPhone) async{
    try {
      isLoading(true);
      Map data = {
        "phone_owner": inputPhone,
        "phone_get": profilePhone,
      };
      // print(data);
      var response = await AppApi.callAPIjwt("POST", AppUrl.claimCouponRC, data);
      // print('responseclaimCouponRC: $response');
      // print(response['status']);
      if (response['status'] == 200) {
        // couponList.removeAt(selectItemIndex);
        // print("couponList${couponList}");
        // print(response);


        await getCouponRC();

        isLoading(false);
        Navigator.pop(context);
      } else if(response['status'] == 400){

        isLoading(false);
        Navigator.pop(context);

        AppAlert.showError(context, "เบอร์นี้รับคูปองของวันนี้ไปแล้ว", "ตกลง");
      }else{
        isLoading(false);

        wrongNumber.value = true;
        print("Response status is not 200: ${response}");
      }
      isLoading(false);
      update();
      } catch (e) {
      print("Exception occurred: $e");
    }
  }

  Future<void> useCoupon(running, store) async {
    try {
      // isLoading(true);
      Map data = {
        "running": running,
        "store": store.toString(),
      };
      print(data);

      await AppApi.callAPIjwt("POST", AppUrl.useCouponRC, data);
      getCouponRC();
      // isLoading(false);
      update();
    } catch (e) {
      print("Exception occurred: $e");
    }
  }


}
