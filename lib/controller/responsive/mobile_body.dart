import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mapp_prachakij_v3/component/ai_tutorial.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/tutorial.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/ai_tutorial_controller.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/new_car_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/storage/storage_controller.dart';
import 'package:mapp_prachakij_v3/main.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_news.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/home_promotion.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/mobile/home_mobile.dart';
import 'package:mapp_prachakij_v3/view/profile/profile.dart';
import 'package:mapp_prachakij_v3/view/psi_page/myPSI.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../../view/home/<USER>';
import '../couponRestaurant_Cafe/couponR_C.controller.dart';
import '../setting_controller/lifetime_appointment_controller.dart';
import '../tutorial_controller.dart';

class MobileBodyPage extends StatefulWidget {
  const MobileBodyPage({Key? key}) : super(key: key);

  @override
  State<MobileBodyPage> createState() => _MobileBodyPageState();
}

class _MobileBodyPageState extends State<MobileBodyPage>
    with TickerProviderStateMixin {
  // final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final SecureStorage secureStorage = SecureStorage();
  final chatInAppCtl = Get.put(ChatInAppController());
  final newCarCtl = Get.put(NewCarController());

  //tabbar
  TabController? homeTabController;
  int currentIndex = 0;

  void _handleTabSelection() {
    setState(() {
      currentIndex = homeTabController!.index;
    });
  }

  //tabbar

  bool agreementStatus = true;

  var count = 0.obs;

  final profileCtl = Get.put(ProfileController());
  final loginCtl = Get.put(LoginController());
  final pageCtl = Get.put(PageSelectController());
  final centerCtl = Get.put(SettingController());
  final likePointCtl = Get.put(LikePointController());
  final mrCtl = Get.put(ReferralMRController());
  final storageController = Get.put(StorageController());
  final PsiCtl = Get.put(PsiController());
  final shorebirdCodePush = ShorebirdCodePush();
  final lifetimeAppointment = Get.put(LifetimeAppointmentController());
  final couponRC = Get.put(CouponRCController());
  final tutorialCtl = Get.put(TutorialController());
  final aiTutorialCtl = Get.find<AiTutorialController>();

  var flexTap = 3;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "MobileBody");

    if (profileCtl.lengthTab.value == 5) {
      print("PsiCtl.psiList.isNotEmpty");
      print(PsiCtl.psiList.isNotEmpty);
      flexTap = 5;
    } else {
      flexTap = profileCtl.lengthTab.value;
    }
    homeTabController = TabController(vsync: this, length: flexTap);
    homeTabController!.addListener(_handleTabSelection);
    centerCtl.getVersion(context);
    _checkForUpdates(context);
    PsiCtl.getCouponPSI();
    PsiCtl.getCarOwner();
    tutorialCtl.checkHomeStatus();// เรียกหลังโหลดข้อมูลแล้ว
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   Get.find<AiTutorialController>().startBubbleSequence();
    // });
  }

  Future<void> _checkForUpdates(BuildContext context) async {
    // Check whether a patch is available to install.

    print("_checkForUpdates");

    final updateAvailable =
    await shorebirdCodePush.isNewPatchAvailableForDownload();
    final currentPatch = await shorebirdCodePush.currentPatchNumber();
    print("Current patch number: $currentPatch");
    final isAvailable =
    await shorebirdCodePush.isNewPatchAvailableForDownload();
    print("New patch available: $isAvailable");
    print("updateAvailable");
    print(updateAvailable);
    if (updateAvailable) {
      await shorebirdCodePush.downloadUpdateIfAvailable();
      final isNewPatchReadyToInstall =
      await shorebirdCodePush.isNewPatchReadyToInstall();
      print("isNewPatchReadyToInstall");
      print(isNewPatchReadyToInstall);
      if (isNewPatchReadyToInstall) {
        // Restart.restartApp;
        AppWidget.showDialogPageSlide(context, const AlertUpdatePatchPage());
        // Patch Ready to Install;
      } else {
        //  Patch not Ready to Install ;
      }
    } else {
      // No Update Available;
    }
  }



  bool showTutorial = false;
  int currentTutorialStep = 0;
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (profileCtl.isLoading.value) {
        return AppLoader.loaderWaitPage(context);
      } else {
        return Scaffold(
          // floatingActionButton: FloatingActionButton(
          //   onPressed: (){
          //     aiTutorialCtl.dailyDashboard();
          //   },
          // ),
          extendBodyBehindAppBar: true,
          appBar: currentIndex < 3
              ? centerCtl.closeHead.value == false
              ? AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: Colors.transparent,
            flexibleSpace: buildTopContainer(),
            titleSpacing: 0,
            elevation: 0,
            toolbarHeight: 80,
          )
              : null
              : null,
          body: Stack(
            children: [
              Container(
                width: Get.width,
                height: Get.height,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 1.0],
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                  ),
                ),
              ),
              profileCtl.token.value != null
                  ? buildTabBarView()
                  : buildTabBarViewGuest(),
              profileCtl.token.value != null
                  ? buildFloatingButton()
                  : buildFloatingButtonGuest(),
              // if (!showTutorial)
              //   InkWell(
              //     onTap: () {
              //       setState(() {
              //         currentTutorialStep++; // เพิ่ม step ก่อนหรือหลัง startTutorial แล้วแต่ความต้องการ
              //         // startTutorial(); // เรียกฟังก์ชันเริ่ม tutorial ใหม่
              //       });
              //       print("tap");
              //       print(currentTutorialStep);
              //     },
              //     child: Tutorial.buildTutorialOverlay(context, 3),
              //   )
              ///Ai Tutorial
              AiTutorial()
            ],
          ),
        );
      }
    });
  }

  void startTutorial() {
    setState(() {
      showTutorial = true;
      currentTutorialStep = 0;
    });
  }

  buildTopContainer() {
    return Stack(
      fit: StackFit.loose,
      children: [
        Obx(
              () => pageCtl.scrollParam.value >= 12
              ? ClipRRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: SizedBox(
                width: Get.width,
                height: 180,
              ),
            ),
          )
              : const SizedBox(),
        ),
        Container(
          width: Get.width,
          height: 120,
          margin: const EdgeInsets.only(
            top: 30,
            left: 18,
            right: 18,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 62,
                          child: Image.asset(
                            'assets/image/home/<USER>',
                          ),
                        ),
                        Row(
                          children: [
                            GradientText(
                              'ISUZU',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt-Medium',
                                fontWeight: FontWeight.w700,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: [
                                const Color(0xFF000000).withOpacity(0.7),
                                const Color(0xFF000000),
                              ],
                            ),
                            GradientText(
                              ' PRACHAKIJ',
                              style: const TextStyle(
                                fontSize: 8,
                                fontFamily: 'Prompt',
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.4,
                              ),
                              gradientDirection: GradientDirection.ttb,
                              colors: const [
                                Color(0xFF664701),
                                Color(0xFF1D1400),
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    // TODO :: LIKEWALLET
                    // InkWell(
                    //     hoverColor: Colors.transparent,
                    //     focusColor: Colors.transparent,
                    //     highlightColor: Colors.transparent,
                    //     splashColor: Colors.transparent,
                    //     onTap: (){
                    //       if(profileCtl.token.value != null){
                    //         AppWidget.showDialogPage(context, const MiniLikePage());
                    //       } else {
                    //         AppWidget.showDialogPageSlide(context, const LoginPage());
                    //       }
                    //     },
                    //     child: Row(
                    //       crossAxisAlignment: CrossAxisAlignment.center,
                    //       children: [
                    //         Image.asset('assets/image/home/<USER>', width: 16,),
                    //         Obx(() => profileCtl.token.value != null
                    //             ? likePointCtl.likePoint.value != 0.00
                    //             ? AppWidget.boldTextS(
                    //             context,
                    //             " ${AppService.numberFormat(likePointCtl.likePoint.value)}",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500)
                    //             : AppWidget.boldTextS(
                    //             context,
                    //             " 0",
                    //             11,
                    //             const Color(0xFF895F00),
                    //             FontWeight.w500),),
                    //         AppWidget.boldTextS(
                    //             context,
                    //             " ไลค์",
                    //             10,
                    //             const Color(0xFF2B1710),
                    //             FontWeight.w500),
                    //       ],
                    //     )
                    // )
                    // TODO :: LIKEWALLET
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // TODO :: SCAN QR CODE
                  // profileCtl.token.value != null
                  //     ? InkWell(
                  //     hoverColor: Colors.transparent,
                  //     focusColor: Colors.transparent,
                  //     highlightColor: Colors.transparent,
                  //     splashColor: Colors.transparent,
                  //     onTap: () async {
                  //       var value = await Get.to(() => const GetQrCodePage());
                  //       if(value != null){
                  //         setState(() {
                  //           AppLoader.loader(context);
                  //         });
                  //         var responseEvent = await AppApi.getEventRegisterByID(context, value);
                  //         if(responseEvent["status"] == 200){
                  //           Get.to(() => DetailEventRegisterPage(responseEvent["result"]));
                  //         }
                  //       }
                  //     },
                  //     child: SvgPicture.asset("assets/image/home/<USER>"))
                  //     : Container(),
                  // TODO :: SCAN QR CODE
                  profileCtl.token.value != null
                      ? buildContainerChatInApp()
                      : Container(),
                  const SizedBox(
                    width: 8,
                  ),
                  profileCtl.token.value != null
                      ? buildContainerMR()
                      : InkWell(
                    hoverColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      AppWidget.showDialogPageSlide(
                          context, const LoginPage());
                    },
                    child: Container(
                      width: 110,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF).withOpacity(0.7),
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(25),
                          topLeft: Radius.circular(25),
                          bottomRight: Radius.circular(25),
                          bottomLeft: Radius.circular(25),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          AppWidget.boldTextS(context, "เข้าสู่ระบบ ", 13,
                              const Color(0xFF895F00), FontWeight.w500),
                          Image.asset(
                            'assets/image/home/<USER>',
                            width: 30,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  //Tab bar
  buildTabBarView() {
    return Obx(() => TabBarView(
        physics: const NeverScrollableScrollPhysics(),
        controller: homeTabController,

        /// พัง แก้จาก carOwnerInfo.carList เป็น psiList
        children: PsiCtl.psiList.isNotEmpty
            ? [
          HomeMobile(),
          PSIpage(),
          // lifetimeAppointment.status.value == 200?
          // PSIpage():null,
          HomeNews(),
          HomePromotionPage(),
          NotificationListPage(),
          // ProfilePage(),
        ]
            : [
          HomeMobile(),
          // PSIpage(),
          HomeNews(),
          HomePromotionPage(),
          NotificationListPage(),
          // ProfilePage(),
        ]));
  }

  buildFloatingButton() {
    return Obx(() => Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 20,
        ),
        width: Get.width * 0.9,
        height: 59,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [
              Color(0xFFFFC700),
              Color(0xFFFFB100),
              Color(0xFFFF9900)
            ],
          ),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20.0),
            topLeft: Radius.circular(20.0),
            bottomRight: Radius.circular(20.0),
            bottomLeft: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            /// พัง แก้จาก carOwnerInfo.carList เป็น psiList
            PsiCtl.psiList.isNotEmpty
                ? Expanded(
              flex: 5,
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                tabs: [
                  Tab(
                      child: currentIndex == 0
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                'assets/image/home/<USER>',
                                color: Colors.white,
                                width: 19,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 1
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                'assets/image/home/<USER>',
                                color: Colors.white,
                                width: 19,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 2
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                'assets/image/home/<USER>',
                                color: Colors.white,
                                width: 19,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 3
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white,
                                  width: 19,
                                )),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 4
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white,
                                  width: 19,
                                )),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  // Tab(
                  //     child: currentIndex == 4
                  //         ? SizedBox(
                  //             height: 21,
                  //             width: 21,
                  //             child: Stack(
                  //               children: [
                  //                 Align(
                  //                     alignment: Alignment.bottomLeft,
                  //                     child: SvgPicture.asset(
                  //                       'assets/image/home/<USER>',
                  //                       // color: Colors.white,
                  //                       width: 19,
                  //                     )),
                  //                 Align(
                  //                   alignment: Alignment.topRight,
                  //                   child: Container(
                  //                     width: 8,
                  //                     height: 8,
                  //                     decoration: const BoxDecoration(
                  //                       borderRadius: BorderRadius.only(
                  //                         topLeft: Radius.circular(10),
                  //                         topRight: Radius.circular(10),
                  //                         bottomLeft: Radius.circular(10),
                  //                         bottomRight: Radius.circular(10),
                  //                       ),
                  //                       gradient: LinearGradient(
                  //                         begin: Alignment.topCenter,
                  //                         end: Alignment.bottomCenter,
                  //                         stops: [0, 1.0],
                  //                         colors: [
                  //                           Color(0xFF664701),
                  //                           Color(0xFF1D1400),
                  //                         ],
                  //                       ),
                  //                     ),
                  //                   ),
                  //                 ),
                  //               ],
                  //             ),
                  //           )
                  //         : SvgPicture.asset(
                  //             'assets/image/home/<USER>',
                  //             color: Colors.white.withOpacity(0.6),
                  //             width: 19,
                  //           )),
                ],
              ),
            )
                : Expanded(
              flex: 4,
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                tabs: [
                  Tab(
                      child: currentIndex == 0
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                'assets/image/home/<USER>',
                                color: Colors.white,
                                width: 19,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  // Tab(
                  //     child: currentIndex == 1
                  //         ? SizedBox(
                  //       height: 21,
                  //       width: 21,
                  //       child: Stack(
                  //         children: [
                  //           Align(
                  //             alignment: Alignment.bottomLeft,
                  //             child: SvgPicture.asset(
                  //               'assets/image/home/<USER>',
                  //               color: Colors.white,
                  //               width: 19,
                  //             ),
                  //           ),
                  //           Align(
                  //             alignment: Alignment.topRight,
                  //             child: Container(
                  //               width: 8,
                  //               height: 8,
                  //               decoration: const BoxDecoration(
                  //                 borderRadius: BorderRadius.only(
                  //                   topLeft: Radius.circular(10),
                  //                   topRight: Radius.circular(10),
                  //                   bottomLeft: Radius.circular(10),
                  //                   bottomRight: Radius.circular(10),
                  //                 ),
                  //                 gradient: LinearGradient(
                  //                   begin: Alignment.topCenter,
                  //                   end: Alignment.bottomCenter,
                  //                   stops: [0, 1.0],
                  //                   colors: [
                  //                     Color(0xFF664701),
                  //                     Color(0xFF1D1400),
                  //                   ],
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     )
                  //         : SvgPicture.asset(
                  //       'assets/image/home/<USER>',
                  //       color: Colors.white.withOpacity(0.6),
                  //       width: 19,
                  //     )),
                  Tab(
                      child: currentIndex == 1
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                'assets/image/home/<USER>',
                                color: Colors.white,
                                width: 19,
                              ),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 2
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white,
                                  width: 19,
                                )),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  Tab(
                      child: currentIndex == 3
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white,
                                  width: 19,
                                )),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                  BorderRadius.only(
                                    topLeft:
                                    Radius.circular(10),
                                    topRight:
                                    Radius.circular(10),
                                    bottomLeft:
                                    Radius.circular(10),
                                    bottomRight:
                                    Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                        width: 19,
                      )),
                  // Tab(
                  //     child: currentIndex == 4
                  //         ? SizedBox(
                  //       height: 21,
                  //       width: 21,
                  //       child: Stack(
                  //         children: [
                  //           Align(
                  //               alignment: Alignment.bottomLeft,
                  //               child: SvgPicture.asset(
                  //                 'assets/image/home/<USER>',
                  //                 // color: Colors.white,
                  //                 width: 19,
                  //               )),
                  //           Align(
                  //             alignment: Alignment.topRight,
                  //             child: Container(
                  //               width: 8,
                  //               height: 8,
                  //               decoration: const BoxDecoration(
                  //                 borderRadius: BorderRadius.only(
                  //                   topLeft: Radius.circular(10),
                  //                   topRight: Radius.circular(10),
                  //                   bottomLeft: Radius.circular(10),
                  //                   bottomRight:
                  //                   Radius.circular(10),
                  //                 ),
                  //                 gradient: LinearGradient(
                  //                   begin: Alignment.topCenter,
                  //                   end: Alignment.bottomCenter,
                  //                   stops: [0, 1.0],
                  //                   colors: [
                  //                     Color(0xFF664701),
                  //                     Color(0xFF1D1400),
                  //                   ],
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     )
                  //         : SvgPicture.asset(
                  //       'assets/image/home/<USER>',
                  //       color: Colors.white.withOpacity(0.6),
                  //       width: 19,
                  //     )),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  //Tab bar
  //Tab bar Guest
  buildTabBarViewGuest() {
    return TabBarView(
      controller: homeTabController,
      children: const [
        HomeMobile(),
        HomeNews(),
        HomePromotionPage(),
      ],
    );
  }

  buildFloatingButtonGuest() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 20,
        ),
        width: Get.width * 0.9,
        height: 59,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [Color(0xFFFFC700), Color(0xFFFFB100), Color(0xFFFF9900)],
          ),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20.0),
            topLeft: Radius.circular(20.0),
            bottomRight: Radius.circular(20.0),
            bottomLeft: Radius.circular(20.0),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 5,
              child: TabBar(
                controller: homeTabController,
                indicatorColor: Colors.transparent,
                dividerColor: Colors.transparent,
                indicator: const BoxDecoration(),
                padding: const EdgeInsets.only(left: 50, right: 50),
                tabs: [
                  Tab(
                      child: currentIndex == 0
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                    bottomLeft: Radius.circular(10),
                                    bottomRight: Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                      )),
                  Tab(
                      child: currentIndex == 1
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                              alignment: Alignment.bottomLeft,
                              child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white),
                            ),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                    bottomLeft: Radius.circular(10),
                                    bottomRight: Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                      )),
                  Tab(
                      child: currentIndex == 2
                          ? SizedBox(
                        height: 21,
                        width: 21,
                        child: Stack(
                          children: [
                            Align(
                                alignment: Alignment.bottomLeft,
                                child: SvgPicture.asset(
                                  'assets/image/home/<USER>',
                                  color: Colors.white,
                                )),
                            Align(
                              alignment: Alignment.topRight,
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    topRight: Radius.circular(10),
                                    bottomLeft: Radius.circular(10),
                                    bottomRight: Radius.circular(10),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: [0, 1.0],
                                    colors: [
                                      Color(0xFF664701),
                                      Color(0xFF1D1400),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                          : SvgPicture.asset(
                        'assets/image/home/<USER>',
                        color: Colors.white.withOpacity(0.6),
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //Tab bar Guest

  buildContainerMR() {
    ScrollController profileScrollController = ScrollController();
    if (profileCtl.profile.value.mrCode == null) {
      profileCtl.clearProfile();
      return;
    }
    if (profileCtl.rankMR.rankCurrent == "STANDARD") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
          profileCtl.showProfileTutorial(context, profileScrollController);
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: const BoxDecoration(
            color: Color(0xFFAADEEA),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "STANDARD", 8,
                      const Color(0xFF000000), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFF000000), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                        profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                    as ImageProvider
                        : NetworkImage(
                        "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PLUS") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: const BoxDecoration(
            color: Color(0xFFFFE100),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "PLUS", 8,
                      const Color(0xFF000000), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFF000000), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                        profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                    as ImageProvider
                        : NetworkImage(
                        "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (profileCtl.rankMR.rankCurrent == "PRO") {
      return GestureDetector(
        onTap: () {
          Get.to(() => const ProfilePage());
        },
        child: Container(
          width: 92,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(25),
              topLeft: Radius.circular(25),
              bottomRight: Radius.circular(25),
              bottomLeft: Radius.circular(25),
            ),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.0, 1.0],
              colors: [
                const Color(0xFF000000).withOpacity(0.7),
                const Color(0xFF000000),
              ],
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AppWidget.boldTextS(context, "PRO", 8,
                      const Color(0xFFFFFFFF), FontWeight.w700),
                  AppWidget.boldTextS(context, profileCtl.profile.value.mrCode,
                      8, const Color(0xFFFFFFFF), FontWeight.w500),
                ],
              ),
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: profileCtl.profile.value.profilePicture == null ||
                        profileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                    as ImageProvider
                        : NetworkImage(
                        "${profileCtl.profile.value.profilePicture}"),
                    width: 28,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  buildContainerChatInApp() {
    return InkWell(
      hoverColor: Colors.transparent,
      onTap: () async {
        if (chatInAppCtl.createGroup.isFalse) {
          // await chatInAppCtl.checkRegister(context);
          var ckSubRegister = await chatInAppCtl.firstCheckTG();

          if (ckSubRegister == 'false') {
            await chatInAppCtl.sendOTPTG(context);
          } else if (ckSubRegister == "success") {
            // Get.find<NotifyController>().delNotiChat();
            Get.to(() => const WebViewTelegram());
            return;
          }
        }

        ///
      },
      child: Container(
        width: 92,
        height: 40,
        decoration: ShapeDecoration(
          gradient: const LinearGradient(
            begin: Alignment(0.00, -1.00),
            end: Alignment(0, 1),
            colors: [Color(0xFFFDFDFD), Color(0xFFFCFCFC), Color(0xFFF7F7F7)],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppWidget.boldTextS(context, "พูดคุย", 10, const Color(0xFF895F00),
                FontWeight.w500),
            const SizedBox(
              width: 5,
            ),
            Image.asset(
              'assets/icon/contact_icon.png',
              width: 30,
            ),
          ],
        ),
      ),
    );
  }
}