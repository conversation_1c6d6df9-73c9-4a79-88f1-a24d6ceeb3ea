import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/check_version_controller.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobileBody;
  final Widget tabletBody;

  ResponsiveLayout({
    required this.mobileBody,
    required this.tabletBody,
  });

  final checkVersion = Get.put(CheckVersionAppController());

  @override
  Widget build(BuildContext context){
    return LayoutBuilder(builder: (context, constraints) {
      if (constraints.maxWidth < 500){
        return mobileBody;
      } else {
        return tabletBody;
      }
    }
    );
  }
}