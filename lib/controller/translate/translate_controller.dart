import 'package:get/get.dart';
import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;

class TranslationsService extends Translations {
  static Map<String, Map<String, String>> translations = {};

  @override
  Map<String, Map<String, String>> get keys => translations;

  static Future<void> loadTranslations() async {
    final enJsonString = await rootBundle.loadString('assets/translate/en.json');
    final thJsonString = await rootBundle.loadString('assets/translate/th.json');

    translations = {
      'en_US': Map<String, String>.from(json.decode(enJsonString)),
      'th_TH': Map<String, String>.from(json.decode(thJsonString)),
    };
  }

  ///นำไปใช้ Get.updateLocale(Locale('en', 'US')); Get.updateLocale(Locale('th', 'TH'));
}
