import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/webview_tg_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'dart:io' as io;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import 'package:html/parser.dart';
import 'package:intl/intl.dart';
import 'package:html/dom.dart' as html; // ตั้ง alias สำหรับ package html

class AppService {
  static closeKeyboard(context) {
    FocusScope.of(context).unfocus();
  }

  static getOS() {
    String os = io.Platform.operatingSystem;
    return os;
  }

  static String parseHtmlString(String htmlString) {
    var document = parse(htmlString);
    var body = document.body;

    if (body != null) {
      // แทนที่ <br> ด้วยข้อความบรรทัดใหม่
      body.querySelectorAll('br').forEach((br) {
        br.replaceWith(html.Text('\n')); // ใช้ Text จาก html
      });

      // แทนที่ <p> ด้วยข้อความและเว้นระยะบรรทัดใหม่
      body.querySelectorAll('p').forEach((p) {
        var newText = html.Text('${p.text}');
        p.replaceWith(newText); // ใช้ Text จาก html
      });

      // คืนค่าเฉพาะข้อความ
      return body.text.trim();
    }
    return '';
  }
  // static parseHtmlString(String htmlString) {
  //   var document = parse(htmlString);
  //   String parsedString = parse(document.body!.text).documentElement!.text;
  //   return parsedString;
  // }

  static setPref(type, name, value) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    type == 'int'
        ? await prefs.setInt(name, value)
        : type == 'string'
        ? await prefs.setString(name, value)
        : await prefs.setBool(name, value);
  }

  static getPref(type, name) async {
    var value;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    type == 'int'
        ? value = await prefs.getInt(name)
        : type == 'string'
        ? value = await prefs.getString(name)
        : value = await prefs.getBool(name);

    return value;
  }

  static phoneToPhoneCode(phone) {
    if (phone != null) {
      return '+66' + phone.substring(1);
    } else {
      return '+66000000000';
    }
  }

  static callPhone(phoneNumber) async {
    await launch('tel:' + phoneNumber);
  }

  static launchUrl(url) async {
    await launch(url);
  }

  static String dateThai(timestamp) {
    if (timestamp != null) {
      DateTime myDateTime = timestamp.toDate();
      var year = myDateTime.year + 543;
      var month = myDateTime.month;
      var day = myDateTime.day;

      var formattedDate = DateFormat('dd MMM yyyy hh:mm', 'th').format(myDateTime);
      return formattedDate;
    } else {
      return '';
    }
  }

  static numberFormat(dynamic num) {
    NumberFormat formatter = NumberFormat('#,##0.00');
    return formatter.format(num);
  }

  static numberFormatNon0(dynamic num) {
    NumberFormat formatter = NumberFormat('#,##0');
    return formatter.format(num);
  }

  static String dateEngToThaiMini(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    int day = parsedDate.day;
    int month = parsedDate.month;
    int year = parsedDate.year + 543;

    return "$day ${months_th_mini[month]} $year";
  }

  static String dateEngToThaiMiniPlus11M(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    int day = parsedDate.day;
    int month = parsedDate.month + 11; // เพิ่ม 11 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    return "$day ${months_th_mini[month]} $year";
  }

  static String dateEng2Tha(textName) {
    List<String> monthList = [
      'มกราคม',
      'กุมภาพันธ์',
      'มีนาคม',
      'เมษายน',
      'พฤษภาคม',
      'มิถุนายน',
      'กรกฎาคม',
      'สิงหาคม',
      'กันยายน',
      'ตุลาคม',
      'พฤศจิกายน',
      'ธันวาคม',
    ];

    var dt = textName.split(" ");
    var dateArray = dt[0].split("-");

    int month = int.parse(dateArray[1]);
    int year = int.parse(dateArray[0]);

    return ("${monthList[month - 1]} ${year + 543}").toString();
  }

  static String dateThaiDateTime(textName) {
    if (textName != null) {
      var arr = textName.split("T");
      var time = arr[1].replaceAll("Z", "");
      var timeArr = time.toString().split(":");
      var hour = timeArr[0];
      var min = timeArr[1];
      return '$hour:$min';
    } else {
      return '';
    }
  }

  static String dateThaiDate(textName) {
    if (textName != null) {
      var arr = textName.split("T");
      var dateArr = arr[0].split('-');
      var months_th = [
        "",
        'มกราคม',
        'กุมภาพันธ์',
        'มีนาคม',
        'เมษายน',
        'พฤษภาคม',
        'มิถุนายน',
        'กรกฎาคม',
        'สิงหาคม',
        'กันยายน',
        'ตุลาคม',
        'พฤศจิกายน',
        'ธันวาคม',
      ];
      var year = int.parse(dateArr[0]) + 543;
      var month = months_th[int.parse(dateArr[1])];
      var day = int.parse(dateArr[2]);
      return '$day $month $year';
    } else {
      return '';
    }
  }

  static logout(context) async {
    try {
      final SecureStorage secureStorage = SecureStorage();
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.clear();
      await secureStorage.deleteSecureDataAll();
      await setPref('bool', 'firstUse', false);
      await setPref('bool', 'loginStatus', false);
      try{
        await Get.find<ChatInAppController>().webViewCtrl!.clearCache();
      }catch(e){
        print(e);
      }
      Get.offAll(() => const HomeNavigator());
    } catch (e) {
      print('$e ==> error logout');
    }
  }

  static saveActivity(menu, type, to_name) async {
    try {
      final SecureStorage secureStorage = SecureStorage();
      var userId = await secureStorage.readSecureData("userId");

      Map dataProfile = {"id" : userId};
      final resMember = await AppApi.post(AppUrl.getProfileById, dataProfile);
      int status = resMember['status'];
      if(status == 200){
        var profile = resMember['result'];
        Map dataActivity = {
          "menu": menu,
          "type": type,
          "cust_phone": profile['mobile'],
          "to_name": to_name,
        };
        if (profile['fullname'] == null || profile['fullname'] == '') {
          dataActivity['cust_name'] = profile['displayName'];
        } else {
          dataActivity['cust_name'] = profile['fullname'];
        }

        final response = await AppApi.post(AppUrl.saveActivity, dataActivity);
        return true;
      } else {
        return false;
      }
    } catch (e){
      print("$e ==> error on saveActivity");
    }
  }

  // static getPhoneCenter(menu) async {
  //   try{
  //     String? url = AppUrl.getPhoneCenter;
  //     Map map = {"menu": menu};
  //
  //     final response = await AppApi.post(url, map);
  //     String? phoneNumber;
  //
  //     if (response['status'] == 200) {
  //       if (response['result'].length != 0) {
  //         phoneNumber = response['result'][0]['phone_number'];
  //       }
  //     } else if (response['status'] == 404) {
  //       phoneNumber = 'false';
  //     }
  //
  //     return phoneNumber;
  //   }catch(e){
  //     print('$e ==> getPhoneCenter');
  //   }
  // }

  static sendError(msg, function) async {
    print('on sendError');
    var phone = await getPref('string', 'phone');

    Map map = {
      "msg": msg,
      "from": 'PMS',
      "function": function,
      "phone": phone,
    };
    print(map);
    final response = await AppApi.post(AppUrl.saveLogErrorMApp, map);
    print(response);

    if (response['status'] == 200) {
      print('saveLogErrorMApp success');
    } else if (response['status'] == 204) {
      print('saveLogErrorMApp false');
    } else if (response['status'] == 404) {
      print('saveLogErrorMApp error');
    } else {
      print('saveLogErrorMApp error');
    }
  }

  static RegExp deny() {
    return RegExp("[~`!@#\$\"%฿^&*()_+{}:;'<>?£¢¥=©®/•⁂€™√✓△π\\.|\\,\\<\\>\\[\\]\\\\-]");
  }

  static RegExp denyEmoji() {
    return RegExp(
        r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])'
    );
  }

  static RegExp numberAZThai100RegExp() {
    return RegExp(r'^[a-zA-Z0-9ก-ฮ]{0,100}$');
  }

  static RegExp numberAz() {
    return RegExp(r'[a-zA-Z0-9]');
  }

  static RegExp numberAZThai4RegExp() {
    return RegExp(r'^[[A-z0-9ก-ฮ \t]{0,4}]{0,4}$');
  }

  static RegExp number6RegExp() {
    return RegExp(r'^[[0-9\t]{0,6}]{0,6}$');
  }

  static RegExp numberAZThai3RegExp() {
    return RegExp(r'^[[A-z0-9ก-ฮ \t]{0,3}]{0,3}$');
  }

  static RegExp thaiAZ100RegExp() {
    return RegExp(r'^[[ก-๙A-Za-z0-9 \t]{0,255}]{0,100}$');
  }

  static RegExp thaiAZ() {
    return RegExp(r'^[[ก-๙A-Za-z \t]{0,255}]{0,100}$');
  }

  static RegExp thaiAZ100specialRegExp() {
    return RegExp(r'^[[ก-๙A-Za-z0-9-_ /. \t]{0,255}]{0,100}$');
  }

  static RegExp thaiAZRegExp() {
    return RegExp(r'^[[ก-๙A-Za-z0-9 \t]{0,255}]{0,500}$');
  }

  static RegExp thaiNumber30RegExp() {
    return RegExp(r'^[[ก-๙0-9A-Za-z \t]{0,255}]{0,30}$');
  }

  static RegExp number10RegExp() {
    return RegExp(r'^[0-9]{0,10}$');
  }

  static RegExp number13RegExp() {
    return RegExp(r'^[0-9]{0,13}$');
  }

  static RegExp number15RegExp() {
    return RegExp(r'^[0-9]{0,15}$');
  }

  static RegExp thaiAZRegExp2() {
    return RegExp(r'^[[ก-๙A-Za-z0-9.% \t]{0,255}]{0,500}$');
  }

  static accessMediaLibrary(context) async {
    PermissionStatus status = await Permission.storage.status;
    if (status.isDenied || status.isPermanentlyDenied) {
      await AppAlert.showNewAccept(context, 'ไม่สามารถเข้าถึงไฟล์ได้',
          'กรุณาเปิดการเข้าถึงไฟล์', 'ตกลง');
      openAppSettings();
    }
    if (status.isGranted) {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.storage,
      ].request();
    }
    return status;
  }

  static getImage(imageSource) async {
    final ImagePicker _picker = ImagePicker();
    var image = await _picker.pickImage(source: imageSource);
    return image;
  }

  // static resizePhoto(image, imgWidth) async {
  //   ImageProperties properties =
  //   await FlutterNativeImage.getImageProperties(image.path);
  //   io.File compressedFile = await FlutterNativeImage.compressImage(
  //     image.path,
  //     targetWidth: imgWidth,
  //     targetHeight: (properties.height! * imgWidth / properties.width!).round(),
  //   );
  //
  //   return compressedFile;
  // }

  static uploadPhotoToS3(base64Image, path) async {
    try {
      String url =
          'https://une5sgp9aj.execute-api.ap-southeast-1.amazonaws.com/dev/api/v1/uploadS3_Center';
      Map map = {"name": "MappPMS", "folder": path, "image": base64Image};
      final response = await AppApi.post(url, map);

      if (response["statusCode"] == 200) {
        var imgUrl = response["result"]["url"]["Location"].toString();
        // print("linkImmageS3 : $imgUrl");

        return imgUrl;
      } else {
        print('upload fail!');
        return false;
      }
    } catch (e) {
      print('upload fail!');
      return false;
    }
  }

  static getMonth() {
    var now = new DateTime.now();
    String m = ('${now.month}');

    String? month;

    switch (m) {
      case '1':
        month = 'มกราคม';
        break;
      case '2':
        month = 'กุมภาพันธ์';
        break;
      case '3':
        month = 'มีนาคม';
        break;
      case '4':
        month = 'เมษายน';
        break;
      case '5':
        month = 'พฤษภาคม';
        break;
      case '6':
        month = 'มิถุนายน';
        break;
      case '7':
        month = 'กรกฎาคม';
        break;
      case '8':
        month = 'สิงหาคม';
        break;
      case '9':
        month = 'กันยายน';
        break;
      case '10':
        month = 'ตุลาคม';
        break;
      case '11':
        month = 'พฤศจิกายน';
        break;
      case '12':
        month = 'ธันวาคม';
        break;
      default:
      // code block
    }

    return month;
  }

  static String yearThaiDate(textName) {
    if (textName != null) {
      var arr = textName.split("T");
      var dateArr = arr[0].split('-');
      var year = int.parse(dateArr[0]) + 543;

      return '$year';
    } else {
      return '';
    }
  }

  static String monthThaiDate(textName) {
    if (textName != null) {
      var arr = textName.split("T");
      var dateArr = arr[0].split('-');
      var months_th_mini = [
        "",
        "ม.ค.",
        "ก.พ.",
        "มี.ค.",
        "เม.ย.",
        "พ.ค.",
        "มิ.ย.",
        "ก.ค.",
        "ส.ค.",
        "ก.ย.",
        "ต.ค.",
        "พ.ย.",
        "ธ.ค.",
      ];
      var month = months_th_mini[int.parse(dateArr[1])];
      return '$month';
    } else {
      return '';
    }

  }

  static getPOIReward(context, String phone, var runningAds, String typeAds,
      int runningPoi) async {
    final profileCtl = Get.find<ProfileController>();
    final webLikepointCtl = Get.find<WebViewLikePointController>();
    Map bodyTransaction = {
      "phone": phone,
      "running_ads": runningAds,
      "type_ads": typeAds,
      "running_poi": runningPoi
    };

    final jsonResponse =
    await AppApi.postNormal(AppUrl.insertTransactionPOI, bodyTransaction);
    print(jsonResponse);

    if (jsonResponse["status"] == 200) {
      Map map = {
        "phone": phone,
        "idActivity": runningPoi,
        "firstName": profileCtl.profile.value.firstname,
        "lastName": profileCtl.profile.value.lastname,
        "merchantID": webLikepointCtl.merchantID.value
      };

      // print(map);
      // print(AppUrl.payLikeLockPMSPoint);

      var jsonResponse = await AppApi.callAPIjwt("POST",AppUrl.payLikeLockPMSPoint, map);

      print('after payLikeLock');
      print(jsonResponse);
      AppLoader.dismiss(context);

      if (jsonResponse["status"] == 200) {
        // Get.snackbar(
        //     '',
        //     "รับ likepoint 500 likepoint สำเร็จ",
        //     colorText: Colors.white,
        //     snackPosition: SnackPosition.TOP
        // );

        return true;
      } else {
        Get.snackbar(
            '',
            "รับ PMSpoint ผิดพลาด",
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP
        );
        return false;
      }
    } else {
      Get.snackbar(
          '',
          "บันทึก Transaction ผิดพลาด",
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP
      );
      return false;
    }
  }

  static convertDateTime(date, select) {
    var datetimeSplit = date.split("T");
    var datesplit = datetimeSplit[0].split("-");
    var timesplit = datetimeSplit[1].split(":");
    if (select == "Ddefalt") {
      return datetimeSplit[0];
    } else if (select == "DdefaltPlusOne") {
      return datesplit[2] +
          "-" +
          datesplit[1] +
          "-" +
          (int.parse(datesplit[0]) + 1).toString();
    } else if (select == "DBY") {
      return datesplit[0];
    } else if (select == "DBM") {
      return datesplit[1];
    } else if (select == "DBD") {
      return datesplit[2];
    } else if (select == "DY") {
      return datesplit[2];
    } else if (select == "DM") {
      return datesplit[1];
    } else if (select == "DD") {
      return datesplit[0];
    } else if (select == "D") {
      return datesplit[0] + "/" + datesplit[1] + "/" + datesplit[2];
    } else if (select == "DDB") {
      return datesplit[2] + "-" + datesplit[1] + "-" + datesplit[0];
    } else if (select == "dd-mm-yy") {
      return datesplit[0] +
          "-" +
          datesplit[1] +
          "-" +
          datesplit[2].substring(2, 4);
    } else if (select == "DTDB") {
      return datesplit[2] +
          "-" +
          datesplit[1] +
          "-" +
          datesplit[0] +
          " " +
          datetimeSplit[1];
    } else if (select == "T") {
      return datetimeSplit[1];
    } else if (select == "TfromDB") {
      var timesplitfromDB = datetimeSplit[1].split(".");
      return timesplitfromDB[0];
    } else if (select == "TH") {
      return timesplit[0];
    } else if (select == "TM") {
      return timesplit[1];
    } else if (select == "TS") {
      return timesplit[2];
    } else if (select == "THM") {
      return timesplit[0] + ":" + timesplit[1];
    } else if (select == "DTY") {
      if (datesplit[1].toString() == "01") {
        return datesplit[2] +
            " มกราคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "02") {
        return datesplit[2] +
            " กุมภาพันธ์ " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "03") {
        return datesplit[2] +
            " มีนาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "04") {
        return datesplit[2] +
            " เมษายน " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "05") {
        return datesplit[2] +
            " พฤษภาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "06") {
        return datesplit[2] +
            " มิถุนายน " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "07") {
        return datesplit[2] +
            " กรกฎาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "08") {
        return datesplit[2] +
            " สิงหาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "09") {
        return datesplit[2] +
            " กันยายน " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "10") {
        return datesplit[2] +
            " ตุลาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "11") {
        return datesplit[2] +
            " พฤศจิกายน " +
            (int.parse(datesplit[0]) + 543).toString();
      } else if (datesplit[1].toString() == "12") {
        return datesplit[2] +
            " ธันวาคม " +
            (int.parse(datesplit[0]) + 543).toString();
      } else {
        return "error";
      }
    } else {
      return "select not fond";
    }
  }

}