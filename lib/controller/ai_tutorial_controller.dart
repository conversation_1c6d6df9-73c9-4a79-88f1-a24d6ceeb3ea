import 'dart:convert';
import 'dart:math';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';

class AiTutorialController extends GetxController {
  RxInt currentBubbleIndex = 0.obs;
  final storage = GetStorage();

  RxList<Map<String, dynamic>> dailyDashboardList =
      <Map<String, dynamic>>[].obs;

  final randomAnyItem = <String, dynamic>{}.obs;
  final randomCategory = ''.obs;
  static const String tutorialKey = 'lastShownDate';
  final allItems = <Map<String, dynamic>>[];
  RxBool isLoading = true.obs;

  final List<Map<String, dynamic>> bubbleDataList = [
    {
      "text": "สะสมคะแนน PMSpoint ที่นี่เลย!",
      "offset": 20.0,
      "width": 290.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "แนะนำซื้อรถรับคะแนนทันที\n300,000 PMSP",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
      "top": 700.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "ตรวจสอบคะแนน PMSpoint ของท่านได้ที่นี้",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "อย่าลืม \"โชดดี ทายเลข\" ครั้งถัดไป",
      "offset": 80.0,
      "width": 343.0,
      "arrowPosition": "top", // 👈 อยู่บนกล่อง
      "top": 200.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "เคลือบแก้ววันนี้ แถมฟรี! ตุ๊กตาน้ำหอม",
      "offset": 270.0,
      "width": 325.0,
      "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
      "top": 500.0, // 👈 ตำแหน่งจากบน
    },
    {
      "text": "เคลมประกันรถที่นี่ มีแต่ได้กับได้",
      "offset": 120.0,
      "width": 284.0,
      "arrowPosition": "bottom", // 👈 อยู่ล่างกล่อง
      "top": 620.0, // 👈 ตำแหน่งจากบน
    },
  ];
  final profileCtl = Get.find<ProfileController>();
  final selectedItemDisplayText = ''.obs;
  RxBool showTutorialBox = true.obs;
  RxBool shouldShowTutorialToday = false.obs;
  final box = GetStorage(); // อยู่ใน AiTutorialController ก็ได้

  @override
  void onReady() async {
    super.onReady();
    print('⏱️ onReady called');
    currentBubbleIndex.value = -1; // 💡 กัน UI แสดงหลุด

    // 🔧 รอให้ ProfileController พร้อมก่อน
    await waitForProfileController();

    await startBubbleSequence();
    dailyDashboard();
  }

  // 🔧 ฟังก์ชันรอให้ ProfileController พร้อม
  Future<void> waitForProfileController() async {
    print('🔄 Waiting for ProfileController to be ready...');

    // รอสูงสุด 10 วินาที
    int maxWaitTime = 10000; // 10 วินาที
    int waitInterval = 500; // 0.5 วินาที
    int currentWaitTime = 0;

    while (currentWaitTime < maxWaitTime) {
      try {
        // ตรวจสอบว่า ProfileController พร้อมหรือไม่
        if (profileCtl.token.value != null &&
            profileCtl.profile.value.mobile != null &&
            profileCtl.profile.value.mobile!.isNotEmpty) {
          print('✅ ProfileController is ready!');
          print('📱 Phone: ${profileCtl.profile.value.mobile}');
          print(
              '🔑 Token: ${profileCtl.token.value != null ? "Available" : "Null"}');
          return;
        }

        await Future.delayed(Duration(milliseconds: waitInterval));
        currentWaitTime += waitInterval;
        print('⏳ Still waiting... (${currentWaitTime}ms)');
      } catch (e) {
        print('⚠️ Error while waiting for ProfileController: $e');
        await Future.delayed(Duration(milliseconds: waitInterval));
        currentWaitTime += waitInterval;
      }
    }

    print('⚠️ ProfileController not ready after ${maxWaitTime}ms');
  }

  Future<void> startBubbleSequence() async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final lastShownDate = box.read<String>(tutorialKey);

    final showTutorial = box.read("tutorial_played_home");
    print("📅 Today: $today | Last shown: $lastShownDate");

    if (lastShownDate == today) {
      print("⏩ Tutorial already shown today. Skipping.");
      currentBubbleIndex.value = -1;
      return;
    }

    if (profileCtl.token.value == null) {
      print("⛔️ No token. Skipping tutorial.");
      currentBubbleIndex.value = -1;
      return;
    }

    if (showTutorial == false) {
      print("⛔️ Tutorial not enabled. Skipping.");
      currentBubbleIndex.value = -1;
      return;
    }

    currentBubbleIndex.value = 0;

    for (int i = 0; i < bubbleDataList.length; i++) {
      currentBubbleIndex.value = i;
      await Future.delayed(const Duration(seconds: 10));

      if (currentBubbleIndex.value == -1) {
        print("🛑 Tutorial cancelled by user.");
        return;
      }
    }

    saveShowAiTutorialDate();
    currentBubbleIndex.value = -1;
    print("✅ Finished AI tutorial.");
  }

  // Future<void> startBubbleSequence() async {
  //   for (int i = 0; i < bubbleDataList.length; i++) {
  //     if(profileCtl.token.value !=null){
  //       currentBubbleIndex.value = i;
  //       await Future.delayed(Duration(seconds: 10));
  //     }
  //   }
  //   currentBubbleIndex.value = -1; // ซ่อน bubble
  // }

  bool alreadyPicked = false;

  dailyDashboard() async {
    isLoading.value = true; // เริ่มโหลด
    dailyDashboardList.clear();
    print('📅 Daily Dashboard');

    // 🔧 ตรวจสอบข้อมูลก่อนส่ง API
    if (profileCtl.profile.value.mobile == null ||
        profileCtl.profile.value.mobile!.isEmpty) {
      print(
          '⚠️ Phone number is null or empty. Skipping dailyDashboard API call.');
      isLoading.value = false;
      return;
    }

    final url = Uri.parse(
        'https://n8n-pmsg.agilesoftgroup.com/webhook/PMS/dailydashboard');
    final body = {"phone": profileCtl.profile.value.mobile};

    // print('🌐 Requesting from: $url');
    // print('📦 Body: $body');

    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(body),
      );

      // print('📥 Response status: ${response.body}');
      final data = jsonDecode(response.body);
      final output = data[0]['output'];

      if (output['status'] == 200) {
        print('✅ Data received from n8n:');
        dailyDashboardList.add({"carOwner": output['cars']});
        dailyDashboardList.add({"promotions": output['promotions']});
        dailyDashboardList.add({"appointment": output['appointment']});
        dailyDashboardList.add({"event": output['event']});
        dailyDashboardList.refresh();
        // print('📊 Daily Dashboard data: ${dailyDashboardList.length} items');

        await pickRandomItem();
        update();
      } else {
        print('❌ Failed: ${response.statusCode}');
        print('⚠️ Body: ${response.body}');
      }
    } catch (e) {
      print('⚠️ Error: $e');
    } finally {
      isLoading.value = false; // ✅ โหลดเสร็จแล้ว
    }
  }

  pickRandomItem() {
    allItems.clear();
    for (var item in dailyDashboardList) {
      final itemType = item.keys.first;
      print('📦 Checking item type: $itemType');
      if (item.values.first is List) {
        allItems.addAll(
            (item.values.first as List).map((e) => {'type': itemType, ...e}));
      } else {
        allItems.add({'type': itemType, ...item.values.first});
      }
    }

    if (allItems.isNotEmpty) {
      final selectedItem = allItems[Random().nextInt(allItems.length)];
      // Update the selected item display text dynamically
      print('📦 Selected item type: ${selectedItem}');
      updateSelectedItem(selectedItem);
    } else {
      print('⚠️ No items available to pick randomly!');
      selectedItemDisplayText.value = "ไม่มีข้อมูลที่แสดงได้";
    }
  }

  updateSelectedItem(Map<String, dynamic> item) {
    String displayText;
    switch (item['type']) {
      case 'promotions':
        displayText =
            '${item['promotion_name'] ?? 'ไม่มีข้อมูลโปรโมชั่น'}\n ${item['promotion_date']} - ${item['promotion_end']}';
        break;
      case 'appointment':
        displayText =
            "${item['product'] ?? 'ไม่มีสินค้า'}\n ${item['need_date'] ?? 'ไม่ระบุวันที่'}";
        break;
      case 'event':
        // displayText ="${item['name_activity'] ?? 'ไม่ระบุ'} \nวันที่: ${item['fdate_activity'] ?? 'ไม่ระบุวันที่'} - ${item['edate_activity']}";
        displayText = "${item['name_activity'] ?? 'ไม่ระบุ'}";
        break;
      case 'carOwner':
        displayText =
            "ครบกำหนดเช็กระยะครั้งถัดไป ${item['nextServiceDate'] ?? 'ไม่ระบุวันที่'}\nครบกำหนดพ่นกันสนิมครั้งถัดไป ${item['nextAntiRustDate'] ?? 'ไม่ระบุวันที่'}";
        break;
      default:
        displayText = "ไม่ทราบประเภท: ${item['type']}";
    }
    selectedItemDisplayText.value = displayText;
    print('📦 Updated selected item display text: $displayText');
  }

  saveShowAiTutorialDate() {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    box.write(tutorialKey, today);
  }
}
