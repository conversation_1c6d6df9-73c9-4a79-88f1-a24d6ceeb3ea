import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';

class StorageController extends GetxController {
  final box = GetStorage();

  void saveTime(context){
    DateTime currentTime = DateTime.now();
    box.write('loginTime1', currentTime.toString());
    AppAlert.showNewAccept(context, "${box.read('loginTime1')}", "", "btnText");
  }

  bool isLoginExpired(context) {
    print('check login');
    String loginTimeString = box.read('loginTime1');
    DateTime loginTime = DateTime.parse(loginTimeString);
    DateTime currentTime = DateTime.now();
    Duration difference = currentTime.difference(loginTime);
    AppAlert.showNewAccept(context, "${loginTime}", "${currentTime}", "${difference.inMinutes > 5}");
    return difference.inMinutes > 5;
  }
}