# pms

A new Flutter project.

## Getting Started


This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.


- Flutter 3.22.3


# DEPLOY TO STORE USE SHOREBIRD
## LOGIN SHOREBIRD
1. Login with your Google account "<EMAIL>"
2. command: `shorebird login`
## ANDROID
1. command: `shorebird release android`
2. Your next step is to upload the app bundle to the Play Store:
    - Open the Play Console at https://play.google.com/apps/publish/
    - Click on "Browse files" and select the app bundle at build/app/outputs/bundle/release/app.aab
### Shorebird Patch Android (Check version in google play console)
command: `shorebird patch --platforms=android --release-version=?` follow the version in pubspec.yaml

## iOS
1. Build ipa file command: `shorebird release ios`
2. Drag and drop the build/ios/ipa/*.ipa bundle into the Apple Transporter macOS app (https://apps.apple.com/us/app/transporter/id1450874784).

### Shorebird Patch iOS (Check version in App Store Connect)
command: `shorebird patch --platforms=ios --release-version=?` follow the version in pubspec.yaml
