<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Prachakij</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Prachakij</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseMessagingAutoInitEnabled</key>
	<false/>
	<key>GoogleUtilitiesAppDelegateProxyEnabled</key>
	<true/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>14.0</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to scan QR codes</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Why is my app authenticating using face id?</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location when open.</string>
	<key>NSLocationAlwaysUsageDescription</key>
    <string>Program requires GPS to track cars and job orders</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>Allow access to photos to upload profile photos from your library</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Program requires GPS to track cars and job orders</string>
    <key>NSAppleMusicUsageDescription</key>
    <string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>

     <key>CFBundleURLTypes</key>
     <array>
       <dict>
         <key>CFBundleTypeRole</key>
         <string>Editor</string>

         <key>CFBundleURLSchemes</key>
         <array>
           <string>fb918420556006860</string>
           <string>pms</string>
           <string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
           <string>com.googleusercontent.apps.840143237001-7oepdujcbq3lgjce52703gvbpjrgkav0</string>
         </array>
       </dict>
     </array>

	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>FIREBASE_ANALYTICS_COLLECTION_ENABLED</key>
	<true/>
	<key>GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED</key>
	<true/>
	<key>GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS</key>
	<false/>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<true/>
</dict>
</plist>
