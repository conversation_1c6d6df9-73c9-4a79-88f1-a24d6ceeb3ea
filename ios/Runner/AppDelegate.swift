import UIKit
import Flutter
import GoogleMaps
import Firebase

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    print("Application did finish launching")
    GeneratedPluginRegistrant.register(with: self)
    GMSServices.provideAPIKey("AIzaSyBsjUMeOjWTxNKNewQyBgnlY-vKWQvH7V8")
    print("Google Maps services initialized")
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
