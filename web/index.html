<!DOCTYPE html>
<html>
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Flutter V3 App Prachakij">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="mapp_prachakij_v3">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Prachakij</title>
  <link rel="manifest" href="manifest.json">

  <script>
    if (typeof serviceWorkerVersion !== "undefined") {
      serviceWorkerVersion = 'v1';
    } else {
      window.serviceWorkerVersion = 'v1';
    }
  </script>
  <script src="flutter.js" defer></script>
  <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD7XaKAZMLWd272gWrXbJl_3cpBUeo2fVM&libraries=places&sensor=false"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-messaging.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.6.1/firebase-analytics.js"></script>
  <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/versions/2.20.3/sdk.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>

  <script src="canvaskit.js" async></script>
  <link rel="preload" href="canvaskit.wasm" as="fetch" type="application/wasm" crossorigin="anonymous">

  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      background: transparent;
    }
    #qr-video {
      width: 100%;
      height: calc(100% - 90px); /* เว้นที่สำหรับแถบด้านบน 96px */
      object-fit: cover;
      position: absolute;
      top: 90px; /* เริ่มต้นที่ 96px จากด้านบน */
      left: 0;
      background: transparent;
      z-index: 1;
    }
    #qr-canvas {
      display: none;
    }
    #overlay-canvas {
      position: absolute;
      top: 90px; /* เริ่มต้นที่ 96px จากด้านบน */
      left: 0;
      width: 100%;
      height: calc(100% - 90px); /* ปรับขนาดให้สอดคล้องกับกล้อง */
      z-index: 2;
    }
    #qr-overlay {
      position: absolute;
      top: 90px; /* เริ่มต้นที่ 96px จากด้านบน */
      left: 0;
      width: 100%;
      height: calc(100% - 90px); /* ปรับขนาดให้สอดคล้องกับกล้อง */
      background: transparent;
      pointer-events: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 3;
    }
    #qr-overlay .scan-text {
      color: white;
      font-size: 16px;
      text-align: center;
      font-family: Arial, sans-serif;
      transform: translateY(140px);
    }
    #loading-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 20px;
      font-family: Arial, sans-serif;
    }
  </style>
</head>
<body>
<div id="loading-message">Loading...</div>
<script>
  var scriptLoaded = false;
  function loadMainDartJs() {
    if (scriptLoaded) {
      return;
    }
    scriptLoaded = true;
    var scriptTag = document.createElement('script');
    scriptTag.src = 'main.dart.js';
    scriptTag.type = 'application/javascript';
    scriptTag.onerror = function() {
      document.getElementById('loading-message').innerText = 'Error loading main.dart.js';
    };
    document.body.append(scriptTag);
  }

  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function () {
      var serviceWorkerUrl = 'flutter_service_worker.js?v=' + serviceWorkerVersion;
      navigator.serviceWorker.register(serviceWorkerUrl)
        .then((reg) => {
          function waitForActivation(serviceWorker) {
            serviceWorker.addEventListener('statechange', () => {
              if (serviceWorker.state == 'activated') {
                console.log('Installed new service worker.');
                loadMainDartJs();
              }
            });
          }
          if (!reg.active && (reg.installing || reg.waiting)) {
            waitForActivation(reg.installing ?? reg.waiting);
          } else if (!reg.active.scriptURL.endsWith(serviceWorkerVersion)) {
            console.log('New service worker available.');
            reg.update();
            waitForActivation(reg.installing);
          } else {
            console.log('Loading app from service worker.');
            loadMainDartJs();
          }
        })
        .catch((err) => {
          console.error('Service worker registration failed:', err);
          document.getElementById('loading-message').innerText = 'Service worker error';
          loadMainDartJs();
        });

      setTimeout(() => {
        if (!scriptLoaded) {
          console.warn(
            'Failed to load app from service worker. Falling back to plain <script> tag.',
          );
          loadMainDartJs();
        }
      }, 4000);
    });
  } else {
    loadMainDartJs();
  }

  // รอให้ Flutter โหลดเสร็จ
  window.addEventListener('flutter-first-frame', () => {
    document.getElementById('loading-message').style.display = 'none';
  });

  // QR Code Scanning Logic
  window.flutter_qr_result = window.flutter_qr_result || function(result) {
    console.log("flutter_qr_result not ready, received: ", result);
  };

  window.startQRScanner = async function() {
    const video = document.createElement("video");
    video.id = "qr-video";
    const canvas = document.createElement("canvas");
    canvas.id = "qr-canvas";
    const overlayCanvas = document.createElement("canvas");
    overlayCanvas.id = "overlay-canvas";
    const overlay = document.createElement("div");
    overlay.id = "qr-overlay";
    // เพิ่มข้อความใต้กรอบสแกน
    const scanText = document.createElement("div");
    scanText.className = "scan-text";
    scanText.innerText = "กรุณาวางรหัส QR ไว้ในกรอบ\nเพื่อให้ระบบสแกนรหัส";
    overlay.appendChild(scanText);

    document.body.appendChild(video);
    document.body.appendChild(canvas);
    document.body.appendChild(overlayCanvas);
    document.body.appendChild(overlay);

    // วาดมุมทั้งสี่ที่มีมุมโค้งด้วย Canvas
    function drawOverlay() {
      overlayCanvas.width = window.innerWidth;
      overlayCanvas.height = window.innerHeight - 96; // ปรับขนาดให้สอดคล้องกับกล้อง
      const ctx = overlayCanvas.getContext("2d");
      ctx.strokeStyle = "#F3A700";
      ctx.lineWidth = 2;

      const size = 200; // ขนาดกรอบ 200x200px
      const cornerLength = 40; // ความยาวของเส้นมุม (แนวตั้งและแนวนอน)
      const radius = 10; // ความโค้งของมุม (ปรับขนาดให้เล็กลงจากเดิม 20px เพื่อให้เหมาะกับมุม)

      const centerX = overlayCanvas.width / 2;
      const centerY = (overlayCanvas.height) / 2; // ปรับ centerY ให้อยู่ในกรอบที่เล็กลง
      const halfSize = size / 2;

      const topLeftX = centerX - halfSize;
      const topLeftY = centerY - halfSize;
      const bottomRightX = centerX + halfSize;
      const bottomRightY = centerY + halfSize;

      // มุมบนซ้าย
      ctx.beginPath();
      ctx.moveTo(topLeftX + cornerLength, topLeftY); // เริ่มจากจุดสิ้นสุดของเส้นแนวนอน
      ctx.lineTo(topLeftX + radius, topLeftY); // เส้นแนวนอน
      ctx.arcTo(topLeftX, topLeftY, topLeftX, topLeftY + radius, radius); // มุมโค้ง
      ctx.lineTo(topLeftX, topLeftY + cornerLength); // เส้นแนวตั้ง
      ctx.stroke();

      // มุมบนขวา
      ctx.beginPath();
      ctx.moveTo(bottomRightX - cornerLength, topLeftY); // เริ่มจากจุดเริ่มต้นของเส้นแนวนอน
      ctx.lineTo(bottomRightX - radius, topLeftY); // เส้นแนวนอน
      ctx.arcTo(bottomRightX, topLeftY, bottomRightX, topLeftY + radius, radius); // มุมโค้ง
      ctx.lineTo(bottomRightX, topLeftY + cornerLength); // เส้นแนวตั้ง
      ctx.stroke();

      // มุมล่างขวา
      ctx.beginPath();
      ctx.moveTo(bottomRightX - cornerLength, bottomRightY); // เริ่มจากจุดสิ้นสุดของเส้นแนวนอน
      ctx.lineTo(bottomRightX - radius, bottomRightY); // เส้นแนวนอน
      ctx.arcTo(bottomRightX, bottomRightY, bottomRightX, bottomRightY - radius, radius); // มุมโค้ง
      ctx.lineTo(bottomRightX, bottomRightY - cornerLength); // เส้นแนวตั้ง
      ctx.stroke();

      // มุมล่างซ้าย
      ctx.beginPath();
      ctx.moveTo(topLeftX + cornerLength, bottomRightY); // เริ่มจากจุดสิ้นสุดของเส้นแนวนอน
      ctx.lineTo(topLeftX + radius, bottomRightY); // เส้นแนวนอน
      ctx.arcTo(topLeftX, bottomRightY, topLeftX, bottomRightY - radius, radius); // มุมโค้ง
      ctx.lineTo(topLeftX, bottomRightY - cornerLength); // เส้นแนวตั้ง
      ctx.stroke();
    }

    let stream = null;
    try {
      stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } });
      video.srcObject = stream;
      await video.play();

      drawOverlay(); // วาดมุมทั้งสี่เมื่อเริ่มสแกน

      const scan = async () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext("2d");
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        if (code) {
          console.log("QR Code detected: ", code.data);
          window.flutter_qr_result(code.data);
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
          }
          video.remove();
          canvas.remove();
          overlayCanvas.remove();
          overlay.remove();
          stream = null;
        } else {
          requestAnimationFrame(scan);
        }
      };
      requestAnimationFrame(scan);
    } catch (err) {
      console.error("Error accessing camera: ", err);
      window.flutter_qr_result("Error: " + err.message);
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      video.remove();
      canvas.remove();
      overlayCanvas.remove();
      overlay.remove();
      stream = null;
    }
  };

  window.stopQRScanner = function() {
    const video = document.getElementById("qr-video");
    const canvas = document.getElementById("qr-canvas");
    const overlayCanvas = document.getElementById("overlay-canvas");
    const overlay = document.getElementById("qr-overlay");

    try {
      if (video && video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
      }
    } catch (e) {
      console.error("Error stopping video stream: ", e);
    }

    if (video) video.remove();
    if (canvas) canvas.remove();
    if (overlayCanvas) overlayCanvas.remove();
    if (overlay) overlay.remove();
  };

  // ตรวจสอบว่า ใช้ JavaScript  อาจทำให้  ข้อผิดพลาด
  window.addEventListener('flutter-first-frame', function() {
    // ตรวจสอบว่า ใช้ extension API ไม่
    if (window.chrome && chrome.runtime && chrome.runtime.sendMessage) {
      // ถ้า ใช้ extension API ให้ครอบด้วย try-catch
      window.sendMessageToExtension = function(message) {
        try {
          chrome.runtime.sendMessage(message);
        } catch (e) {
          console.error('Error sending message to extension:', e);
          // ป้องกันไม่ให้ error ทำให้การทำงานของแอป
          return false;
        }
      };
    } else {
      // ถ้าไม่ใช้ extension API ให้สร้างก์จำลอง
      window.sendMessageToExtension = function(message) {
        console.log('Extension API not available, message:', message);
        return false;
      };
    }
  });

  // ใช้งาน extension API 3้งหมดเพื่อหลีกเลี่ยงข้อ พลาด
  window.addEventListener('load', function() {
    // Mock extensionAdapter เsemicolonป้อง ข้อ พลาด
    window.extensionAdapter = {
      sendMessageToTab: function() {
        console.log('Extension adapter disabled to prevent errors');
        return false;
      },
      handleMessage: function() {
        console.log('Extension adapter disabled to prevent errors');
        return false;
      }
    };
    
    // Mock chrome.runtime เsemicolonป้อง ข้อ พลาด
    if (!window.chrome) {
      window.chrome = {};
    }
    if (!window.chrome.runtime) {
      window.chrome.runtime = {
        sendMessage: function() {
          console.log('Chrome runtime sendMessage disabled to prevent errors');
          return false;
        }
      };
    }
  });
</script>
</body>
</html>
